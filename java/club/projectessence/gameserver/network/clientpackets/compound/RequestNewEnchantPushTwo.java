/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.compound;

import club.projectessence.gameserver.data.xml.CombinationItemsData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.request.CompoundRequest;
import club.projectessence.gameserver.model.items.combination.CombinationItem;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.variables.ItemVariables;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.compound.ExEnchantOneFail;
import club.projectessence.gameserver.network.serverpackets.compound.ExEnchantTwoFail;
import club.projectessence.gameserver.network.serverpackets.compound.ExEnchantTwoOK;

/**
 * <AUTHOR>
 */
public class RequestNewEnchantPushTwo extends ClientPacket {
	private int _objectId;

	@Override
	public void readImpl() {
		_objectId = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		} else if (player.isInStoreMode()) {
			client.sendPacket(SystemMessageId.YOU_CANNOT_DO_THAT_WHILE_IN_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP);
			client.sendPacket(ExEnchantOneFail.STATIC_PACKET);
			return;
		} else if (player.isProcessingTransaction() || player.isProcessingRequest()) {
			client.sendPacket(SystemMessageId.YOU_CANNOT_USE_THIS_SYSTEM_DURING_TRADING_PRIVATE_STORE_AND_WORKSHOP_SETUP);
			client.sendPacket(ExEnchantOneFail.STATIC_PACKET);
			return;
		}

		final CompoundRequest request = player.getRequest(CompoundRequest.class);
		if ((request == null) || request.isProcessing()) {
			client.sendPacket(ExEnchantTwoFail.STATIC_PACKET);
			return;
		}

		// Make sure player owns this item.
		request.setItemTwo(_objectId);
		final ItemInstance itemOne = request.getItemOne();
		final ItemInstance itemTwo = request.getItemTwo();
		if ((itemOne == null) || (itemTwo == null) || itemTwo.getVariables().getBoolean(ItemVariables.ENCHANT_DISABLED, false)) {
			client.sendPacket(ExEnchantTwoFail.STATIC_PACKET);
			return;
		}

		// Lets prevent using same item twice. Also stackable item check.
		if ((itemOne.getObjectId() == itemTwo.getObjectId()) && (!itemOne.isStackable() || (player.getInventory().getInventoryItemCount(itemOne.getItem().getId(), -1) < 2))) {
			client.sendPacket(ExEnchantTwoFail.STATIC_PACKET);
			return;
		}

		final CombinationItem combinationItem = CombinationItemsData.getInstance().getItemsBySlots(itemOne.getId(), itemTwo.getId(), itemOne.getEnchantLevel(), itemTwo.getEnchantLevel());

		// Not implemented or not able to merge!
		if (combinationItem == null) {
			client.sendPacket(ExEnchantTwoFail.STATIC_PACKET);
			return;
		}

		client.sendPacket(ExEnchantTwoOK.STATIC_PACKET);
	}
}
