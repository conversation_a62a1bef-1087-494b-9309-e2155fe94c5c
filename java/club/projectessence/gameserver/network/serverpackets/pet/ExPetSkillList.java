package club.projectessence.gameserver.network.serverpackets.pet;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.AbstractItemPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Collection;
import java.util.List;

public class ExPetSkillList extends AbstractItemPacket {
	// @formatter:off
	public static List<Integer> DISPLAYED_PASSIVES =
			List.of(
					49001, // Pet Natural Ability: Kookaburra Chick
					49011, // Pet Natural Ability: Tiger Cub
					49021, // Pet Natural Ability: Wolf Cub
					49031, // Pet Natural Ability: Buffalo Calf
					49041, // Pet Natural Ability: Hawk Hatchling
					49051, // Pet Natural Ability: Dragon Hatchling
					49201, // STR
					49202, // DEX
					49203, // CON
					49204, // INT
					49205, // WIT
					49206, // MEN
					49207, // Steel Skin
					49208, // Physical Training
					49027, // Sound Body
					49058, // Perfect Bite	
					49015, // Sharp Senses
					49039, // Pulling Defense
					49007, // Magic Flare
					49049 // Swift Feather
			);
	private final Collection<Skill> _skills;
	private final boolean _first;
	// @formatter:on

	public ExPetSkillList(Collection<Skill> skills, boolean first) {
		_skills = skills;
		_skills.removeIf(s -> s.isPassive() && !DISPLAYED_PASSIVES.contains(s.getId()));
		_first = first;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PET_SKILL_LIST, buffer);
		buffer.writeByte(_first ? 0x01 : 0x00);
		buffer.writeInt(_skills.size());
		for (Skill skill : _skills) {
			buffer.writeInt(skill.getDisplayId());
			buffer.writeInt(skill.getDisplayLevel());
			buffer.writeInt(skill.getReuseDelayGroup());
			buffer.writeByte(0); // skill enchant
			buffer.writeByte(skill.isPassive() ? 0x01 : 0x00);
		}
	}
}
