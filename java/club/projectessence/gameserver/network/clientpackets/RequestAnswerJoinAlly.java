/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.network.SystemMessageId;

public class RequestAnswerJoinAlly extends ClientPacket
{
	private int _response;
	
	@Override
	public void readImpl()
	{
		_response = readInt();
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		final PlayerInstance requestor = player.getRequest().getPartner();
		if (requestor == null)
		{
			return;
		}
		if (_response == 0)
		{
			player.sendPacket(SystemMessageId.NO_RESPONSE_YOUR_ENTRANCE_TO_THE_ALLIANCE_HAS_BEEN_CANCELLED);
			requestor.sendPacket(SystemMessageId.NO_RESPONSE_INVITATION_TO_JOIN_AN_ALLIANCE_HAS_BEEN_CANCELLED);
		}
		else
		{
			if (!(requestor.getRequest().getRequestPacket() instanceof RequestJoinAlly))
			{
				return; // hax
			}
			final Clan clan = requestor.getClan();
			// we must double check this cause of hack
			if (clan.checkAllyJoinCondition(requestor, player))
			{
				// Kiểm tra bổ sung Faction trước khi cập nhật thông tin liên minh
				if (clan.getFaction() != player.getClan().getFaction())
				{
					player.sendMessage("The alliance cannot be joined because the faction (" + player.getClan().getFaction() + ") does not match the inviting clan's faction (" + clan.getFaction() + ").");
					requestor.sendMessage(player.getClan().getName() + "'s faction (" + player.getClan().getFaction() + ") does not match your clan's faction (" + clan.getFaction() + "). Alliance join request rejected.");
					return;
				}
				// TODO: Need correct message id
				requestor.sendPacket(SystemMessageId.THAT_PERSON_HAS_BEEN_SUCCESSFULLY_ADDED_TO_YOUR_FRIEND_LIST);
				player.sendPacket(SystemMessageId.YOU_HAVE_ACCEPTED_THE_ALLIANCE);
				player.getClan().setAllyId(clan.getAllyId());
				player.getClan().setAllyName(clan.getAllyName());
				player.getClan().setAllyPenaltyExpiryTime(0, 0);
				player.getClan().changeAllyCrest(clan.getAllyCrestId(), true);
				player.getClan().updateClanInDB();
			}
		}
		player.getRequest().onRequestResponse();
	}
}
