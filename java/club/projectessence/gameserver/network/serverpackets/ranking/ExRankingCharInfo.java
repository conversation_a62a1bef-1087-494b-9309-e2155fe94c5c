/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.ranking;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class ExRankingCharInfo extends ServerPacket {
	private final PlayerInstance _player;

	public ExRankingCharInfo(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RANKING_CHAR_INFO, buffer);

		buffer.writeInt(RankManager.getInstance().getPlayerVisualRank(_player.getObjectId())); // server rank
		buffer.writeInt(RankManager.getInstance().getPlayerVisualRaceRank(_player.getObjectId())); // race rank
		Map<Integer, StatSet> previousDays = RankManager.getInstance().getRankingStatistics(_player.getObjectId());
		if ((previousDays != null) && !previousDays.isEmpty()) {
			StatSet previousDay = previousDays.values().stream().reduce((prev, next) -> next).orElse(null);
			buffer.writeInt(previousDay == null ? 0x00 : previousDay.getInt("server_rank")); // previous server rank
			buffer.writeInt(previousDay == null ? 0x00 : previousDay.getInt("race_rank")); // previous race rank
		} else {
			buffer.writeInt(0); // previous server rank
			buffer.writeInt(0); // previous race rank
		}
		buffer.writeInt(0); // nClassRank
		buffer.writeInt(0); // nClassRank_Snapshot snapshot
	}
}
