/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.olympiad;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.BuffInfo;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ExOlympiadSpelledInfo extends ServerPacket {
	private final int _playerId;
	private final List<BuffInfo> _effects = new ArrayList<>();
	private final List<Skill> _effects2 = new ArrayList<>();

	public ExOlympiadSpelledInfo(PlayerInstance player) {
		_playerId = player.getObjectId();
	}

	public void addSkill(BuffInfo info) {
		_effects.add(info);
	}

	public void addSkill(Skill skill) {
		_effects2.add(skill);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_OLYMPIAD_SPELLED_INFO, buffer);

		buffer.writeInt(_playerId);
		buffer.writeInt(_effects.size() + _effects2.size());
		for (BuffInfo info : _effects) {
			if ((info != null) && info.isInUse()) {
				buffer.writeInt(info.getSkill().getDisplayId());
				buffer.writeShort(info.getSkill().getDisplayLevel());
				// buffer.writeShort(0x00); // Sub level
				buffer.writeInt(info.getSkill().getAbnormalType().getClientId());
				writeOptionalD(info.getSkill().isAura() ? -1 : info.getTime(), buffer);
			}
		}
		for (Skill skill : _effects2) {
			if (skill != null) {
				buffer.writeInt(skill.getDisplayId());
				buffer.writeShort(skill.getDisplayLevel());
				// buffer.writeShort(0x00); // Sub level
				buffer.writeInt(skill.getAbnormalType().getClientId());
				buffer.writeShort(-1);
			}
		}
	}
}
