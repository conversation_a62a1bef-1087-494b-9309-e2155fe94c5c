/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.itemcontainer;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.database.DatabaseFactory;
import club.projectessence.commons.util.CommonUtil;
import club.projectessence.gameserver.cache.PaperdollCache;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.AppearanceItemData;
import club.projectessence.gameserver.data.xml.ArmorSetData;
import club.projectessence.gameserver.data.xml.ClientWeaponTypesData;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.enums.ItemSkillType;
import club.projectessence.gameserver.enums.PrivateStoreType;
import club.projectessence.gameserver.enums.Race;
import club.projectessence.gameserver.instancemanager.GlobalVariables;
import club.projectessence.gameserver.model.ArmorSet;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.VariationInstance;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerItemUnequip;
import club.projectessence.gameserver.model.holders.ArmorsetSkillHolder;
import club.projectessence.gameserver.model.holders.ItemSkillHolder;
import club.projectessence.gameserver.model.items.Armor;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.Weapon;
import club.projectessence.gameserver.model.items.appearance.AppearanceStone;
import club.projectessence.gameserver.model.items.appearance.AppearanceType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.items.type.ArmorType;
import club.projectessence.gameserver.model.items.type.EtcItemType;
import club.projectessence.gameserver.model.items.type.WeaponType;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.SkillConditionScope;
import club.projectessence.gameserver.network.serverpackets.ExUserInfoEquipSlot;
import club.projectessence.gameserver.network.serverpackets.SkillCoolTime;
import club.projectessence.gameserver.network.serverpackets.pet.PetItemList;

/**
 * This class manages inventory
 *
 * @version $Revision: ********.2.12 $ $Date: 2005/03/29 23:15:15 $ rewritten 23.2.2006 by Advi
 */
public abstract class Inventory extends ItemContainer
{
	// Common Items
	public static final int					ADENA_ID				= 57;
	public final static int					LCOIN_ID				= 91663;
	public final static int					ELIXIR_ID				= 94314;
	public static final int					ANCIENT_ADENA_ID		= 5575;
	public static final int					BEAUTY_TICKET_ID		= 36308;
	public static final int					AIR_STONE_ID			= 39461;
	public static final int					TEMPEST_STONE_ID		= 39592;
	public static final int					ELCYUM_CRYSTAL_ID		= 36514;
	public static final int					PREMIUM_SAYHA_CUBE_ID	= 100001;
	public static final int					GIRAN_SEALED_ID			= 92314;
	public static final long				MAX_ADENA				= Config.MAX_ADENA;
	public static final int					PAPERDOLL_UNDER			= 0;
	public static final int					PAPERDOLL_HEAD			= 1;
	public static final int					PAPERDOLL_HAIR			= 2;
	public static final int					PAPERDOLL_HAIR2			= 3;
	public static final int					PAPERDOLL_NECK			= 4;
	public static final int					PAPERDOLL_RHAND			= 5;
	public static final int					PAPERDOLL_CHEST			= 6;
	public static final int					PAPERDOLL_LHAND			= 7;
	public static final int					PAPERDOLL_REAR			= 8;
	public static final int					PAPERDOLL_LEAR			= 9;
	public static final int					PAPERDOLL_GLOVES		= 10;
	public static final int					PAPERDOLL_LEGS			= 11;
	public static final int					PAPERDOLL_FEET			= 12;
	public static final int					PAPERDOLL_RFINGER		= 13;
	public static final int					PAPERDOLL_LFINGER		= 14;
	public static final int					PAPERDOLL_LBRACELET		= 15;
	public static final int					PAPERDOLL_RBRACELET		= 16;
	public static final int					PAPERDOLL_AGATHION1		= 17;
	public static final int					PAPERDOLL_AGATHION2		= 18;
	public static final int					PAPERDOLL_AGATHION3		= 19;
	public static final int					PAPERDOLL_AGATHION4		= 20;
	public static final int					PAPERDOLL_AGATHION5		= 21;
	public static final int					PAPERDOLL_DECO1			= 22;
	public static final int					PAPERDOLL_DECO2			= 23;
	public static final int					PAPERDOLL_DECO3			= 24;
	public static final int					PAPERDOLL_DECO4			= 25;
	public static final int					PAPERDOLL_DECO5			= 26;
	public static final int					PAPERDOLL_DECO6			= 27;
	public static final int					PAPERDOLL_CLOAK			= 28;
	public static final int					PAPERDOLL_BELT			= 29;
	public static final int					PAPERDOLL_BROOCH		= 30;
	public static final int					PAPERDOLL_BROOCH_JEWEL1	= 31;
	public static final int					PAPERDOLL_BROOCH_JEWEL2	= 32;
	public static final int					PAPERDOLL_BROOCH_JEWEL3	= 33;
	public static final int					PAPERDOLL_BROOCH_JEWEL4	= 34;
	public static final int					PAPERDOLL_BROOCH_JEWEL5	= 35;
	public static final int					PAPERDOLL_BROOCH_JEWEL6	= 36;
	public static final int					PAPERDOLL_ARTIFACT_BOOK	= 37;
	public static final int					PAPERDOLL_ARTIFACT1		= 38;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT2		= 39;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT3		= 40;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT4		= 41;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT5		= 42;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT6		= 43;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT7		= 44;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT8		= 45;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT9		= 46;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT10	= 47;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT11	= 48;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT12	= 49;											// Artifact Balance
	public static final int					PAPERDOLL_ARTIFACT13	= 50;											// Artifact Spirit
	public static final int					PAPERDOLL_ARTIFACT14	= 51;											// Artifact Spirit
	public static final int					PAPERDOLL_ARTIFACT15	= 52;											// Artifact Spirit
	public static final int					PAPERDOLL_ARTIFACT16	= 53;											// Artifact Protection
	public static final int					PAPERDOLL_ARTIFACT17	= 54;											// Artifact Protection
	public static final int					PAPERDOLL_ARTIFACT18	= 55;											// Artifact Protection
	public static final int					PAPERDOLL_ARTIFACT19	= 56;											// Artifact Support
	public static final int					PAPERDOLL_ARTIFACT20	= 57;											// Artifact Support
	public static final int					PAPERDOLL_ARTIFACT21	= 58;											// Artifact Support
	public static final int					PAPERDOLL_TOTALSLOTS	= 59;
	// Speed percentage mods
	public static final double				MAX_ARMOR_WEIGHT		= 12000;
	protected static final Logger			LOGGER					= Logger.getLogger(Inventory.class.getName());
	private final ItemInstance[]			_paperdoll;
	private final List<PaperdollListener>	_paperdollListeners;
	private final PaperdollCache			_paperdollCache			= new PaperdollCache();
	// protected to be accessed from child classes only
	protected int							_totalWeight;
	// used to quickly check for using of items of special type
	private int								_wearedMask;
	private int								_blockedItemSlotsMask;
	
	/**
	 * Constructor of the inventory
	 */
	protected Inventory()
	{
		_paperdoll = new ItemInstance[PAPERDOLL_TOTALSLOTS];
		_paperdollListeners = new ArrayList<>();
		if ((this instanceof PlayerInventory) || (this instanceof PetInventory))
		{
			addPaperdollListener(ArmorSetListener.getInstance());
			addPaperdollListener(BowCrossRodListener.getInstance());
			addPaperdollListener(ItemSkillsListener.getInstance());
			addPaperdollListener(BraceletListener.getInstance());
			addPaperdollListener(BroochListener.getInstance());
			addPaperdollListener(AgathionBraceletListener.getInstance());
			addPaperdollListener(ArtifactBookListener.getInstance());
			addPaperdollListener(GveRewardListener.getInstance());
		}
		// common
		addPaperdollListener(StatsListener.getInstance());
	}
	
	public static int getPaperdollIndex(long slot)
	{
		if (slot == Item.BodyPart.SLOT_UNDERWEAR.getSlot())
		{
			return PAPERDOLL_UNDER;
		}
		else if (slot == Item.BodyPart.SLOT_R_EAR.getSlot())
		{
			return PAPERDOLL_REAR;
		}
		else if ((slot == Item.BodyPart.SLOT_LR_EAR.getSlot()) || (slot == Item.BodyPart.SLOT_L_EAR.getSlot()))
		{
			return PAPERDOLL_LEAR;
		}
		else if (slot == Item.BodyPart.SLOT_NECK.getSlot())
		{
			return PAPERDOLL_NECK;
		}
		else if ((slot == Item.BodyPart.SLOT_R_FINGER.getSlot()) || (slot == Item.BodyPart.SLOT_LR_FINGER.getSlot()))
		{
			return PAPERDOLL_RFINGER;
		}
		else if (slot == Item.BodyPart.SLOT_L_FINGER.getSlot())
		{
			return PAPERDOLL_LFINGER;
		}
		else if (slot == Item.BodyPart.SLOT_HEAD.getSlot())
		{
			return PAPERDOLL_HEAD;
		}
		else if ((slot == Item.BodyPart.SLOT_R_HAND.getSlot()) || (slot == Item.BodyPart.SLOT_LR_HAND.getSlot()))
		{
			return PAPERDOLL_RHAND;
		}
		else if (slot == Item.BodyPart.SLOT_L_HAND.getSlot())
		{
			return PAPERDOLL_LHAND;
		}
		else if (slot == Item.BodyPart.SLOT_GLOVES.getSlot())
		{
			return PAPERDOLL_GLOVES;
		}
		else if ((slot == Item.BodyPart.SLOT_CHEST.getSlot()) || (slot == Item.BodyPart.SLOT_FULL_ARMOR.getSlot()) || (slot == Item.BodyPart.SLOT_ALLDRESS.getSlot()))
		{
			return PAPERDOLL_CHEST;
		}
		else if (slot == Item.BodyPart.SLOT_LEGS.getSlot())
		{
			return PAPERDOLL_LEGS;
		}
		else if (slot == Item.BodyPart.SLOT_FEET.getSlot())
		{
			return PAPERDOLL_FEET;
		}
		else if (slot == Item.BodyPart.SLOT_BACK.getSlot())
		{
			return PAPERDOLL_CLOAK;
		}
		else if ((slot == Item.BodyPart.SLOT_HAIR.getSlot()) || (slot == Item.BodyPart.SLOT_HAIRALL.getSlot()))
		{
			return PAPERDOLL_HAIR;
		}
		else if (slot == Item.BodyPart.SLOT_HAIR2.getSlot())
		{
			return PAPERDOLL_HAIR2;
		}
		else if (slot == Item.BodyPart.SLOT_R_BRACELET.getSlot())
		{
			return PAPERDOLL_RBRACELET;
		}
		else if (slot == Item.BodyPart.SLOT_L_BRACELET.getSlot())
		{
			return PAPERDOLL_LBRACELET;
		}
		else if (slot == Item.BodyPart.SLOT_DECO.getSlot())
		{
			return PAPERDOLL_DECO1; // return first we deal with it later
		}
		else if (slot == Item.BodyPart.SLOT_BELT.getSlot())
		{
			return PAPERDOLL_BELT;
		}
		else if (slot == Item.BodyPart.SLOT_BROOCH.getSlot())
		{
			return PAPERDOLL_BROOCH;
		}
		else if (slot == Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot())
		{
			return PAPERDOLL_BROOCH_JEWEL1;
		}
		else if (slot == Item.BodyPart.SLOT_AGATHION.getSlot())
		{
			return PAPERDOLL_AGATHION1;
		}
		else if (slot == Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot())
		{
			return PAPERDOLL_ARTIFACT_BOOK;
		}
		else if (slot == Item.BodyPart.SLOT_ARTIFACT.getSlot())
		{
			return PAPERDOLL_ARTIFACT1;
		}
		return -1;
	}
	
	protected abstract ItemLocation getEquipLocation();
	
	/**
	 * Returns the instance of new ChangeRecorder
	 *
	 * @return ChangeRecorder
	 */
	private ChangeRecorder newRecorder()
	{
		return new ChangeRecorder(this);
	}
	
	/**
	 * Drop item from inventory and updates database
	 *
	 * @param process
	 *            : String Identifier of process triggering this action
	 * @param item
	 *            : ItemInstance to be dropped
	 * @param actor
	 *            : PlayerInstance Player requesting the item drop
	 * @param reference
	 *            : Object Object referencing current action like NPC selling item or previous item in transformation
	 * @return ItemInstance corresponding to the destroyed item or the updated item in inventory
	 */
	public ItemInstance dropItem(String process, ItemInstance item, PlayerInstance actor, Object reference)
	{
		if (item == null)
		{
			return null;
		}
		synchronized (item)
		{
			if (!_items.containsKey(item.getObjectId()))
			{
				return null;
			}
			removeItem(item);
			item.setOwnerId(process, 0, actor, reference);
			item.setItemLocation(ItemLocation.VOID);
			item.setLastChange(ItemInstance.REMOVED);
			item.updateDatabase();
			refreshWeight();
		}
		return item;
	}
	
	/**
	 * Drop item from inventory by using its <b>objectID</b> and updates database
	 *
	 * @param process
	 *            : String Identifier of process triggering this action
	 * @param objectId
	 *            : int Item Instance identifier of the item to be dropped
	 * @param count
	 *            : int Quantity of items to be dropped
	 * @param actor
	 *            : PlayerInstance Player requesting the item drop
	 * @param reference
	 *            : Object Object referencing current action like NPC selling item or previous item in transformation
	 * @return ItemInstance corresponding to the destroyed item or the updated item in inventory
	 */
	public ItemInstance dropItem(String process, int objectId, long count, PlayerInstance actor, Object reference)
	{
		ItemInstance item = getItemByObjectId(objectId);
		if (item == null)
		{
			return null;
		}
		synchronized (item)
		{
			if (!_items.containsKey(item.getObjectId()))
			{
				return null;
			}
			// Adjust item quantity and create new instance to drop
			// Directly drop entire item
			if (item.getCount() > count)
			{
				item.changeCount(process, -count, actor, reference);
				item.setLastChange(ItemInstance.MODIFIED);
				item.updateDatabase();
				final ItemInstance newItem = ItemTable.getInstance().createItem(process, item.getId(), count, actor, reference);
				newItem.updateDatabase();
				refreshWeight();
				return newItem;
			}
		}
		return dropItem(process, item, actor, reference);
	}
	
	/**
	 * Adds item to inventory for further adjustments and Equip it if necessary (itemlocation defined)
	 *
	 * @param item
	 *            : ItemInstance to be added from inventory
	 */
	@Override
	protected void addItem(ItemInstance item)
	{
		super.addItem(item);
		if (item.isEquipped())
		{
			equipItem(item);
		}
	}
	
	/**
	 * Removes item from inventory for further adjustments.
	 *
	 * @param item
	 *            : ItemInstance to be removed from inventory
	 */
	@Override
	protected boolean removeItem(ItemInstance item)
	{
		// Unequip item if equiped
		for (int i = 0; i < _paperdoll.length; i++)
		{
			if (_paperdoll[i] == item)
			{
				unEquipItemInSlot(i);
			}
		}
		return super.removeItem(item);
	}
	
	/**
	 * @param slot
	 *            the slot.
	 * @return the item in the paperdoll slot
	 */
	public ItemInstance getPaperdollItem(int slot)
	{
		return _paperdoll[slot];
	}
	
	/**
	 * @param slot
	 *            the slot.
	 * @return {@code true} if specified paperdoll slot is empty, {@code false} otherwise
	 */
	public boolean isPaperdollSlotEmpty(int slot)
	{
		return _paperdoll[slot] == null;
	}
	
	public boolean isPaperdollSlotNotEmpty(int slot)
	{
		return _paperdoll[slot] != null;
	}
	
	public boolean isItemEquipped(int itemId)
	{
		for (ItemInstance item : _paperdoll)
		{
			if ((item != null) && (item.getId() == itemId))
			{
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Returns the item in the paperdoll Item slot
	 *
	 * @param slot
	 *            identifier
	 * @return ItemInstance
	 */
	public ItemInstance getPaperdollItemByItemId(long slot)
	{
		final int index = getPaperdollIndex(slot);
		if (index == -1)
		{
			return null;
		}
		return _paperdoll[index];
	}
	
	/**
	 * Returns the ID of the item in the paperdoll slot
	 *
	 * @param slot
	 *            : int designating the slot
	 * @return int designating the ID of the item
	 */
	public int getPaperdollItemId(int slot)
	{
		final ItemInstance item = _paperdoll[slot];
		if (item != null)
		{
			return item.getId();
		}
		return 0;
	}
	
	/**
	 * Returns the ID of the item in the paperdoll slot
	 *
	 * @param slot
	 *            : int designating the slot
	 * @return int designating the ID of the item
	 */
	public int getPaperdollItemDisplayId(int slot)
	{
		final ItemInstance item = _paperdoll[slot];
		return (item != null) ? item.getDisplayId() : 0;
	}
	
	/**
	 * Returns the visual id of the item in the paperdoll slot
	 *
	 * @param slot
	 *            : int designating the slot
	 * @return int designating the ID of the item
	 */
	public int getPaperdollItemVisualId(int slot)
	{
		if ((slot == Inventory.PAPERDOLL_CHEST) && (getOwner() != null) && getOwner().isPlayer())
		{
			int visualArmorTransformationId = getOwner().getActingPlayer().getVisualArmorTransformationId();
			if (visualArmorTransformationId > 0)
			{
				return visualArmorTransformationId;
			}
			if (getOwner().isAffected(EffectFlag.EVENT_HALLOWEEN_APPEARANCE))
			{
				return 49120;
			}
		}
		final ItemInstance item = _paperdoll[slot];
		return (item != null) ? item.getVisualId() : 0;
	}
	
	public VariationInstance getPaperdollAugmentation(int slot)
	{
		final ItemInstance item = _paperdoll[slot];
		return (item != null) ? item.getAugmentation() : null;
	}
	
	/**
	 * Returns the objectID associated to the item in the paperdoll slot
	 *
	 * @param slot
	 *            : int pointing out the slot
	 * @return int designating the objectID
	 */
	public int getPaperdollObjectId(int slot)
	{
		final ItemInstance item = _paperdoll[slot];
		return (item != null) ? item.getObjectId() : 0;
	}
	
	/**
	 * Adds new inventory's paperdoll listener.
	 *
	 * @param listener
	 *            the new listener
	 */
	public synchronized void addPaperdollListener(PaperdollListener listener)
	{
		if (!_paperdollListeners.contains(listener))
		{
			_paperdollListeners.add(listener);
		}
	}
	
	/**
	 * Removes a paperdoll listener.
	 *
	 * @param listener
	 *            the listener to be deleted
	 */
	public synchronized void removePaperdollListener(PaperdollListener listener)
	{
		_paperdollListeners.remove(listener);
	}
	
	/**
	 * Equips an item in the given slot of the paperdoll.<br>
	 * <u><i>Remark :</i></u> The item <b>must be</b> in the inventory already.
	 *
	 * @param slot
	 *            : int pointing out the slot of the paperdoll
	 * @param item
	 *            : ItemInstance pointing out the item to add in slot
	 * @return ItemInstance designating the item placed in the slot before
	 */
	public synchronized ItemInstance setPaperdollItem(int slot, ItemInstance item)
	{
		final ItemInstance old = _paperdoll[slot];
		if (old != item)
		{
			if (old != null)
			{
				_paperdoll[slot] = null;
				_paperdollCache.getPaperdollItems().remove(old);
				// Put old item from paperdoll slot to base location
				if (getOwner().isPet())
				{
					((PetInstance) getOwner()).transferItem("return", old.getObjectId(), old.getCount(), getOwner().getActingPlayer().getInventory(), getOwner().getActingPlayer(), getOwner().getActingPlayer());
					getOwner().getActingPlayer().sendPacket(new PetItemList(getOwner().getInventory().getItems()));
				}
				else
				{
					old.setItemLocation(getBaseLocation());
				}
				old.setLastChange(ItemInstance.MODIFIED);
				// Get the mask for paperdoll
				int mask = 0;
				for (int i = 0; i < PAPERDOLL_TOTALSLOTS; i++)
				{
					final ItemInstance pi = _paperdoll[i];
					if (pi != null)
					{
						mask |= pi.getItem().getItemMask();
					}
				}
				_wearedMask = mask;
				// Notify all paperdoll listener in order to unequip old item in slot
				for (PaperdollListener listener : _paperdollListeners)
				{
					if (listener == null)
					{
						continue;
					}
					listener.notifyUnequiped(slot, old, this);
				}
				old.updateDatabase();
			}
			// Add new item in slot of paperdoll
			if (item != null)
			{
				_paperdoll[slot] = item;
				_paperdollCache.getPaperdollItems().add(item);
				item.setItemLocation(getEquipLocation(), slot);
				item.setLastChange(ItemInstance.MODIFIED);
				_wearedMask |= item.getItem().getItemMask();
				for (PaperdollListener listener : _paperdollListeners)
				{
					if (listener == null)
					{
						continue;
					}
					listener.notifyEquiped(slot, item, this);
				}
				item.updateDatabase();
			}
			_paperdollCache.clearCachedStats();
			getOwner().getStat().recalculateStats(!getOwner().isPlayer());
			if (getOwner().isPlayer())
			{
				getOwner().sendPacket(new ExUserInfoEquipSlot(getOwner().getActingPlayer()));
				final Clan clan = getOwner().getClan();
				if ((clan != null) && clan.getDkp().isUsingDynamicStars() && GlobalVariables.getInstance().getBoolean("dkp_" + clan.getId(), false))
				{
					clan.getDkp().getPlayerDkp(getOwner().getObjectId()).recalculateDynamicStars(getOwner().getActingPlayer());
				}
			}
		}
		// Notify to scripts
		if (old != null)
		{
			final Creature owner = getOwner();
			if ((owner != null) && owner.isPlayer())
			{
				EventDispatcher.getInstance().notifyEventAsync(new OnPlayerItemUnequip(owner.getActingPlayer(), old), old.getItem());
			}
		}
		return old;
	}
	
	/**
	 * @return the mask of wore item
	 */
	public int getWearedMask()
	{
		return _wearedMask;
	}
	
	public long getSlotFromItem(ItemInstance item)
	{
		long slot = -1;
		final int location = item.getLocationSlot();
		switch (location)
		{
			case PAPERDOLL_UNDER:
			{
				slot = Item.BodyPart.SLOT_UNDERWEAR.getSlot();
				break;
			}
			case PAPERDOLL_LEAR:
			{
				slot = Item.BodyPart.SLOT_L_EAR.getSlot();
				break;
			}
			case PAPERDOLL_REAR:
			{
				slot = Item.BodyPart.SLOT_R_EAR.getSlot();
				break;
			}
			case PAPERDOLL_NECK:
			{
				slot = Item.BodyPart.SLOT_NECK.getSlot();
				break;
			}
			case PAPERDOLL_RFINGER:
			{
				slot = Item.BodyPart.SLOT_R_FINGER.getSlot();
				break;
			}
			case PAPERDOLL_LFINGER:
			{
				slot = Item.BodyPart.SLOT_L_FINGER.getSlot();
				break;
			}
			case PAPERDOLL_HAIR:
			{
				slot = Item.BodyPart.SLOT_HAIR.getSlot();
				break;
			}
			case PAPERDOLL_HAIR2:
			{
				slot = Item.BodyPart.SLOT_HAIR2.getSlot();
				break;
			}
			case PAPERDOLL_HEAD:
			{
				slot = Item.BodyPart.SLOT_HEAD.getSlot();
				break;
			}
			case PAPERDOLL_RHAND:
			{
				slot = Item.BodyPart.SLOT_R_HAND.getSlot();
				break;
			}
			case PAPERDOLL_LHAND:
			{
				slot = Item.BodyPart.SLOT_L_HAND.getSlot();
				break;
			}
			case PAPERDOLL_GLOVES:
			{
				slot = Item.BodyPart.SLOT_GLOVES.getSlot();
				break;
			}
			case PAPERDOLL_CHEST:
			{
				slot = item.getItem().getBodyPart();
				break;
			}
			case PAPERDOLL_LEGS:
			{
				slot = Item.BodyPart.SLOT_LEGS.getSlot();
				break;
			}
			case PAPERDOLL_CLOAK:
			{
				slot = Item.BodyPart.SLOT_BACK.getSlot();
				break;
			}
			case PAPERDOLL_FEET:
			{
				slot = Item.BodyPart.SLOT_FEET.getSlot();
				break;
			}
			case PAPERDOLL_LBRACELET:
			{
				slot = Item.BodyPart.SLOT_L_BRACELET.getSlot();
				break;
			}
			case PAPERDOLL_RBRACELET:
			{
				slot = Item.BodyPart.SLOT_R_BRACELET.getSlot();
				break;
			}
			case PAPERDOLL_DECO1:
			case PAPERDOLL_DECO2:
			case PAPERDOLL_DECO3:
			case PAPERDOLL_DECO4:
			case PAPERDOLL_DECO5:
			case PAPERDOLL_DECO6:
			{
				slot = Item.BodyPart.SLOT_DECO.getSlot();
				break;
			}
			case PAPERDOLL_BELT:
			{
				slot = Item.BodyPart.SLOT_BELT.getSlot();
				break;
			}
			case PAPERDOLL_BROOCH:
			{
				slot = Item.BodyPart.SLOT_BROOCH.getSlot();
				break;
			}
			case PAPERDOLL_BROOCH_JEWEL1:
			case PAPERDOLL_BROOCH_JEWEL2:
			case PAPERDOLL_BROOCH_JEWEL3:
			case PAPERDOLL_BROOCH_JEWEL4:
			case PAPERDOLL_BROOCH_JEWEL5:
			case PAPERDOLL_BROOCH_JEWEL6:
			{
				slot = Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot();
				break;
			}
			case PAPERDOLL_AGATHION1:
			case PAPERDOLL_AGATHION2:
			case PAPERDOLL_AGATHION3:
			case PAPERDOLL_AGATHION4:
			case PAPERDOLL_AGATHION5:
			{
				slot = Item.BodyPart.SLOT_AGATHION.getSlot();
				break;
			}
			case PAPERDOLL_ARTIFACT_BOOK:
			{
				slot = Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot();
				break;
			}
			case PAPERDOLL_ARTIFACT1:
			case PAPERDOLL_ARTIFACT2:
			case PAPERDOLL_ARTIFACT3:
			case PAPERDOLL_ARTIFACT4:
			case PAPERDOLL_ARTIFACT5:
			case PAPERDOLL_ARTIFACT6:
			case PAPERDOLL_ARTIFACT7:
			case PAPERDOLL_ARTIFACT8:
			case PAPERDOLL_ARTIFACT9:
			case PAPERDOLL_ARTIFACT10:
			case PAPERDOLL_ARTIFACT11:
			case PAPERDOLL_ARTIFACT12:
			case PAPERDOLL_ARTIFACT13:
			case PAPERDOLL_ARTIFACT14:
			case PAPERDOLL_ARTIFACT15:
			case PAPERDOLL_ARTIFACT16:
			case PAPERDOLL_ARTIFACT17:
			case PAPERDOLL_ARTIFACT18:
			case PAPERDOLL_ARTIFACT19:
			case PAPERDOLL_ARTIFACT20:
			case PAPERDOLL_ARTIFACT21:
			{
				slot = Item.BodyPart.SLOT_ARTIFACT.getSlot();
			}
		}
		return slot;
	}
	
	/**
	 * Unequips item in body slot and returns alterations.<br>
	 * <b>If you dont need return value use {@link Inventory#unEquipItemInBodySlot(long)} instead</b>
	 *
	 * @param slot
	 *            : int designating the slot of the paperdoll
	 * @return ItemInstance[] : list of changes
	 */
	public ItemInstance[] unEquipItemInBodySlotAndRecord(long slot)
	{
		final ChangeRecorder recorder = newRecorder();
		try
		{
			unEquipItemInBodySlot(slot);
		}
		finally
		{
			removePaperdollListener(recorder);
		}
		return recorder.getChangedItems();
	}
	
	/**
	 * Sets item in slot of the paperdoll to null value
	 *
	 * @param pdollSlot
	 *            : int designating the slot
	 * @return ItemInstance designating the item in slot before change
	 */
	public ItemInstance unEquipItemInSlot(int pdollSlot)
	{
		return setPaperdollItem(pdollSlot, null);
	}
	
	/**
	 * Unequips item in slot and returns alterations<br>
	 * <b>If you dont need return value use {@link Inventory#unEquipItemInSlot(int)} instead</b>
	 *
	 * @param slot
	 *            : int designating the slot
	 * @return ItemInstance[] : list of items altered
	 */
	public ItemInstance[] unEquipItemInSlotAndRecord(int slot)
	{
		final ChangeRecorder recorder = newRecorder();
		try
		{
			unEquipItemInSlot(slot);
		}
		finally
		{
			removePaperdollListener(recorder);
		}
		return recorder.getChangedItems();
	}
	
	/**
	 * Unequips item in slot (i.e. equips with default value)
	 *
	 * @param slot
	 *            : int designating the slot
	 * @return {@link ItemInstance} designating the item placed in the slot
	 */
	public ItemInstance unEquipItemInBodySlot(long slot)
	{
		int pdollSlot = -1;
		if (slot == Item.BodyPart.SLOT_L_EAR.getSlot())
		{
			pdollSlot = PAPERDOLL_LEAR;
		}
		else if (slot == Item.BodyPart.SLOT_R_EAR.getSlot())
		{
			pdollSlot = PAPERDOLL_REAR;
		}
		else if (slot == Item.BodyPart.SLOT_LR_EAR.getSlot()) // used for pets equipment handling
		{
			if (isPaperdollSlotNotEmpty(PAPERDOLL_REAR))
			{
				pdollSlot = PAPERDOLL_REAR;
			}
			else
			{
				pdollSlot = PAPERDOLL_LEAR;
			}
		}
		else if (slot == Item.BodyPart.SLOT_NECK.getSlot())
		{
			pdollSlot = PAPERDOLL_NECK;
		}
		else if (slot == Item.BodyPart.SLOT_R_FINGER.getSlot())
		{
			pdollSlot = PAPERDOLL_RFINGER;
		}
		else if (slot == Item.BodyPart.SLOT_L_FINGER.getSlot())
		{
			pdollSlot = PAPERDOLL_LFINGER;
		}
		else if (slot == Item.BodyPart.SLOT_LR_FINGER.getSlot()) // used for pets equipment handling
		{
			if (isPaperdollSlotNotEmpty(PAPERDOLL_RFINGER))
			{
				pdollSlot = PAPERDOLL_RFINGER;
			}
			else
			{
				pdollSlot = PAPERDOLL_LFINGER;
			}
		}
		else if (slot == Item.BodyPart.SLOT_HAIR.getSlot())
		{
			pdollSlot = PAPERDOLL_HAIR;
		}
		else if (slot == Item.BodyPart.SLOT_HAIR2.getSlot())
		{
			pdollSlot = PAPERDOLL_HAIR2;
		}
		else if (slot == Item.BodyPart.SLOT_HAIRALL.getSlot())
		{
			setPaperdollItem(PAPERDOLL_HAIR, null);
			pdollSlot = PAPERDOLL_HAIR;
		}
		else if (slot == Item.BodyPart.SLOT_HEAD.getSlot())
		{
			pdollSlot = PAPERDOLL_HEAD;
		}
		else if ((slot == Item.BodyPart.SLOT_R_HAND.getSlot()) || (slot == Item.BodyPart.SLOT_LR_HAND.getSlot()))
		{
			pdollSlot = PAPERDOLL_RHAND;
		}
		else if (slot == Item.BodyPart.SLOT_L_HAND.getSlot())
		{
			pdollSlot = PAPERDOLL_LHAND;
		}
		else if (slot == Item.BodyPart.SLOT_GLOVES.getSlot())
		{
			pdollSlot = PAPERDOLL_GLOVES;
		}
		else if ((slot == Item.BodyPart.SLOT_CHEST.getSlot()) || (slot == Item.BodyPart.SLOT_ALLDRESS.getSlot()) || (slot == Item.BodyPart.SLOT_FULL_ARMOR.getSlot()))
		{
			pdollSlot = PAPERDOLL_CHEST;
		}
		else if (slot == Item.BodyPart.SLOT_LEGS.getSlot())
		{
			pdollSlot = PAPERDOLL_LEGS;
		}
		else if (slot == Item.BodyPart.SLOT_BACK.getSlot())
		{
			pdollSlot = PAPERDOLL_CLOAK;
		}
		else if (slot == Item.BodyPart.SLOT_FEET.getSlot())
		{
			pdollSlot = PAPERDOLL_FEET;
		}
		else if (slot == Item.BodyPart.SLOT_UNDERWEAR.getSlot())
		{
			pdollSlot = PAPERDOLL_UNDER;
		}
		else if (slot == Item.BodyPart.SLOT_L_BRACELET.getSlot())
		{
			pdollSlot = PAPERDOLL_LBRACELET;
		}
		else if (slot == Item.BodyPart.SLOT_R_BRACELET.getSlot())
		{
			pdollSlot = PAPERDOLL_RBRACELET;
		}
		else if (slot == Item.BodyPart.SLOT_DECO.getSlot())
		{
			pdollSlot = PAPERDOLL_DECO1;
		}
		else if (slot == Item.BodyPart.SLOT_BELT.getSlot())
		{
			pdollSlot = PAPERDOLL_BELT;
		}
		else if (slot == Item.BodyPart.SLOT_BROOCH.getSlot())
		{
			pdollSlot = PAPERDOLL_BROOCH;
		}
		else if (slot == Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot())
		{
			pdollSlot = PAPERDOLL_BROOCH_JEWEL1;
		}
		else if (slot == Item.BodyPart.SLOT_AGATHION.getSlot())
		{
			pdollSlot = PAPERDOLL_AGATHION1;
		}
		else if (slot == Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot())
		{
			pdollSlot = PAPERDOLL_ARTIFACT_BOOK;
		}
		else if (slot == Item.BodyPart.SLOT_ARTIFACT.getSlot())
		{
			pdollSlot = PAPERDOLL_ARTIFACT1;
		}
		else
		{
			LOGGER.info("Unhandled slot type: " + slot);
			LOGGER.info(CommonUtil.getTraceString(Thread.currentThread().getStackTrace()));
		}
		if (pdollSlot >= 0)
		{
			return setPaperdollItem(pdollSlot, null);
		}
		return null;
	}
	
	/**
	 * Equips item and returns list of alterations<br>
	 * <b>If you don't need return value use {@link Inventory#equipItem(ItemInstance)} instead</b>
	 *
	 * @param item
	 *            : ItemInstance corresponding to the item
	 * @return ItemInstance[] : list of alterations
	 */
	public ItemInstance[] equipItemAndRecord(ItemInstance item)
	{
		final ChangeRecorder recorder = newRecorder();
		try
		{
			equipItem(item);
		}
		finally
		{
			removePaperdollListener(recorder);
		}
		return recorder.getChangedItems();
	}
	
	/**
	 * Equips item in slot of paperdoll.
	 *
	 * @param item
	 *            : ItemInstance designating the item and slot used.
	 */
	public void equipItem(ItemInstance item)
	{
		if (getOwner().isPlayer())
		{
			if (((PlayerInstance) getOwner()).getPrivateStoreType() != PrivateStoreType.NONE)
			{
				return;
			}
			final PlayerInstance player = (PlayerInstance) getOwner();
			if (!player.canOverrideCond(PlayerCondOverride.ITEM_CONDITIONS) && !player.isHero() && item.isHeroItem())
			{
				return;
			}
		}
		final long targetSlot = item.getItem().getBodyPart();
		// Check if player is using Formal Wear and item isn't Wedding Bouquet.
		final ItemInstance formal = getPaperdollItem(PAPERDOLL_CHEST);
		if ((item.getId() != 21163) && (formal != null) && (formal.getItem().getBodyPart() == Item.BodyPart.SLOT_ALLDRESS.getSlot()))
		{
			// only chest target can pass this
			if ((targetSlot == Item.BodyPart.SLOT_LR_HAND.getSlot()) || (targetSlot == Item.BodyPart.SLOT_L_HAND.getSlot()) || (targetSlot == Item.BodyPart.SLOT_R_HAND.getSlot()) || (targetSlot == Item.BodyPart.SLOT_LEGS.getSlot()) || (targetSlot == Item.BodyPart.SLOT_FEET.getSlot()) || (targetSlot == Item.BodyPart.SLOT_GLOVES.getSlot()) || (targetSlot == Item.BodyPart.SLOT_HEAD.getSlot()))
			{
				return;
			}
		}
		if (getOwner().isPlayer() && item.isWeapon())
		{
			if ((getOwner().getRace() == Race.ORC) || (getOwner().getRace() == Race.DWARF)) // Orcs and dwarfs cant equip rapiers
			{
				String type = ClientWeaponTypesData.getInstance().getClientWeaponType(item.getId());
				if ((type != null) && type.equals("rapier"))
				{
					return;
				}
			}
			else if (getOwner().getRace() == Race.SYLPH) // Sylphs can equip only pistols
			{
				Weapon weapon = (Weapon) item.getItem();
				if (!weapon.getItemType().isPistols())
				{
					if ((item.getId() != 49798) && (item.getId() != 71844) && (item.getId() != 91162) && (item.getId() != 93994) && (item.getId() != 150000)) // Pom poms
					{
						return;
					}
				}
			}
			if (getOwner().getRace() != Race.SYLPH)
			{
				Weapon weapon = (Weapon) item.getItem(); // Only sylphs can equip pistols
				if (weapon.getItemType().isPistols() && (item.getId() != 150000))
				{
					return;
				}
			}
			if (getOwner().getActingPlayer().isVanguard()) // Vanguard can equip only pole
			{
				if (((Weapon) item.getItem()).getItemType() != WeaponType.POLE && (item.getId() != 150000))
				{
					return;
				}
			}
		}
		if (getOwner().isPlayer() && item.isArmor()) // Death Knights and sylphs cant equip shields
		{
			if (getOwner().getActingPlayer().isDeathKnight() || getOwner().getActingPlayer().isVanguard() || (getOwner().getActingPlayer().getRace() == Race.SYLPH))
			{
				Armor armor = item.getArmorItem();
				if (armor.getItemType() == ArmorType.SHIELD)
				{
					return;
				}
			}
		}
		// don't care about arrows, listener will unequip them (hopefully)
		// handle full armor
		// formal dress
		if (targetSlot == Item.BodyPart.SLOT_LR_HAND.getSlot())
		{
			ItemInstance shieldItem = getPaperdollItem(PAPERDOLL_LHAND);
			if (shieldItem != null)
			{
				Armor armor = shieldItem.getArmorItem();
				if ((armor != null) && (armor.getItemType() != ArmorType.SIGIL))
				{
					setPaperdollItem(PAPERDOLL_LHAND, null);
				}
			}
			setPaperdollItem(PAPERDOLL_RHAND, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_L_HAND.getSlot())
		{
			final ItemInstance weaponItem = getPaperdollItem(PAPERDOLL_RHAND);
			if (weaponItem != null)
			{
				final Weapon weapon = weaponItem.getWeaponItem();
				if ((weapon != null) && (weapon.getBodyPart() == Item.BodyPart.SLOT_LR_HAND.getSlot()))
				{
					Armor armor = item.getArmorItem();
					if ((armor != null) && (armor.getItemType() != ArmorType.SIGIL))
					{
						setPaperdollItem(PAPERDOLL_RHAND, null);
					}
				}
			}
			setPaperdollItem(PAPERDOLL_LHAND, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_R_HAND.getSlot())
		{
			setPaperdollItem(PAPERDOLL_RHAND, item);
		}
		else if ((targetSlot == Item.BodyPart.SLOT_L_EAR.getSlot()) || (targetSlot == Item.BodyPart.SLOT_R_EAR.getSlot()) || (targetSlot == Item.BodyPart.SLOT_LR_EAR.getSlot()))
		{
			if (_paperdoll[PAPERDOLL_LEAR] == null)
			{
				setPaperdollItem(PAPERDOLL_LEAR, item);
			}
			else if (_paperdoll[PAPERDOLL_REAR] == null)
			{
				setPaperdollItem(PAPERDOLL_REAR, item);
			}
			else
			{
				setPaperdollItem(PAPERDOLL_LEAR, item);
			}
		}
		else if ((targetSlot == Item.BodyPart.SLOT_L_FINGER.getSlot()) || (targetSlot == Item.BodyPart.SLOT_R_FINGER.getSlot()) || (targetSlot == Item.BodyPart.SLOT_LR_FINGER.getSlot()))
		{
			if (_paperdoll[PAPERDOLL_LFINGER] == null)
			{
				setPaperdollItem(PAPERDOLL_LFINGER, item);
			}
			else if (_paperdoll[PAPERDOLL_RFINGER] == null)
			{
				setPaperdollItem(PAPERDOLL_RFINGER, item);
			}
			else
			{
				setPaperdollItem(PAPERDOLL_LFINGER, item);
			}
		}
		else if (targetSlot == Item.BodyPart.SLOT_NECK.getSlot())
		{
			setPaperdollItem(PAPERDOLL_NECK, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_FULL_ARMOR.getSlot())
		{
			setPaperdollItem(PAPERDOLL_LEGS, null);
			setPaperdollItem(PAPERDOLL_CHEST, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_CHEST.getSlot())
		{
			setPaperdollItem(PAPERDOLL_CHEST, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_LEGS.getSlot())
		{
			final ItemInstance chest = getPaperdollItem(PAPERDOLL_CHEST);
			if ((chest != null) && (chest.getItem().getBodyPart() == Item.BodyPart.SLOT_FULL_ARMOR.getSlot()))
			{
				setPaperdollItem(PAPERDOLL_CHEST, null);
			}
			setPaperdollItem(PAPERDOLL_LEGS, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_FEET.getSlot())
		{
			setPaperdollItem(PAPERDOLL_FEET, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_GLOVES.getSlot())
		{
			setPaperdollItem(PAPERDOLL_GLOVES, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_HEAD.getSlot())
		{
			setPaperdollItem(PAPERDOLL_HEAD, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_HAIR.getSlot())
		{
			final ItemInstance hair = getPaperdollItem(PAPERDOLL_HAIR);
			if ((hair != null) && (hair.getItem().getBodyPart() == Item.BodyPart.SLOT_HAIRALL.getSlot()))
			{
				setPaperdollItem(PAPERDOLL_HAIR2, null);
			}
			else
			{
				setPaperdollItem(PAPERDOLL_HAIR, null);
			}
			setPaperdollItem(PAPERDOLL_HAIR, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_HAIR2.getSlot())
		{
			final ItemInstance hair2 = getPaperdollItem(PAPERDOLL_HAIR);
			if ((hair2 != null) && (hair2.getItem().getBodyPart() == Item.BodyPart.SLOT_HAIRALL.getSlot()))
			{
				setPaperdollItem(PAPERDOLL_HAIR, null);
			}
			else
			{
				setPaperdollItem(PAPERDOLL_HAIR2, null);
			}
			setPaperdollItem(PAPERDOLL_HAIR2, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_HAIRALL.getSlot())
		{
			setPaperdollItem(PAPERDOLL_HAIR2, null);
			setPaperdollItem(PAPERDOLL_HAIR, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_UNDERWEAR.getSlot())
		{
			setPaperdollItem(PAPERDOLL_UNDER, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_BACK.getSlot())
		{
			setPaperdollItem(PAPERDOLL_CLOAK, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_L_BRACELET.getSlot())
		{
			setPaperdollItem(PAPERDOLL_LBRACELET, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_R_BRACELET.getSlot())
		{
			setPaperdollItem(PAPERDOLL_RBRACELET, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_DECO.getSlot())
		{
			equipTalisman(item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_BELT.getSlot())
		{
			setPaperdollItem(PAPERDOLL_BELT, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_ALLDRESS.getSlot())
		{
			setPaperdollItem(PAPERDOLL_LEGS, null);
			setPaperdollItem(PAPERDOLL_LHAND, null);
			setPaperdollItem(PAPERDOLL_RHAND, null);
			setPaperdollItem(PAPERDOLL_HEAD, null);
			setPaperdollItem(PAPERDOLL_FEET, null);
			setPaperdollItem(PAPERDOLL_GLOVES, null);
			setPaperdollItem(PAPERDOLL_CHEST, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_BROOCH.getSlot())
		{
			setPaperdollItem(PAPERDOLL_BROOCH, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot())
		{
			equipBroochJewel(item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_AGATHION.getSlot())
		{
			equipAgathion(item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot())
		{
			setPaperdollItem(PAPERDOLL_ARTIFACT_BOOK, item);
		}
		else if (targetSlot == Item.BodyPart.SLOT_ARTIFACT.getSlot())
		{
			equipArtifact(item);
		}
		else
		{
			LOGGER.warning("Unknown body slot " + targetSlot + " for Item ID: " + item.getId());
		}
	}
	
	/**
	 * Refresh the weight of equipment loaded
	 */
	@Override
	protected void refreshWeight()
	{
		long weight = 0;
		for (ItemInstance item : _items.values())
		{
			if ((item != null) && (item.getItem() != null))
			{
				weight += item.getItem().getWeight() * item.getCount();
			}
		}
		_totalWeight = (int) Math.min(weight, Integer.MAX_VALUE);
	}
	
	/**
	 * @return the totalWeight.
	 */
	public int getTotalWeight()
	{
		return _totalWeight;
	}
	
	/**
	 * Return the ItemInstance of the arrows needed for this bow.
	 *
	 * @param bow
	 *            : Item designating the bow
	 * @return ItemInstance pointing out arrows for bow
	 */
	public ItemInstance findArrowForBow(Item bow)
	{
		if (bow == null)
		{
			return null;
		}
		for (ItemInstance item : getItems())
		{
			if (item.isEtcItem() && (item.getItem().getCrystalTypePlus() == bow.getCrystalTypePlus()) && (item.getEtcItem().getItemType() == EtcItemType.ARROW))
			{
				return item;
			}
		}
		return null;
	}
	
	/**
	 * Return the ItemInstance of the bolts needed for this crossbow.
	 *
	 * @param crossbow
	 *            : Item designating the crossbow
	 * @return ItemInstance pointing out bolts for crossbow
	 */
	public ItemInstance findBoltForCrossBow(Item crossbow)
	{
		ItemInstance bolt = null;
		for (ItemInstance item : getItems())
		{
			if (item.isEtcItem() && (item.getItem().getCrystalTypePlus() == crossbow.getCrystalTypePlus()) && (item.getEtcItem().getItemType() == EtcItemType.BOLT))
			{
				bolt = item;
				break;
			}
		}
		// Get the ItemInstance corresponding to the item identifier and return it
		return bolt;
	}
	
	/**
	 * Return the ItemInstance of the bolts needed for these pistols.
	 *
	 * @param pistols
	 *            : Item designating the pistols
	 * @return ItemInstance pointing out elemental orb for pistols
	 */
	public ItemInstance findElementalOrbForPistols(Item pistols)
	{
		ItemInstance orb = null;
		for (ItemInstance item : getItems())
		{
			if (item.isEtcItem() && (item.getItem().getCrystalTypePlus() == pistols.getCrystalTypePlus()) && (item.getEtcItem().getItemType() == EtcItemType.ELEMENTAL_ORB))
			{
				orb = item;
				break;
			}
		}
		// Get the ItemInstance corresponding to the item identifier and return it
		return orb;
	}
	
	/**
	 * Get back items in inventory from database
	 */
	@Override
	public void restore()
	{
		try (Connection con = DatabaseFactory.getConnection(); PreparedStatement ps = con.prepareStatement("SELECT * FROM items WHERE owner_id=? AND pet_id=? AND (loc=? OR loc=?) ORDER BY loc_data"))
		{
			ps.setInt(1, getOwnerId());
			ps.setInt(2, getOwner().isPet() ? ((PetInstance) getOwner()).getControlObjectId() : 0);
			ps.setString(3, getBaseLocation().name());
			ps.setString(4, getEquipLocation().name());
			try (ResultSet rs = ps.executeQuery())
			{
				while (rs.next())
				{
					try
					{
						final ItemInstance item = new ItemInstance(rs);
						if (getOwner().isPlayer())
						{
							final PlayerInstance player = (PlayerInstance) getOwner();
							if (!player.canOverrideCond(PlayerCondOverride.ITEM_CONDITIONS) && !player.isHero() && item.isHeroItem())
							{
								item.setItemLocation(ItemLocation.INVENTORY);
							}
						}
						World.getInstance().addObject(item);
						// If stackable item is found in inventory just add to current quantity
						if (item.isStackable() && (getItemByItemId(item.getId()) != null))
						{
							addItem("Restore", item, getOwner().getActingPlayer(), null);
						}
						else
						{
							addItem(item);
						}
					}
					catch (Exception e)
					{
						LOGGER.warning("Could not restore item " + rs.getInt("item_id") + " for " + getOwner());
						e.printStackTrace();
					}
				}
			}
			refreshWeight();
		}
		catch (Exception e)
		{
			LOGGER.log(Level.WARNING, "Could not restore inventory: " + e.getMessage(), e);
		}
	}
	
	public int getTalismanSlots()
	{
		return getOwner().getActingPlayer().getStat().getTalismanSlots();
	}
	
	private void equipTalisman(ItemInstance item)
	{
		if (getTalismanSlots() == 0)
		{
			return;
		}
		// find same (or incompatible) talisman type
		for (int i = PAPERDOLL_DECO1; i < (PAPERDOLL_DECO1 + getTalismanSlots()); i++)
		{
			if ((_paperdoll[i] != null) && (getPaperdollItemId(i) == item.getId()))
			{
				// overwrite
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slot found - put on first free
		for (int i = PAPERDOLL_DECO1; i < (PAPERDOLL_DECO1 + getTalismanSlots()); i++)
		{
			if (_paperdoll[i] == null)
			{
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slots - put on first
		setPaperdollItem(PAPERDOLL_DECO1, item);
	}
	
	public int getArtifactSlots()
	{
		return getOwner().getActingPlayer().getStat().getArtifactSlots();
	}
	
	private void equipArtifact(ItemInstance item)
	{
		final int slotNumber = getArtifactSlots();
		if (slotNumber == 0)
		{
			return;
		}
		switch (item.getItem().getArtifactSlot())
		{
			case 1: // Attack
			{
				for (int i = PAPERDOLL_ARTIFACT13; i < (PAPERDOLL_ARTIFACT13 + slotNumber); i++)
				{
					if (_paperdoll[i] == null)
					{
						setPaperdollItem(i, item);
						return;
					}
				}
				break;
			}
			case 2: // Protection
			{
				for (int i = PAPERDOLL_ARTIFACT16; i < (PAPERDOLL_ARTIFACT16 + slotNumber); i++)
				{
					if (_paperdoll[i] == null)
					{
						setPaperdollItem(i, item);
						return;
					}
				}
				break;
			}
			case 3: // Support
			{
				for (int i = PAPERDOLL_ARTIFACT19; i < (PAPERDOLL_ARTIFACT19 + slotNumber); i++)
				{
					if (_paperdoll[i] == null)
					{
						setPaperdollItem(i, item);
						return;
					}
				}
				break;
			}
			case 4: // Balance
			{
				for (int i = PAPERDOLL_ARTIFACT1; i < (PAPERDOLL_ARTIFACT1 + (4 * slotNumber)); i++)
				{
					if (_paperdoll[i] == null)
					{
						setPaperdollItem(i, item);
						return;
					}
				}
				break;
			}
		}
	}
	
	public int getBroochJewelSlots()
	{
		return getOwner().getActingPlayer().getStat().getBroochJewelSlots();
	}
	
	private void equipBroochJewel(ItemInstance item)
	{
		if (getBroochJewelSlots() == 0)
		{
			return;
		}
		// find same (or incompatible) brooch jewel type
		for (int i = PAPERDOLL_BROOCH_JEWEL1; i < (PAPERDOLL_BROOCH_JEWEL1 + getBroochJewelSlots()); i++)
		{
			if ((_paperdoll[i] != null) && (getPaperdollItemId(i) == item.getId()))
			{
				// overwrite
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slot found - put on first free
		for (int i = PAPERDOLL_BROOCH_JEWEL1; i < (PAPERDOLL_BROOCH_JEWEL1 + getBroochJewelSlots()); i++)
		{
			if (_paperdoll[i] == null)
			{
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slots - put on first
		setPaperdollItem(PAPERDOLL_BROOCH_JEWEL1, item);
	}
	
	public int getAgathionSlots()
	{
		return getOwner().getActingPlayer().getStat().getAgathionSlots();
	}
	
	private void equipAgathion(ItemInstance item)
	{
		if (getAgathionSlots() == 0)
		{
			return;
		}
		// find same (or incompatible) agathion type
		for (int i = PAPERDOLL_AGATHION1; i < (PAPERDOLL_AGATHION1 + getAgathionSlots()); i++)
		{
			if ((_paperdoll[i] != null) && (getPaperdollItemId(i) == item.getId()))
			{
				// overwrite
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slot found - put on first free
		for (int i = PAPERDOLL_AGATHION1; i < (PAPERDOLL_AGATHION1 + getAgathionSlots()); i++)
		{
			if (_paperdoll[i] == null)
			{
				setPaperdollItem(i, item);
				return;
			}
		}
		// no free slots - put on first
		setPaperdollItem(PAPERDOLL_AGATHION1, item);
	}
	
	public boolean canEquipCloak()
	{
		return getOwner().getActingPlayer().getStat().canEquipCloak();
	}
	
	/**
	 * Re-notify to paperdoll listeners every equipped item
	 */
	public void reloadEquippedItems()
	{
		int slot;
		for (ItemInstance item : _paperdoll)
		{
			if (item == null)
			{
				continue;
			}
			slot = item.getLocationSlot();
			for (PaperdollListener listener : _paperdollListeners)
			{
				if (listener == null)
				{
					continue;
				}
				listener.notifyUnequiped(slot, item, this);
				listener.notifyEquiped(slot, item, this);
			}
		}
		if (getOwner().isPlayer())
		{
			getOwner().sendPacket(new ExUserInfoEquipSlot(getOwner().getActingPlayer()));
		}
	}
	
	public int getArmorMinEnchant()
	{
		if ((getOwner() == null) || !getOwner().isPlayer())
		{
			return 0;
		}
		final PlayerInstance player = getOwner().getActingPlayer();
		return _paperdollCache.getMaxSetEnchant(player);
	}
	
	public int getWeaponEnchant()
	{
		final ItemInstance item = getPaperdollItem(PAPERDOLL_RHAND);
		return item != null ? item.getEnchantLevel() : 0;
	}
	
	/**
	 * Blocks the given item slot from being equipped.
	 *
	 * @param itemSlot
	 *            mask from Item
	 */
	public void blockItemSlot(long itemSlot)
	{
		_blockedItemSlotsMask |= itemSlot;
	}
	
	/**
	 * Unblocks the given item slot so it can be equipped.
	 *
	 * @param itemSlot
	 *            mask from Item
	 */
	public void unblockItemSlot(long itemSlot)
	{
		_blockedItemSlotsMask &= ~itemSlot;
	}
	
	/**
	 * @param itemSlot
	 *            mask from Item
	 * @return if the given item slot is blocked or not.
	 */
	public boolean isItemSlotBlocked(long itemSlot)
	{
		return (_blockedItemSlotsMask & itemSlot) == itemSlot;
	}
	
	/**
	 * @param itemSlotsMask
	 *            use 0 to unset all blocked item slots.
	 */
	public void setBlockedItemSlotsMask(int itemSlotsMask)
	{
		_blockedItemSlotsMask = itemSlotsMask;
	}
	
	/**
	 * Reduce the arrow number of the Creature.<br>
	 * <br>
	 * <b><u>Overridden in</u>:</b>
	 * <li>PlayerInstance</li><br>
	 *
	 * @param arrows
	 * @param type
	 */
	public void reduceArrowCount(ItemInstance arrows, EtcItemType type)
	{
		// default is to do nothing
	}
	
	/**
	 * Gets the items in paperdoll slots filtered by filter.
	 *
	 * @param filters
	 *            multiple filters
	 * @return the filtered items in inventory
	 */
	@SafeVarargs
	public final Collection<ItemInstance> getPaperdollItems(Predicate<ItemInstance>... filters)
	{
		if (filters.length == 0)
		{
			return _paperdollCache.getPaperdollItems();
		}
		Predicate<ItemInstance> filter = Objects::nonNull;
		for (Predicate<ItemInstance> additionalFilter : filters)
		{
			filter = filter.and(additionalFilter);
		}
		final List<ItemInstance> items = new ArrayList<>(_paperdoll.length / (filters.length + 1));
		for (ItemInstance item : _paperdoll)
		{
			if (filter.test(item))
			{
				items.add(item);
			}
		}
		return items;
	}
	
	@SafeVarargs
	public final int getPaperdollItemCount(Predicate<ItemInstance>... filters)
	{
		if (filters.length == 0)
		{
			return _paperdollCache.getPaperdollItems().size();
		}
		Predicate<ItemInstance> filter = Objects::nonNull;
		for (Predicate<ItemInstance> additionalFilter : filters)
		{
			filter = filter.and(additionalFilter);
		}
		int count = 0;
		for (ItemInstance item : _paperdoll)
		{
			if (filter.test(item))
			{
				count++;
			}
		}
		return count;
	}
	
	public PaperdollCache getPaperdollCache()
	{
		return _paperdollCache;
	}
	
	public interface PaperdollListener
	{
		void notifyEquiped(int slot, ItemInstance inst, Inventory inventory);
		
		void notifyUnequiped(int slot, ItemInstance inst, Inventory inventory);
	}
	
	// Recorder of alterations in inventory
	private static final class ChangeRecorder implements PaperdollListener
	{
		private final Inventory			_inventory;
		private final Set<ItemInstance>	_changed	= ConcurrentHashMap.newKeySet();
		
		/**
		 * Constructor of the ChangeRecorder
		 *
		 * @param inventory
		 */
		ChangeRecorder(Inventory inventory)
		{
			_inventory = inventory;
			_inventory.addPaperdollListener(this);
		}
		
		/**
		 * Add alteration in inventory when item equipped
		 *
		 * @param slot
		 * @param item
		 * @param inventory
		 */
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			_changed.add(item);
		}
		
		/**
		 * Add alteration in inventory when item unequipped
		 *
		 * @param slot
		 * @param item
		 * @param inventory
		 */
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			_changed.add(item);
		}
		
		/**
		 * Returns alterations in inventory
		 *
		 * @return ItemInstance[] : array of altered items
		 */
		public ItemInstance[] getChangedItems()
		{
			return _changed.toArray(new ItemInstance[_changed.size()]);
		}
	}
	
	private static final class BowCrossRodListener implements PaperdollListener
	{
		private static BowCrossRodListener instance = new BowCrossRodListener();
		
		public static BowCrossRodListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (slot != PAPERDOLL_RHAND)
			{
				return;
			}
			if (item.getItemType() == WeaponType.FISHINGROD)
			{
				final ItemInstance lure = inventory.getPaperdollItem(PAPERDOLL_LHAND);
				if (lure != null)
				{
					inventory.setPaperdollItem(PAPERDOLL_LHAND, null);
				}
			}
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			// used to be arrows equip for bows before essence
		}
	}
	
	private static final class StatsListener implements PaperdollListener
	{
		private static StatsListener instance = new StatsListener();
		
		public static StatsListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			inventory.getOwner().getStat().recalculateStats(true);
			if (inventory.getOwner().isPlayer())
			{
				PlayerInstance player = (PlayerInstance) inventory.getOwner();
				player.updateStatBonusPassives();
			}
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			inventory.getOwner().getStat().recalculateStats(true);
			if (inventory.getOwner().isPlayer())
			{
				PlayerInstance player = (PlayerInstance) inventory.getOwner();
				player.updateStatBonusPassives();
			}
		}
	}
	
	private static final class ItemSkillsListener implements PaperdollListener
	{
		private static ItemSkillsListener instance = new ItemSkillsListener();
		
		public static ItemSkillsListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (!inventory.getOwner().isPlayer() && !inventory.getOwner().isPet())
			{
				return;
			}
			final Playable owner = (Playable) inventory.getOwner();
			final Item it = item.getItem();
			boolean update = false;
			boolean updateTimestamp = false;
			// Remove augmentation bonuses on unequip
			if (item.isAugmented() && owner.isPlayer())
			{
				item.getAugmentation().removeBonus(owner.getActingPlayer(), item);
			}
			// Recalculate all stats
			owner.getStat().recalculateStats(true);
			final List<ItemSkillHolder> onEnchantSkills = it.getSkills(ItemSkillType.ON_ENCHANT);
			if (onEnchantSkills != null)
			{
				for (ItemSkillHolder holder : onEnchantSkills)
				{
					// Remove skills bestowed from +4 armor
					if (item.getEnchantLevel() >= holder.getValue())
					{
						owner.removeSkill(holder.getSkill(), holder.getSkill().isPassive());
						update = true;
					}
				}
			}
			// Clear enchant bonus
			item.clearEnchantStats((Playable) inventory.getOwner());
			// Clear SA Bonus
			item.clearSpecialAbilities((Playable) inventory.getOwner());
			final List<ItemSkillHolder> normalSkills = it.getSkills(ItemSkillType.NORMAL);
			if (normalSkills != null)
			{
				for (ItemSkillHolder holder : normalSkills)
				{
					final Skill skill = holder.getSkill();
					if (skill != null)
					{
						owner.removeSkill(skill, skill.isPassive());
						update = true;
					}
					else
					{
						LOGGER.warning("Inventory.ItemSkillsListener.Weapon: Incorrect skill: " + holder);
					}
				}
			}
			if (item.isArmor())
			{
				for (ItemInstance itm : inventory.getItems())
				{
					if (!itm.isEquipped() || itm.equals(item))
					{
						continue;
					}
					final List<ItemSkillHolder> otherNormalSkills = itm.getItem().getSkills(ItemSkillType.NORMAL);
					if (otherNormalSkills == null)
					{
						continue;
					}
					for (ItemSkillHolder holder : otherNormalSkills)
					{
						if (owner.getSkillLevel(holder.getSkillId()) != 0)
						{
							continue;
						}
						final Skill skill = holder.getSkill();
						if (skill != null)
						{
							owner.addSkill(skill);
							if (skill.isActive() && !owner.hasSkillReuse(skill.getReuseHashCode()))
							{
								final int equipDelay = item.getEquipReuseDelay();
								if (equipDelay > 0)
								{
									owner.addTimeStamp(skill, equipDelay);
									owner.disableSkill(skill, equipDelay);
								}
								updateTimestamp = true;
							}
							update = true;
						}
					}
				}
			}
			// Must check all equipped items for enchant conditions.
			for (ItemInstance equipped : inventory.getPaperdollItems())
			{
				final List<ItemSkillHolder> otherEnchantSkills = equipped.getItem().getSkills(ItemSkillType.ON_ENCHANT);
				if (otherEnchantSkills == null)
				{
					continue;
				}
				for (ItemSkillHolder holder : otherEnchantSkills)
				{
					// Add skills bestowed from +4 armor
					if (equipped.getEnchantLevel() >= holder.getValue())
					{
						final Skill skill = holder.getSkill();
						// Check passive skill conditions.
						if (skill.isPassive() && !skill.checkConditions(SkillConditionScope.PASSIVE, owner, owner))
						{
							owner.removeSkill(holder.getSkill(), holder.getSkill().isPassive());
							update = true;
						}
					}
				}
			}
			// Must check for toggle and isRemovedOnUnequipWeapon skill item conditions.
			for (Skill skill : owner.getAllSkills())
			{
				if ((skill.isToggle() && owner.isAffectedBySkill(skill.getId()) && !skill.checkConditions(SkillConditionScope.GENERAL, owner, owner)) //
				|| (it.isWeapon() && skill.isRemovedOnUnequipWeapon()))
				{
					owner.stopSkillEffects(true, skill.getId());
					update = true;
				}
			}
			// Apply skill, if weapon have "skills on unequip"
			it.forEachSkill(ItemSkillType.ON_UNEQUIP, holder -> holder.getSkill().activateSkill(owner, owner));
			if (update && owner.isPlayer())
			{
				owner.getActingPlayer().sendSkillList();
			}
			if (updateTimestamp && owner.isPlayer())
			{
				owner.sendPacket(new SkillCoolTime(owner.getActingPlayer()));
			}
			if (item.isWeapon())
			{
				owner.unchargeAllShots();
			}
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (!inventory.getOwner().isPlayer() && !inventory.getOwner().isPet())
			{
				return;
			}
			final Playable owner = (Playable) inventory.getOwner();
			boolean update = false;
			boolean updateTimestamp = false;
			// Apply augmentation bonuses on equip
			if (item.isAugmented() && owner.isPlayer())
			{
				item.getAugmentation().applyBonus(owner.getActingPlayer(), item);
			}
			// Recalculate all stats
			owner.getStat().recalculateStats(true);
			final List<ItemSkillHolder> onEnchantSkills = item.getItem().getSkills(ItemSkillType.ON_ENCHANT);
			if (onEnchantSkills != null)
			{
				for (ItemSkillHolder holder : onEnchantSkills)
				{
					if (owner.getSkillLevel(holder.getSkillId()) >= holder.getSkillLevel())
					{
						continue;
					}
					// Add skills bestowed from +4 armor
					if (item.getEnchantLevel() >= holder.getValue())
					{
						final Skill skill = holder.getSkill();
						// Check passive skill conditions.
						if (skill.isPassive() && !skill.checkConditions(SkillConditionScope.PASSIVE, owner, owner))
						{
							continue;
						}
						owner.addSkill(skill);
						update = true;
					}
				}
			}
			// Apply enchant stats
			item.applyEnchantStats((Playable) inventory.getOwner());
			// Apply SA skill
			item.applySpecialAbilities((Playable) inventory.getOwner());
			final List<ItemSkillHolder> normalSkills = item.getItem().getSkills(ItemSkillType.NORMAL);
			if (normalSkills != null)
			{
				for (ItemSkillHolder holder : normalSkills)
				{
					if (owner.getSkillLevel(holder.getSkillId()) >= holder.getSkillLevel())
					{
						continue;
					}
					final Skill skill = holder.getSkill();
					if (skill != null)
					{
						if (skill.isPassive() && !skill.checkConditions(SkillConditionScope.PASSIVE, owner, owner))
						{
							continue;
						}
						owner.addSkill(skill);
						if (skill.isActive() && !owner.hasSkillReuse(skill.getReuseHashCode()))
						{
							final int equipDelay = item.getEquipReuseDelay();
							if (equipDelay > 0)
							{
								owner.addTimeStamp(skill, equipDelay);
								owner.disableSkill(skill, equipDelay);
							}
							updateTimestamp = true;
						}
						update = true;
					}
					else
					{
						LOGGER.warning("Inventory.ItemSkillsListener.Weapon: Incorrect skill: " + holder);
					}
				}
			}
			// Must check all equipped items for enchant conditions.
			for (ItemInstance equipped : inventory.getPaperdollItems())
			{
				final List<ItemSkillHolder> otherEnchantSkills = equipped.getItem().getSkills(ItemSkillType.ON_ENCHANT);
				if (otherEnchantSkills == null)
				{
					continue;
				}
				for (ItemSkillHolder holder : otherEnchantSkills)
				{
					if (owner.getSkillLevel(holder.getSkillId()) >= holder.getSkillLevel())
					{
						continue;
					}
					// Add skills bestowed from +4 armor
					if (equipped.getEnchantLevel() >= holder.getValue())
					{
						final Skill skill = holder.getSkill();
						// Check passive skill conditions.
						if (skill.isPassive() && !skill.checkConditions(SkillConditionScope.PASSIVE, owner, owner))
						{
							continue;
						}
						owner.addSkill(skill);
						update = true;
					}
				}
			}
			// Apply skill, if weapon have "skills on equip"
			item.getItem().forEachSkill(ItemSkillType.ON_EQUIP, holder -> holder.getSkill().activateSkill(owner, owner));
			if (update && owner.isPlayer())
			{
				owner.getActingPlayer().sendSkillList();
			}
			if (updateTimestamp && owner.isPlayer())
			{
				owner.sendPacket(new SkillCoolTime(owner.getActingPlayer()));
			}
		}
	}
	
	private static final class ArmorSetListener implements PaperdollListener
	{
		private static ArmorSetListener instance = new ArmorSetListener();
		
		public static ArmorSetListener getInstance()
		{
			return instance;
		}
		
		private static boolean applySkills(Playable playable, ItemInstance item, ArmorSet armorSet, Function<ItemInstance, Integer> idProvider)
		{
			final long piecesCount = armorSet.getPiecesCount(playable, idProvider);
			if (piecesCount >= armorSet.getMinimumPieces())
			{
				// Applying all skills that matching the conditions
				boolean updateTimeStamp = false;
				boolean update = false;
				for (ArmorsetSkillHolder holder : armorSet.getSkills())
				{
					if (playable.getSkillLevel(holder.getSkillId()) >= holder.getSkillLevel())
					{
						continue;
					}
					if (holder.validateConditions(playable, armorSet, idProvider))
					{
						final Skill itemSkill = holder.getSkill();
						if (itemSkill == null)
						{
							LOGGER.warning("Inventory.ArmorSetListener.addSkills: Incorrect skill: " + holder);
							continue;
						}
						if (itemSkill.isPassive() && !itemSkill.checkConditions(SkillConditionScope.PASSIVE, playable, playable))
						{
							continue;
						}
						playable.addSkill(itemSkill);
						if (itemSkill.isActive() && (item != null))
						{
							if (!playable.hasSkillReuse(itemSkill.getReuseHashCode()))
							{
								final int equipDelay = item.getEquipReuseDelay();
								if (equipDelay > 0)
								{
									playable.addTimeStamp(itemSkill, equipDelay);
									playable.disableSkill(itemSkill, equipDelay);
								}
							}
							updateTimeStamp = true;
						}
						update = true;
					}
				}
				if (updateTimeStamp && playable.isPlayer())
				{
					playable.sendPacket(new SkillCoolTime(playable.getActingPlayer()));
				}
				return update;
			}
			return false;
		}
		
		private static boolean verifyAndApply(Playable playable, ItemInstance item, Function<ItemInstance, Integer> idProvider)
		{
			boolean update = false;
			final List<ArmorSet> armorSets = ArmorSetData.getInstance().getSets(idProvider.apply(item));
			for (ArmorSet armorSet : armorSets)
			{
				if (applySkills(playable, item, armorSet, idProvider))
				{
					update = true;
				}
			}
			return update;
		}
		
		private static boolean verifyAndRemove(Playable playable, ItemInstance item, Function<ItemInstance, Integer> idProvider)
		{
			boolean update = false;
			final List<ArmorSet> armorSets = ArmorSetData.getInstance().getSets(idProvider.apply(item));
			for (ArmorSet armorSet : armorSets)
			{
				// Remove all skills that doesn't matches the conditions
				for (ArmorsetSkillHolder holder : armorSet.getSkills())
				{
					if (!holder.validateConditions(playable, armorSet, idProvider))
					{
						final Skill itemSkill = holder.getSkill();
						if (itemSkill == null)
						{
							LOGGER.warning("Inventory.ArmorSetListener.removeSkills: Incorrect skill: " + holder);
							continue;
						}
						// Update if a skill has been removed.
						if (playable.removeSkill(itemSkill, itemSkill.isPassive()) != null)
						{
							update = true;
						}
					}
				}
				// Attempt to apply lower level skills if possible
				if (applySkills(playable, item, armorSet, idProvider))
				{
					update = true;
				}
			}
			return update;
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (!inventory.getOwner().isPlayer() && !inventory.getOwner().isPet())
			{
				return;
			}
			final Playable owner = (Playable) inventory.getOwner();
			boolean update = false;
			// Verify and apply normal set
			if (verifyAndApply(owner, item, ItemInstance::getId))
			{
				update = true;
			}
			// Verify and apply visual set
			final int itemVisualId = item.getVisualId();
			if (itemVisualId > 0)
			{
				final AppearanceStone stone = AppearanceItemData.getInstance().getStone(itemVisualId);
				if ((stone != null) && (stone.getType() == AppearanceType.FIXED) && verifyAndApply(owner, item, ItemInstance::getVisualId))
				{
					update = true;
				}
			}
			if (update && owner.isPlayer())
			{
				owner.getActingPlayer().sendSkillList();
			}
			if ((owner.isPlayer() && (item.getItem().getBodyPart() == Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot())) || (item.getItem().getBodyPart() == Item.BodyPart.SLOT_BROOCH.getSlot()))
			{
				owner.getActingPlayer().updateActiveBroochJewel();
			}
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (!inventory.getOwner().isPlayer() && !inventory.getOwner().isPet())
			{
				return;
			}
			final Playable owner = (Playable) inventory.getOwner();
			boolean remove = false;
			// Verify and remove normal set bonus
			if (verifyAndRemove(owner, item, ItemInstance::getId))
			{
				remove = true;
			}
			// Verify and remove visual set bonus
			final int itemVisualId = item.getVisualId();
			if (itemVisualId > 0)
			{
				final AppearanceStone stone = AppearanceItemData.getInstance().getStone(itemVisualId);
				if ((stone != null) && (stone.getType() == AppearanceType.FIXED) && verifyAndRemove(owner, item, ItemInstance::getVisualId))
				{
					remove = true;
				}
			}
			if (remove && owner.isPlayer())
			{
				owner.getActingPlayer().checkItemRestriction();
				owner.getActingPlayer().sendSkillList();
			}
			if ((owner.isPlayer() && (item.getItem().getBodyPart() == Item.BodyPart.SLOT_BROOCH_JEWEL.getSlot())) || (item.getItem().getBodyPart() == Item.BodyPart.SLOT_BROOCH.getSlot()))
			{
				owner.getActingPlayer().updateActiveBroochJewel();
			}
		}
	}
	
	private static final class BraceletListener implements PaperdollListener
	{
		private static BraceletListener instance = new BraceletListener();
		
		public static BraceletListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (item.getItem().getBodyPart() == Item.BodyPart.SLOT_R_BRACELET.getSlot())
			{
				inventory.unEquipItemInSlot(PAPERDOLL_DECO1);
				inventory.unEquipItemInSlot(PAPERDOLL_DECO2);
				inventory.unEquipItemInSlot(PAPERDOLL_DECO3);
				inventory.unEquipItemInSlot(PAPERDOLL_DECO4);
				inventory.unEquipItemInSlot(PAPERDOLL_DECO5);
				inventory.unEquipItemInSlot(PAPERDOLL_DECO6);
			}
		}
		
		// Note (April 3, 2009): Currently on equip, talismans do not display properly, do we need checks here to fix this?
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{}
	}
	
	private static final class BroochListener implements PaperdollListener
	{
		private static BroochListener instance = new BroochListener();
		
		public static BroochListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (item.getItem().getBodyPart() == Item.BodyPart.SLOT_BROOCH.getSlot())
			{
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL1);
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL2);
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL3);
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL4);
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL5);
				inventory.unEquipItemInSlot(PAPERDOLL_BROOCH_JEWEL6);
			}
		}
		
		// Note (April 3, 2009): Currently on equip, talismans do not display properly, do we need checks here to fix this?
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{}
	}
	
	private static final class AgathionBraceletListener implements PaperdollListener
	{
		private static AgathionBraceletListener instance = new AgathionBraceletListener();
		
		public static AgathionBraceletListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (item.getItem().getBodyPart() == Item.BodyPart.SLOT_L_BRACELET.getSlot())
			{
				inventory.unEquipItemInSlot(PAPERDOLL_AGATHION1);
				inventory.unEquipItemInSlot(PAPERDOLL_AGATHION2);
				inventory.unEquipItemInSlot(PAPERDOLL_AGATHION3);
				inventory.unEquipItemInSlot(PAPERDOLL_AGATHION4);
				inventory.unEquipItemInSlot(PAPERDOLL_AGATHION5);
			}
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{}
	}
	
	private static final class ArtifactBookListener implements PaperdollListener
	{
		private static ArtifactBookListener instance = new ArtifactBookListener();
		
		public static ArtifactBookListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (item.getItem().getBodyPart() == Item.BodyPart.SLOT_ARTIFACT_BOOK.getSlot())
			{
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT1);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT2);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT3);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT4);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT5);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT6);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT7);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT8);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT9);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT10);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT11);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT12);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT13);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT14);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT15);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT16);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT17);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT18);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT19);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT20);
				inventory.unEquipItemInSlot(PAPERDOLL_ARTIFACT21);
			}
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{}
	}
	
	private static final class GveRewardListener implements PaperdollListener
	{
		private static GveRewardListener instance = new GveRewardListener();
		
		public static GveRewardListener getInstance()
		{
			return instance;
		}
		
		@Override
		public void notifyEquiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (inventory.getOwner().isPlayer())
			{
				PlayerInstance player = (PlayerInstance) inventory.getOwner();
				player.calcItemReward();
				player.calcEnchantReward();
				player.calcSetReward();
			}
		}
		
		@Override
		public void notifyUnequiped(int slot, ItemInstance item, Inventory inventory)
		{
			if (inventory.getOwner().isPlayer())
			{
				PlayerInstance player = (PlayerInstance) inventory.getOwner();
				player.calcItemReward();
				player.calcEnchantReward();
				player.calcSetReward();
			}
		}
	}
}
