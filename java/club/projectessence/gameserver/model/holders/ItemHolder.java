package club.projectessence.gameserver.model.holders;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.interfaces.IIdentifiable;

/**
 * A simple DTO for items; contains item ID and count.<br>
 * Extended by {@link ItemChanceHolder}, {@link QuestItemHolder}, {@link UniqueItemHolder}.
 *
 * <AUTHOR>
 */
public class ItemHolder implements IIdentifiable
{
	protected final int		_id;
	protected long			_count;
	private int				_enchant;
	private boolean			_canEnchant	= true;
	private long			_price;
	private final String	_name;				// Added field for name
	private final String	_icon;				// Added field for icon
	
	public ItemHolder(StatSet set)
	{
		_id = set.getInt("id");
		_count = set.getLong("count");
		_enchant = 0;
		_price = set.getLong("price", 0);
		_name = null;
		_icon = null;
	}
	
	public ItemHolder(int id, long count)
	{
		_id = id;
		_count = count;
		_enchant = 0;
		_price = 0;
		_name = null;
		_icon = null;
	}
	
	public ItemHolder(int id, long count, int enchant, boolean canEnchant)
	{
		_id = id;
		_count = count;
		_enchant = enchant;
		_canEnchant = canEnchant;
		_price = 0;
		_name = null;
		_icon = null;
	}
	
	public ItemHolder(int id, long count, int enchant, boolean canEnchant, long price)
	{
		_id = id;
		_count = count;
		_enchant = enchant;
		_canEnchant = canEnchant;
		_price = price;
		_name = null;
		_icon = null;
	}
	
	// Added constructor to support name and icon
	public ItemHolder(int id, long count, int enchant, boolean canEnchant, long price, String name, String icon)
	{
		_id = id;
		_count = count;
		_enchant = enchant;
		_canEnchant = canEnchant;
		_price = price;
		_name = name;
		_icon = icon;
	}
	
	/**
	 * @return the ID of the item contained in this object
	 */
	@Override
	public int getId()
	{
		return _id;
	}
	
	public void setCount(long count)
	{
		_count = count;
	}
	
	/**
	 * @return the count of items contained in this object
	 */
	public long getCount()
	{
		return _count;
	}
	
	public int getEnchant()
	{
		return _enchant;
	}
	
	public void setEnchant(int val)
	{
		_enchant = val;
	}
	
	public boolean canEnchant()
	{
		return _canEnchant;
	}
	
	public long getPrice()
	{
		return _price;
	}
	
	public String getName()
	{
		return _name;
	}
	
	public String getIcon()
	{
		return _icon;
	}
	
	@Override
	public boolean equals(Object obj)
	{
		if (!(obj instanceof ItemHolder))
		{
			return false;
		}
		else if (obj == this)
		{
			return true;
		}
		final ItemHolder objInstance = (ItemHolder) obj;
		return (_id == objInstance.getId()) && (_count == objInstance.getCount());
	}
	
	@Override
	public String toString()
	{
		return "[" + getClass().getSimpleName() + "] ID: " + _id + ", count: " + _count + ", price: " + _price;
	}
}