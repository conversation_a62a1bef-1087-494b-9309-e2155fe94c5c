/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.events.impl.creature;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.IBaseEvent;
import club.projectessence.gameserver.model.zone.ZoneType;

/**
 * <AUTHOR>
 */
public class OnCreatureZoneEnter implements IBaseEvent {
	private final Creature _creature;
	private final ZoneType _zone;

	public OnCreatureZoneEnter(Creature creature, ZoneType zone) {
		_creature = creature;
		_zone = zone;
	}

	public Creature getCreature() {
		return _creature;
	}

	public ZoneType getZone() {
		return _zone;
	}

	@Override
	public EventType getType() {
		return EventType.ON_CREATURE_ZONE_ENTER;
	}
}
