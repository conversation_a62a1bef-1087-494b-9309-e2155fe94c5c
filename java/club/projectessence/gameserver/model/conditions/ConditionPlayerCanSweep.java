/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.conditions;

import club.projectessence.Config;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * Checks Sweeper conditions:
 * <ul>
 * <li>Minimum checks, player not null, skill not null.</li>
 * <li>Checks if the target isn't null, is dead and spoiled.</li>
 * <li>Checks if the sweeper player is the target spoiler, or is in the spoiler party.</li>
 * <li>Checks if the corpse is too old.</li>
 * <li>Checks inventory limit and weight max load won't be exceed after sweep.</li>
 * </ul>
 * If two or more conditions aren't meet at the same time, one message per condition will be shown.
 *
 * <AUTHOR>
 */
public class ConditionPlayerCanSweep extends Condition {
	private final boolean _value;

	public ConditionPlayerCanSweep(boolean value) {
		_value = value;
	}

	@Override
	public boolean testImpl(Creature effector, Creature effected, Skill skill, Item item) {
		boolean canSweep = false;
		if (effector.getActingPlayer() != null) {
			final PlayerInstance sweeper = effector.getActingPlayer();
			if (skill != null) {
				for (WorldObject wo : skill.getTargetsAffected(sweeper, effected, false)) {
					if ((wo != null) && wo.isAttackable()) {
						final Attackable attackable = (Attackable) wo;
						if (attackable.isDead()) {
							if (attackable.isSpoiled()) {
								canSweep = attackable.checkSpoilOwner(sweeper, true);
								if (canSweep) {
									canSweep = !attackable.isOldCorpse(sweeper, Config.CORPSE_CONSUME_SKILL_ALLOWED_TIME_BEFORE_DECAY, true);
								}
								if (canSweep) {
									canSweep = sweeper.getInventory().checkInventorySlotsAndWeight(attackable.getSpoilLootItems(), true, true);
								}
							} else {
								sweeper.sendPacket(SystemMessageId.SWEEPER_FAILED_TARGET_NOT_SPOILED);
							}
						}
					}
				}
			}
		}
		return _value == canSweep;
	}
}
