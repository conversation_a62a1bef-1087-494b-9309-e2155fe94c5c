/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.ranking;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ranking.ExOlympiadRankingInfo;

/**
 * <AUTHOR>
 */
public class RequestOlympiadRankingInfo extends ClientPacket {
	private int _tabId;
	private int _rankingType;
	private boolean _currentSeason;
	private int _classId;
	private int _serverId;

	@Override
	public void readImpl() {
		_tabId = readByte(); // cRankingType
		_rankingType = readByte(); // cRankingScope
		_currentSeason = readBoolean(); // bCurrentSeason
		_classId = readInt(); // nClassID
		_serverId = readInt(); // nWorldID
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		client.sendPacket(new ExOlympiadRankingInfo(player, _tabId, _rankingType, _currentSeason, _classId, _serverId));
	}
}
