/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.conditions;

import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * The Class ConditionPlayerPledgeClass.
 *
 * <AUTHOR>
 */
public class ConditionPlayerPledgeClass extends Condition {
	private final int _pledgeClass;

	/**
	 * Instantiates a new condition player pledge class.
	 *
	 * @param pledgeClass the pledge class
	 */
	public ConditionPlayerPledgeClass(int pledgeClass) {
		_pledgeClass = pledgeClass;
	}

	/**
	 * Test impl.
	 *
	 * @return true, if successful
	 */
	@Override
	public boolean testImpl(Creature effector, Creature effected, Skill skill, Item item) {
		final PlayerInstance player = effector.getActingPlayer();
		if ((player == null) || (player.getClan() == null)) {
			return false;
		}

		final boolean isClanLeader = player.isClanLeader();
		if ((_pledgeClass == -1) && !isClanLeader) {
			return false;
		}

		return isClanLeader || (player.getPledgeClass() >= _pledgeClass);
	}
}
