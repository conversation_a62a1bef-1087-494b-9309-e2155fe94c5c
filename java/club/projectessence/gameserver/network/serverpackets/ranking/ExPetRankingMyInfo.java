/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.ranking;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.PetDataTable;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public class ExPetRankingMyInfo extends ServerPacket {
	private final int _petId;
	private final PlayerInstance _player;
	private final Optional<Map.Entry<Integer, StatSet>> _ranking;
	private final Optional<Map.Entry<Integer, StatSet>> _visualRanking;
	private final Map<Integer, StatSet> _rankingList;
	private final Map<Integer, StatSet> _visualtList;

	public ExPetRankingMyInfo(PlayerInstance player, int petId) {
		_player = player;
		_petId = petId;
		_ranking = RankManager.getInstance().getPetRankList().entrySet().stream().filter(it -> it.getValue().getInt("pet_control_object_id") == petId).findFirst();
		_visualRanking = RankManager.getInstance().getPetVisualList().entrySet().stream().filter(it -> it.getValue().getInt("pet_control_object_id") == petId).findFirst();
		_rankingList = RankManager.getInstance().getPetRankList();
		_visualtList = RankManager.getInstance().getPetVisualList();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PET_RANKING_MY_INFO, buffer);
		buffer.writeInt(_petId);
		buffer.writeShort(1);
		buffer.writeInt(-1);
		buffer.writeInt(0);
		buffer.writeInt(_ranking.isPresent() ? _ranking.get().getKey() : 0); // server rank
		buffer.writeInt(_visualRanking.isPresent() ? _visualRanking.get().getKey() : 0); // snapshot server rank
		if (_petId > 0) {
			int typeRank = 1;
			boolean found = false;
			for (StatSet ss : _rankingList.values()) {
				if (ss.getInt("race", -1) == PetDataTable.getInstance().getPetDataByItemId(_player.getInventory().getItemByObjectId(_petId).getId()).getType()) {
					if (ss.getInt("pet_control_object_id", -1) == _petId) {
						found = true;
						buffer.writeInt(typeRank);
						break;
					}
					typeRank++;
				}
			}
			if (!found) {
				buffer.writeInt(0);
			}
			int snapshotTypeRank = 1;
			boolean snapshotFound = false;
			for (StatSet ss : _visualtList.values()) {
				if (ss.getInt("race", -1) == PetDataTable.getInstance().getPetDataByItemId(_player.getInventory().getItemByObjectId(_petId).getId()).getType()) {
					if (ss.getInt("pet_control_object_id", -1) == _petId) {
						snapshotFound = true;
						buffer.writeInt(snapshotTypeRank);
						break;
					}
					snapshotTypeRank++;
				}
			}
			if (!snapshotFound) {
				buffer.writeInt(0);
			}
		} else {
			buffer.writeInt(0);
			buffer.writeInt(0);
		}
	}
}
