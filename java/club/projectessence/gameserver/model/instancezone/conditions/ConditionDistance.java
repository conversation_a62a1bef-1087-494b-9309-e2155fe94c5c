/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.instancezone.conditions;

import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.instancezone.InstanceTemplate;
import club.projectessence.gameserver.network.SystemMessageId;

/**
 * Distance instance condition
 *
 * <AUTHOR>
 */
public class ConditionDistance extends Condition {
	public ConditionDistance(InstanceTemplate template, StatSet parameters, boolean onlyLeader, boolean showMessageAndHtml) {
		super(template, parameters, onlyLeader, showMessageAndHtml);
		setSystemMessage(SystemMessageId.C1_IS_IN_A_LOCATION_WHICH_CANNOT_BE_ENTERED_THEREFORE_IT_CANNOT_BE_PROCESSED, (message, player) -> message.addString(player.getName()));
	}

	@Override
	public boolean test(PlayerInstance player, Npc npc) {
		final int distance = getParameters().getInt("distance", 1000);
		return player.isInsideRadius3D(npc, distance);
	}
}
