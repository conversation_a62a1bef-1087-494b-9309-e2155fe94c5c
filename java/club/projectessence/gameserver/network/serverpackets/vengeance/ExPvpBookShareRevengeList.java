/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.vengeance;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.VengeanceManager;
import club.projectessence.gameserver.instancemanager.VengeanceManager.VengeanceHolder;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExPvpBookShareRevengeList extends ServerPacket {
	private final PlayerInstance _player;

	public ExPvpBookShareRevengeList(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PVPBOOK_SHARE_REVENGE_LIST, buffer);

		List<VengeanceHolder> vengeances = VengeanceManager.getInstance().getVengeances(_player);
		if (vengeances == null) {
			buffer.writeByte(0x01); // cCurrentPage
			buffer.writeByte(0x01); // cMaxPage
			buffer.writeInt(0x00);
		} else {
			buffer.writeByte(0x01); // cCurrentPage
			buffer.writeByte(0x01); // cMaxPage
			buffer.writeInt(vengeances.size());
			for (VengeanceHolder holder : vengeances) {
				buffer.writeInt(holder.getType().ordinal()); // var int nShareType; [2 - help request, 1 - vengeance, 0 - Vengeance + Help Request]
				buffer.writeInt((int) (holder.getKillTime() / 1000)); // var int nKilledTime;
				buffer.writeInt(holder.getShowLocationRemaining()); // var int nShowKillerCount;
				buffer.writeInt(holder.getTeleportRemaining()); // var int nTeleportKillerCount;
				buffer.writeInt(holder.getSharedTeleportRemaining()); // var int nSharedTeleportKillerCount;
				buffer.writeInt(0x00); // var int nKilledUserDBID;
				buffer.writeSizedString(holder.getVictimName()); // var string sKilledUserName;
				buffer.writeSizedString(holder.getVictimClanName()); // var string sKilledUserPledgeName;
				buffer.writeInt(holder.getVictimLevel()); // var int nKilledUserLevel;
				buffer.writeInt(holder.getVictimRaceId()); // var int nKilledUserRace;
				buffer.writeInt(holder.getVictimClassId()); // var int nKilledUserClass;
				buffer.writeInt(0x00); // var int nKillUserDBID;
				buffer.writeSizedString(holder.getKillerName()); // var string sKillUserName;
				buffer.writeSizedString(holder.getKillerClanName()); // var string sKillUserPledgeName;
				buffer.writeInt(holder.getKillerLevel()); // var int nKillUserLevel;
				buffer.writeInt(holder.getKillerRaceId()); // var int nKillUserRace;
				buffer.writeInt(holder.getKillerClassId()); // var int nKillUserClass;
				PlayerInstance killer = World.getInstance().getPlayer(holder.getKillerName());
				buffer.writeInt((killer != null) && killer.isOnline() ? 0x02 : 0x00); // var int nKillUserOnline; [2 - online, 0 - offline]
				// TODO:
				// — if a character who is affected by Einhasad Overseeing Lv. 3+ kills a peaceful character, the characters who have accepted the help request can teleport to the vengeance spot for free;
				// — if the hostile character is affected by Einhasad Overseeing Lv. 3+ at the moment of the killing, vengeance teleport is free even if the character's reputation is reset after the killing.
				buffer.writeInt(0x00); // nKillUserKarma
				buffer.writeInt((int) (holder.getShareTime() / 1000)); // var int nSharedTime;
			}
		}
	}
}