/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.events.impl.creature.OnCreatureMDefChange;
import club.projectessence.gameserver.model.events.listeners.ConsumerEventListener;
import club.projectessence.gameserver.model.holders.SkillHolder;
import club.projectessence.gameserver.model.holders.TriggerSkillInfoHolder;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.ConnectionState;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class TriggerSkillByMDef extends AbstractEffect {
	private final List<TriggerSkillInfoHolder> _skills = new ArrayList<>();

	public TriggerSkillByMDef(StatSet params) {
		String skillsArr[] = params.getString("skills").split(";");
		for (String str : skillsArr) {
			String paramArr[] = str.split(",");
			if (paramArr.length < 4) {
				LOGGER.info(getClass().getSimpleName() + ":  not enough param arguments (" + str + ")");
				continue;
			}
			int skillId = Integer.parseInt(paramArr[0]);
			int skillLevel = Integer.parseInt(paramArr[1]);
			int minMDef = Integer.parseInt(paramArr[2]);
			int maxMDef = Integer.parseInt(paramArr[3]);
			_skills.add(new TriggerSkillInfoHolder(new SkillHolder(skillId, skillLevel), minMDef, maxMDef));
		}
	}

	@Override
	public void onStart(Creature effector, Creature effected, Skill skill, ItemInstance item) {
		if (effected.isPlayer()) {
			final PlayerInstance player = effected.getActingPlayer();
			if ((player.getClient() == null) || (player.getClient().getConnectionState() != ConnectionState.IN_GAME)) {
				ThreadPool.get().schedule(() -> onStart(effector, effected, skill, item), 1000);
				return;
			}
		}
		if ((skill == null) || !effected.isAffectedBySkill(skill.getId())) {
			return;
		}
		onMDefChange(effected, effected.getMDef());
		// Register listeners
		final ListenersContainer container = effected;
		container.addListener(new ConsumerEventListener(container, EventType.ON_CREATURE_MDEF_CHANGE, (OnCreatureMDefChange event) -> onMDefChange(event), this));
	}

	@Override
	public void onExit(Creature effector, Creature effected, Skill skill) {
		for (TriggerSkillInfoHolder sk : _skills) {
			effected.stopSkillEffects(sk.getSkillHolder().getSkill());
		}
		effected.removeListenerIf(listener -> listener.getOwner() == this);
	}

	private void onMDefChange(OnCreatureMDefChange event) {
		onMDefChange(event.getCreature(), event.getNewMDef());
	}

	public void onMDefChange(Creature creature, int newMDef) {
		if (Config.NO_TRIGGER_EFFECTS) {
			return;
		}
		for (TriggerSkillInfoHolder skill : _skills) {
			if ((newMDef < skill.getMinVal()) || (newMDef > skill.getMaxVal())) {
				int buffLv = creature.getAffectedSkillLevel(skill.getSkillHolder().getSkillId());
				if ((buffLv > 0) & (buffLv == skill.getSkillHolder().getSkillLevel())) {
					creature.stopSkillEffects(skill.getSkillHolder().getSkill());
				}
			} else if (creature.getAffectedSkillLevel(skill.getSkillHolder().getSkillId()) != skill.getSkillHolder().getSkillLevel()) {
				skill.getSkillHolder().getSkill().applyEffects(creature, creature);
			}
		}
	}
}
