/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.effects;

/**
 * Effect types.
 *
 * <AUTHOR>
 */
public enum EffectType {
	AGGRESSION,
	CHARM_OF_LUCK,
	CPHEAL,
	DISPEL,
	DISPEL_BY_SLOT,
	DMG_OVER_TIME,
	DMG_OVER_TIME_PERCENT,
	MAGICAL_DMG_OVER_TIME,
	DEATH_LINK,
	<PERSON><PERSON><PERSON><PERSON>_CONTROL,
	EXTRACT_ITEM,
	FISHING,
	FIS<PERSON>ING_START,
	HATE,
	HEAL,
	HP_DRAIN,
	MAGICAL_ATTACK,
	MANAHEAL_BY_LEVEL,
	MANAHEAL_PERCENT,
	MUTE,
	NOBLESSE_BLESSING,
	NONE,
	PHYSICAL_ATTACK,
	PHYSICAL_ATTACK_HP_LINK,
	LETHAL_ATTACK,
	REGULAR_ATTACK,
	REBALANCE_HP,
	REFUEL_AIRSHIP,
	RELAXING,
	RESURRECTION,
	RESURRECTION_SPECIAL,
	ROOT,
	SLEEP,
	STEAL_ABNORMAL,
	BLOCK_ACTIONS,
	SUMMON,
	SUMMON_PET,
	SUMMON_NPC,
	TELEPORT,
	TELEPORT_TO_TARGET,
	ABNORMAL_SHIELD
}