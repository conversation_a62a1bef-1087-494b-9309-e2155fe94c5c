/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.vipsystem;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.VipSystemManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

/**
 * <AUTHOR>
 */
public class ReceiveVipInfo extends ServerPacket {
	private final int _vipTier;
	private final long _vipPoints;
	private final int _vipDuration;
	private final long _pointsDepreciatedOnLevel;
	private final long _pointsToLevel;
	private final long _pointsToLevelNext;


	public ReceiveVipInfo(PlayerInstance player) {
		final VipSystemManager vipManager = VipSystemManager.getInstance();
		_vipTier = player.getVipTier();
		_vipPoints = player.getVipPoints();
		_vipDuration = (int) Math.max(0, ChronoUnit.SECONDS.between(Instant.now(), Instant.ofEpochMilli(player.getVipTierExpiration())));
		_pointsToLevel = vipManager.getPointsToLevel(_vipTier);
		_pointsToLevelNext = vipManager.getPointsToLevel(_vipTier + 1);
		_pointsDepreciatedOnLevel = vipManager.getPointsDepreciatedOnLevel(_vipTier);
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.RECIVE_VIP_INFO, buffer);
		buffer.writeByte(_vipTier);
		buffer.writeLong(_vipPoints);
		buffer.writeInt(_vipDuration);
		buffer.writeLong(_pointsToLevelNext);
		buffer.writeLong(_pointsDepreciatedOnLevel);
		buffer.writeByte(_vipTier);
		buffer.writeLong(_pointsToLevel);
	}
}