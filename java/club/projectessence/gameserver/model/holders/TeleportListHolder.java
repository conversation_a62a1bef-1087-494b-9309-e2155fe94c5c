/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.holders;

import club.projectessence.commons.util.Rnd;
import club.projectessence.gameserver.model.Location;

import java.util.List;

/**
 * <AUTHOR>
 */
public class TeleportListHolder {
	private final int _locId;
	private final int _price;
	private final int _siegeId;
	private final boolean _special;
	private final List<Location> _locations;

	public TeleportListHolder(int locId, List<Location> locations, int price, int siegeId, boolean special) {
		_locId = locId;
		_price = price;
		_siegeId = siegeId;
		_special = special;
		_locations = locations;
	}

	public int getLocId() {
		return _locId;
	}

	public List<Location> getLocations() {
		return _locations;
	}

	public int getPrice() {
		return _price;
	}

	public int getSiegeId() {
		return _siegeId;
	}

	public boolean isSpecial() {
		return _special;
	}

	public Location getLocation() {
		return _locations.get(Rnd.get(_locations.size()));
	}
}