/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

/**
 * @version $Revision: 1.1.2.1.2.4 $ $Date: 2005/03/27 15:29:30 $
 */
public class CannotMoveAnymore extends ClientPacket {
	private int _x;
	private int _y;
	private int _z;
	private int _heading;

	@Override
	public void readImpl() {
		_x = readInt();
		_y = readInt();
		_z = readInt();
		_heading = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (player.getAI() != null) {
			player.getAI().notifyEvent(CtrlEvent.EVT_ARRIVED_BLOCKED, new Location(_x, _y, _z, _heading));
		}
		/*
		 * if (player.getParty() != null) { player.getParty().broadcastToPartyMembers(player, new PartyMemberPosition(player)); }
		 */

		// player.stopMove();
		//
		// if (Config.DEBUG)
		// LOGGER.finer("client: x:"+_x+" y:"+_y+" z:"+_z+
		// " server x:"+player.getX()+" y:"+player.getZ()+" z:"+player.getZ());
		// StopMove smwl = new StopMove(player);
		// client.getPlayer().sendPacket(smwl);
		// client.getPlayer().broadcastPacket(smwl);
		//
		// StopRotation sr = new StopRotation(client.getPlayer(),
		// _heading);
		// client.getPlayer().sendPacket(sr);
		// client.getPlayer().broadcastPacket(sr);
	}
}
