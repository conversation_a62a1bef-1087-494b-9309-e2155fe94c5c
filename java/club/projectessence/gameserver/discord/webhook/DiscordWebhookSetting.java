package club.projectessence.gameserver.discord.webhook;

public class DiscordWebhookSetting {
	private String _webhook = "";
	private boolean _notif_death = false;

	private long _lastNotificationTimeStamp = 0;

	public DiscordWebhookSetting(String webhook) {
		_webhook = webhook;
	}

	public DiscordWebhookSetting(String webhook, boolean notif_death) {
		_webhook = webhook;
		_notif_death = notif_death;
	}

	public String getWebhook() {
		return _webhook;
	}

	public void setWebhook(String webhook) {
		_webhook = webhook;
	}

	public boolean isDeathNotif() {
		return _notif_death;
	}

	public void setDeathNotif(boolean val) {
		_notif_death = val;
	}

	public long getLastNotificationTimeStamp() {
		return _lastNotificationTimeStamp;
	}

	public void setLastNotificationTimeStamp(long value) {
		_lastNotificationTimeStamp = value;
	}
}