/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.newhenna;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.UserInfo;
import club.projectessence.gameserver.network.serverpackets.newhenna.ExNewHennaEquip;
import club.projectessence.gameserver.util.Util;

/**
 * <AUTHOR>
 */
public class ExRequestNewHennaEquip extends ClientPacket
{
	private int	_slotId;
	private int	_itemObjId;
	
	@Override
	public void readImpl()
	{
		_slotId = readByte(); // cSlotID
		_itemObjId = readInt(); // nItemSid
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (!client.getFloodProtectors().getTransaction().tryPerformAction("NewHennaEquip"))
		{
			return;
		}
		if (player.isAccountLockedDown())
		{
			player.sendMessage("Your account is in lockdown");
			return;
		}
		if (player.getHennaEmptySlots() == 0)
		{
			player.sendPacket(SystemMessageId.NO_SLOT_EXISTS_TO_DRAW_THE_SYMBOL);
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		final ItemInstance item = player.getInventory().getItemByObjectId(_itemObjId);
		if (item == null)
		{
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendPacket(new ExNewHennaEquip(_slotId, 0, false));
			return;
		}
		final Henna henna = HennaData.getInstance().getHennaByItemId(item.getId());
		if (henna == null)
		{
			LOGGER.warning("Invalid Henna Id: " + item.getId() + " from player " + player);
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendPacket(new ExNewHennaEquip(_slotId, 0, false));
			return;
		}
		final long _count = player.getInventory().getInventoryItemCount(henna.getDyeItemId(), -1);
		if (henna.isAllowedClass(player) && (_count >= henna.getWearCount()) && (player.getAdena() >= henna.getWearFee()) && player.addHenna(_slotId, henna))
		{
			player.destroyItemByItemId("Henna", henna.getDyeItemId(), henna.getWearCount(), player, true);
			player.getInventory().reduceAdena("Henna", henna.getWearFee(), player, player.getLastFolkNPC());
			if (player.getAdena() > 0)
			{
				final InventoryUpdate iu = new InventoryUpdate();
				iu.addModifiedItem(player.getInventory().getAdenaInstance());
				player.sendInventoryUpdate(iu);
			}
			player.sendPacket(new ExNewHennaEquip(_slotId, henna.getDyeId(), true));
			player.sendPacket(SystemMessageId.THE_SYMBOL_HAS_BEEN_ADDED);
			player.getStat().recalculateStats(true);
			player.sendPacket(new UserInfo(player));
		}
		else
		{
			player.sendPacket(SystemMessageId.THE_SYMBOL_CANNOT_BE_DRAWN);
			if (!player.canOverrideCond(PlayerCondOverride.ITEM_CONDITIONS) && !henna.isAllowedClass(player))
			{
				Util.handleIllegalPlayerAction(player, "Exploit attempt: Character " + player.getName() + " of account " + player.getAccountName() + " tryed to add a forbidden henna.", Config.DEFAULT_PUNISH);
			}
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendPacket(new ExNewHennaEquip(_slotId, henna.getDyeId(), false));
		}
	}
}
