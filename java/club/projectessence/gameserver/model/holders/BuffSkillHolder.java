/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.holders;

/**
 * A container used for schemes buffer.
 */
public class BuffSkillHolder {
	private final int _id;
	private final int _level;
	private final int _price;
	private final String _type;
	private final String _description;

	public BuffSkillHolder(int id, int level, int price, String type, String description) {
		_id = id;
		_level = level;
		_price = price;
		_type = type;
		_description = description;
	}

	public int getId() {
		return _id;
	}

	public int getLevel() {
		return _level;
	}

	public int getPrice() {
		return _price;
	}

	public String getType() {
		return _type;
	}

	public String getDescription() {
		return _description;
	}
}