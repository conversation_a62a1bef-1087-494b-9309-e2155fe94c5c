/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.shuttle;

import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.type.WeaponType;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.shuttle.ExMoveToLocationInShuttle;
import club.projectessence.gameserver.network.serverpackets.shuttle.ExStopMoveInShuttle;

/**
 * <AUTHOR>
 */
public class MoveToLocationInShuttle extends ClientPacket {
	private int _boatId;
	private int _targetX;
	private int _targetY;
	private int _targetZ;
	private int _originX;
	private int _originY;
	private int _originZ;

	@Override
	public void readImpl() {
		_boatId = readInt(); // objectId of boat
		_targetX = readInt();
		_targetY = readInt();
		_targetZ = readInt();
		_originX = readInt();
		_originY = readInt();
		_originZ = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if ((_targetX == _originX) && (_targetY == _originY) && (_targetZ == _originZ)) {
			player.sendPacket(new ExStopMoveInShuttle(player, _boatId));
			return;
		}

		if (player.isAttackingNow() && (player.getActiveWeaponItem() != null) && (player.getActiveWeaponItem().getItemType() == WeaponType.BOW)) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		if (player.isSitting() || player.isMovementDisabled()) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		player.setInVehiclePosition(new Location(_targetX, _targetY, _targetZ));
		player.broadcastPacket(new ExMoveToLocationInShuttle(player, _originX, _originY, _originZ));
	}
}
