/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.network.serverpackets.SiegeDefenderList;

/**
 * @version $Revision: 1.3.4.2 $ $Date: 2005/03/27 15:29:30 $
 */
public class RequestSiegeDefenderList extends ClientPacket {
	private int _castleId;

	@Override
	public void readImpl() {
		_castleId = readInt();
	}

	@Override
	public void runImpl() {
		final Castle castle = CastleManager.getInstance().getCastleById(_castleId);
		if (castle == null) {
			return;
		}
		client.sendPacket(new SiegeDefenderList(castle));
	}
}
