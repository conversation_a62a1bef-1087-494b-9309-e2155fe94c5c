/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.data.xml.HennaData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.henna.Henna;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.HennaItemRemoveInfo;

/**
 * <AUTHOR>
 */
public class RequestHennaItemRemoveInfo extends ClientPacket {
	private int _symbolId;

	@Override
	public void readImpl() {
		_symbolId = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if ((player == null) || (_symbolId == 0)) {
			return;
		}

		final Henna henna = HennaData.getInstance().getHennaByDyeId(_symbolId);
		if (henna == null) {
			LOGGER.warning("Invalid Henna Id: " + _symbolId + " from player " + player);
			client.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		player.sendPacket(new HennaItemRemoveInfo(henna, player));
	}
}
