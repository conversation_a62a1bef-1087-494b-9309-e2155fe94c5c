/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.effecthandlers;

import club.projectessence.gameserver.data.xml.TranscendentInstanceData;
import club.projectessence.gameserver.enums.Position;
import club.projectessence.gameserver.enums.ShotType;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.actor.Attackable;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.AbstractEffect;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.effects.EffectType;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.stats.Formulas;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.util.Util;

/**
 * Physical Attack HP Link effect implementation.<br>
 * <b>Note</b>: Initial formula taken from PhysicalAttack.
 *
 * <AUTHOR> Nik
 */
public class PhysicalAttackHpLink extends AbstractEffect
{
	private final double	_power;
	private final double	_criticalChance;
	private final boolean	_overHit;
	
	public PhysicalAttackHpLink(StatSet params)
	{
		_power = params.getDouble("power", 0);
		_criticalChance = params.getDouble("criticalChance", 0);
		_overHit = params.getBoolean("overHit", false);
	}
	
	@Override
	public boolean calcSuccess(Creature effector, Creature effected, Skill skill)
	{
		return !Formulas.calcSkillEvasion(effector, effected, skill);
	}
	
	@Override
	public EffectType getEffectType()
	{
		return EffectType.PHYSICAL_ATTACK_HP_LINK;
	}
	
	@Override
	public boolean isInstant()
	{
		return true;
	}
	
	@Override
	public void instant(Creature effector, Creature effected, Skill skill, ItemInstance item)
	{
		if (effector.isAlikeDead())
		{
			return;
		}
		if ((_overHit || (effector.isAffected(EffectFlag.TRANSCENDENT_SKILLS) && TranscendentInstanceData.getInstance().isTranscendentSkill(skill.getId()))) && effected.isAttackable())
		{
			((Attackable) effected).overhitEnabled(true);
		}
		final double attack = effector.getPAtk();
		double defence = effected.getPDef();
		// Apply PDEF_REDUCE
		double pDefReduce = effector.getStat().getValue(Stat.PDEF_REDUCE, 0);
		if (pDefReduce != 0)
		{
			defence = Math.max(defence - pDefReduce, 0);
		}
		switch (Formulas.calcShldUse(effector, effected))
		{
			case Formulas.SHIELD_DEFENSE_SUCCEED:
			{
				defence += effected.getShldDef();
				break;
			}
			case Formulas.SHIELD_DEFENSE_PERFECT_BLOCK:
			{
				defence = -1;
				break;
			}
		}
		double damage = 1;
		final boolean critical = Formulas.calcCrit(_criticalChance, effector, effected, skill);
		if (defence != -1)
		{
			// Trait, elements
			final double weaponTraitMod = Formulas.calcWeaponTraitBonus(effector, effected);
			final double generalTraitMod = Formulas.calcGeneralTraitBonus(effector, effected, skill.getTraitType(), true);
			final double weaknessMod = Formulas.calcWeaknessBonus(effector, effected, skill.getTraitType());
			final double attributeMod = Formulas.calcAttributeBonus(effector, effected, skill);
			final double pvpPveMod = Formulas.calculatePvpPveBonusMod(effector, effected, skill, true);
			final double randomMod = effector.getRandomDamageMultiplier();
			final double pvpDamageDecrease = Formulas.calculatePvpDamageDecrease(effector, effected);
			final double aoeDamageMod = Formulas.caculateAoeDamageMod(effector, effected, skill);
			// Skill specific mods
			final double weaponMod = effector.getAttackType().isRanged() ? 70 : 77;
			final double power = _power + effector.getStat().getValue(Stat.SKILL_POWER_ADD, 0);
			final double rangedBonus = effector.getAttackType().isRanged() ? attack + power : 0;
			final double critMod = critical ? Formulas.calcCritDamage(effector, effected, skill, false) : 1;
			final double cAtkAdd = critical ? Formulas.calcCritDamageAdd(effector, effected, skill) : 0;
			double ssmod = 1;
			if (skill.useSoulShot())
			{
				if (effector.isChargedShot(ShotType.SOULSHOTS))
				{
					ssmod = 2 * (effector.getStat().getValue(Stat.SHOTS_BONUS) + (effector.getStat().getValue(Stat.SOULSHOT_DAMAGE, 1) - 1) + (effected.getStat().getValue(Stat.SOULSHOT_DAMAGE_RESISTANCE, 1) - 1));
				}
				else if (effector.isChargedShot(ShotType.BLESSED_SOULSHOTS))
				{
					ssmod = 4 * (effector.getStat().getValue(Stat.SHOTS_BONUS) + (effector.getStat().getValue(Stat.SOULSHOT_DAMAGE, 1) - 1) + (effected.getStat().getValue(Stat.SOULSHOT_DAMAGE_RESISTANCE, 1) - 1));
				}
			}
			// Base damage calculation
			final double baseMod = (weaponMod * ((attack * effector.getLevelMod()) + power + rangedBonus)) / defence;
			damage = baseMod * ssmod * critMod * weaponTraitMod * generalTraitMod * weaknessMod * attributeMod * pvpPveMod * randomMod * aoeDamageMod;
			damage *= effector.getStat().getValue(Stat.PHYSICAL_SKILL_POWER, 1);
			// Apply RANGE_DMG_DIST_BOOST_SKILL
			final boolean isRanged = effector.getAttackType().isRanged();
			if (isRanged)
			{
				double rangedAtkBoost = effector.getStat().getValue(Stat.RANGE_DMG_DIST_BOOST_SKILL, 0);
				if (rangedAtkBoost > 0)
				{
					int distToTarg = (int) Util.calculateDistance(effector, effected, false, false);
					if (distToTarg > 40)
					{
						distToTarg -= 40;
						distToTarg = (int) Math.pow(distToTarg, 1.15);
						damage *= (distToTarg * rangedAtkBoost) + 1;
					}
				}
			}
			// Apply HP link damage modifier
			damage += damage * ((-((effector.getCurrentHp() * 2) / effector.getMaxHp()) + 2) / 2.0);
			// Apply armor type modifiers
			double armorTypeMod = 1;
			if (effected.isPlayer())
			{
				PlayerInstance targetPlayer = effected.getActingPlayer();
				if (targetPlayer.isWearingHeavyArmor())
				{
					armorTypeMod = effector.getStat().getValue(Stat.HEAVY_DAM_MUL, 1);
				}
				else if (targetPlayer.isWearingLightArmor())
				{
					armorTypeMod = effector.getStat().getValue(Stat.LIGHT_DAM_MUL, 1);
				}
				else
				{
					armorTypeMod = effector.getStat().getValue(Stat.ROBE_DAM_MUL, 1);
				}
			}
			damage *= armorTypeMod;
			// Apply position defence modifier
			double positionDefenceMod = 1;
			if (Position.getPosition(effector, effected) == Position.BACK)
			{
				positionDefenceMod = effected.getStat().getValue(Stat.POWER_DEFENCE_BEHIND, 1.0);
			}
			damage *= positionDefenceMod;
			// Apply HP/MP/CP damage modifiers
			double hpDamMod = effector.getStat().getValue(Stat.INC_DAM_HP, 0);
			if (hpDamMod != 0)
			{
				double hpRatio = 1 - (effected.getCurrentHp() / effected.getMaxHp());
				damage += damage * hpRatio * hpDamMod;
			}
			double mpDamMod = effector.getStat().getValue(Stat.INC_DAM_MP, 0);
			if (mpDamMod != 0)
			{
				double mpRatio = 1 - (effected.getCurrentMp() / effected.getMaxMp());
				damage += damage * mpRatio * mpDamMod;
			}
			if (effected.isPlayer())
			{
				double cpDamMod = effector.getStat().getValue(Stat.INC_DAM_CP, 0);
				if (cpDamMod != 0)
				{
					double cpRatio = 1 - (effected.getCurrentCp() / effected.getMaxCp());
					damage += damage * cpRatio * cpDamMod;
				}
			}
			// Apply DMG_ADD and DMG_REMOVE
			double dmgAdd = effector.getStat().getValue(Stat.DMG_ADD, 0);
			if (dmgAdd != 0)
			{
				damage += dmgAdd;
			}
			double dmgRemove = effected.getStat().getValue(Stat.DMG_REMOVE, 0);
			if (dmgRemove != 0)
			{
				damage -= dmgRemove;
			}
			damage += cAtkAdd - pvpDamageDecrease;
			// Apply PDAM_MAX limit
			final double pdamMax = effected.getStat().getValue(Stat.PDAM_MAX, 0);
			if (pdamMax > 0)
			{
				damage = Math.min(damage, pdamMax);
			}
			// Apply SAME_RACE_DMG_VUL and HATED_RACE_DMG_BOOST
			if (effector instanceof PlayerInstance && effected instanceof PlayerInstance)
			{
				if (effector.getRace() == effected.getRace())
				{
					damage *= effected.getStat().getValue(Stat.SAME_RACE_DMG_VUL, 1);
				}
				else if (((PlayerInstance) effector).hatesRace(effected))
				{
					damage *= effector.getStat().getValue(Stat.HATED_RACE_DMG_BOOST, 1);
				}
			}

			// Balance system now applied at base stats level in IStatFunction
			// No additional PvP damage modification needed
		}
		final double initialDamage = damage;
		if (effector.isPlayer())
		{
			final PlayerInstance player = effector.getActingPlayer();
			if (effector.getAttackType().isRanged())
			{
				if (!player.getClassId().isArcher())
				{
					damage *= 0.5;
				}
			}
			damage += getStrBonusDamage(player, initialDamage);
		}
		if (effected.isPlayer())
		{
			final PlayerInstance player = effected.getActingPlayer();
			damage -= getConBonusDefense(player, initialDamage);
		}
		effector.doAttack(damage, effected, skill, false, false, critical, false);
	}
}
