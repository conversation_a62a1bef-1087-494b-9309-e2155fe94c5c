/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.effects;

/**
 * <AUTHOR>
 */
public enum EffectFlag
{
	NONE,
	RESURRECTION_SPECIAL,
	NOBLESS_BLESSING,
	SILENT_MOVE,
	PROTECTION_BLESSING,
	RELAXING,
	BLOCK_CONTROL,
	CONFUSED,
	MUTED,
	PSYCHICAL_MUTED,
	PSYCHICAL_ATTACK_MUTED,
	PASSIVE,
	DISARMED,
	ROOTED,
	BLOCK_ACTIONS,
	CONDITIONAL_BLOCK_ACTIONS,
	BETRAYED,
	HP_BLOCK,
	MP_BLOCK,
	BUFF_BLOCK,
	DEBUFF_BLOCK,
	ABNORMAL_SHIELD,
	BLOCK_RESURRECTION,
	UNTARGETABLE,
	CANNOT_ESCAPE,
	DOUBLE_CAST,
	ATTACK_BEHIND,
	TARGETING_DISABLED,
	FACEOFF,
	PHYSICAL_SHIELD_ANGLE_ALL,
	NO_ARROW_MP_COST,
	IGNORE_DEATH,
	HPCPHEAL_CRITICAL,
	PROTECT_DEATH_PENALTY,
	CHAT_BLOCK,
	FAKE_DEATH,
	DUELIST_FURY,
	FEAR,
	TRANSCENDENT_SKILLS,
	KAMAEL_SHADOW_TRANSFORM_SKILLS_REPLACE,
	BORN_TO_BE_DEAD,
	REVIVING_AFTER_DEATH,
	FORBIDDEN_TO_DIE,
	EXTRA_HIT,
	DEBUFF_MASTER,
	FORTUNE_SEEKERS_MARK,
	CHAIN_STRIKE_SHOCK,
	SHADOW_ASSAULT,
	BLUFF_UPGRADE,
	CRITICAL_WOUND_UPGRADE,
	REVERSED_PULL,
	EVENT_LEOGUL_SPAWN,
	EVENT_HALLOWEEN_APPEARANCE,
	BACKSTAB_MASTERY,
	BLAZING_BEAST,
	DARKNESS_UNLEASHING,
	WILD_EVOLUTION,
	SAYHA_SUSTENTION,
	MANA_GAIN,
	HIGHER_MANA_GAIN,
	STIGMA_OF_DEATH,
	LEGENDARY_ARCHER,
	DEXTEROUS_BODY_ENHANCEMENT,
	FLAMING_BODY_DP,
	ACCELERATION_DP,
	EVOLUTION_DEFENSE,
	EVOLUTION_POWER,
	UNDYING_BODY_3,
	UNDYING_BODY_4,
	UNDYING_BODY_5,
	UNDERGROUND_LABYRINTH,
	ADDITIONAL_ATTACKS_SILENCE,
	CRUSADERS_RED_ENERGY,
	ESSENTIAL_ENERGY,
	RESONANCE,
	LIGHTNING_MASTERY,
	CHALLENGER_TRANSFORM,
	SWORD_SPIRIT_CHALLENGER,
	AMADEUS,
	FLAMENCO,
	DANCERS_STIGMA,
	SWORD_MUSES_STIGMA,
	EXTENDED_LIFE_1,
	EXTENDED_LIFE_2,
	EXTENDED_LIFE_3,
	EXTENDED_LIFE_4,
	EXTENDED_LIFE_5,
	OVERWHELMING_FORCE,
	POWERFUL_DISARM,
	POWERFUL_RUSH,
	MASTER_OF_AGGRESSION_1,
	MASTER_OF_AGGRESSION_2,
	GLAKIAS_DOLL_SORCERY_1,
	GLAKIAS_DOLL_SORCERY_2,
	GLAKIAS_DOLL_SORCERY_3,
	GLAKIAS_DOLL_SORCERY_4,
	GLAKIAS_DOLL_SORCERY_5,
	RARE_CRAFT,
	IGNORE_ELEMENTAL;
	
	public long getMask()
	{
		return 1L << (ordinal() > 62 ? (ordinal() % 62) - 1 : ordinal());
	}
	
	public int getEffectFlagsIndex()
	{
		return ordinal() / 62;
	}
}
