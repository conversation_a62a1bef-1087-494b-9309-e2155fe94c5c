package club.projectessence.gameserver.handler;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.data.xml.ClassItemManager;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import gabriel.eventEngine.events.GabrielEventsLoader;
import gabriel.eventEngine.events.engine.EventManager;
import gabriel.eventEngine.events.engine.base.EventType;
import gabriel.eventEngine.events.engine.base.PvPEventPlayerData;
import gabriel.eventEngine.events.engine.main.events.BattleRoyale;
import gabriel.eventEngine.interf.PlayerEventInfo;

public class BattleRoyalItemHandler implements IItemHandler
{
	private static final Logger					LOGGER			= Logger.getLogger(BattleRoyalItemHandler.class.getName());
	private static final int					ITEMS_PER_PAGE	= 5;
	private static final long					PURCHASE_COOLDOWN = 1000; // 1 second cooldown between purchases
	private static final Map<Integer, Integer>	weaponPages		= new ConcurrentHashMap<>();
	private static final Map<Integer, Integer>	armorPages		= new ConcurrentHashMap<>();
	private static final Map<Integer, Long>		lastPurchaseTime = new ConcurrentHashMap<>();
	
	@Override
	public boolean useItem(Playable playable, ItemInstance item, boolean forceUse)
	{
		if (!playable.isPlayer())
		{
			playable.sendPacket(SystemMessageId.YOUR_PET_CANNOT_CARRY_THIS_ITEM);
			return false;
		}
		final PlayerInstance player = (PlayerInstance) playable;

		// Safety check before any item operations to prevent client crashes
		if (!canSafelyUseItem(player, item))
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("DEBUG: Safety check failed for item " + (item != null ? item.getId() : "null"));
			}
			player.sendMessage("Cannot use item at this time. Please try again.");
			return false;
		}

		final PlayerEventInfo playerInfo = player.getEventInfo();
		if (GabrielEventsLoader.detailedDebug)
		{
			LOGGER.info("Player " + player.getName() + " used BattleRoyalItemHandler (ID: 76000), instanceId: " + playerInfo.getInstanceId());
		}
		BattleRoyale event = (BattleRoyale) EventManager.getInstance().getMainEventManager().getCurrent();
		if (event == null)
		{
			player.sendMessage("You can only use this item during a Battle Royale event! (Event is null)");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " cannot use item: Event is null");
			}
			return false;
		}
		if (GabrielEventsLoader.detailedDebug)
		{
			LOGGER.info("Current event type: " + event.getEventType() + ", expected type: " + EventType.BattleRoyales);
			LOGGER.info("Player instanceId: " + playerInfo.getInstanceId() + ", players in instance: " + event.getPlayers(playerInfo.getInstanceId()));
			LOGGER.info("Does instance contain player? " + event.getPlayers(playerInfo.getInstanceId()).contains(playerInfo));
		}
		if (!event.getEventType().equals(EventType.BattleRoyales))
		{
			player.sendMessage("You can only use this item during a Battle Royale event! (Event type mismatch)");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " cannot use item: Event type mismatch (found: " + event.getEventType() + ", expected: " + EventType.BattleRoyales + ")");
			}
			return false;
		}
		if (!event.getPlayers(playerInfo.getInstanceId()).contains(playerInfo))
		{
			player.sendMessage("You can only use this item during a Battle Royale event! (Player not in event instance)");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " cannot use item: Player not in event instance (instanceId: " + playerInfo.getInstanceId() + ")");
			}
			return false;
		}
		BattleRoyale.BRData eventData = event.getEventData(playerInfo.getInstanceId());
		if (eventData == null)
		{
			player.sendMessage("Event data not found!");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " cannot use item: Event data not found");
			}
			return false;
		}
		int stageNumber = eventData.getCurrentStageIndex() >= 0 ? eventData.getStages().get(eventData.getCurrentStageIndex()).getStageNumber() : 0;
		String selectedGrade;
		switch (stageNumber)
		{
			case 0: // Stage 0 (lobby) and Stage 1
			case 1:
				selectedGrade = "C";
				break;
			case 2:
				selectedGrade = "B";
				break;
			case 3:
				selectedGrade = "A";
				break;
			case 4:
			case 5:
				selectedGrade = "special";
				break;
			default:
				player.sendMessage("Invalid stage! Cannot display items.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("Player " + player.getName() + " cannot use item: Invalid stage (" + stageNumber + ")");
				}
				return false;
		}
		int classId = player.getClassId().getId();
		List<String> weaponsList = new ArrayList<>();
		List<String> armorsList = new ArrayList<>();
		Set<String> uniqueArmorNames = new HashSet<>();
		Set<Integer> purchasedItems = eventData.getPlayerItemIds(playerInfo);
		boolean hasPurchasedWeaponInGrade = false;
		boolean hasPurchasedArmorInGrade = false;
		Map<String, ClassItemManager.GradeItems> allWeaponsMap = ClassItemManager.getInstance().getGradeItems(classId, "weapons");
		Map<String, ClassItemManager.GradeItems> allArmorsMap = ClassItemManager.getInstance().getGradeItems(classId, "armors");
		if (allWeaponsMap != null && !allWeaponsMap.isEmpty())
		{
			ClassItemManager.GradeItems gradeItems = allWeaponsMap.get(selectedGrade);
			if (gradeItems != null && gradeItems.getItems() != null)
			{
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (purchasedItems.contains(itemHolder.getId()))
					{
						hasPurchasedWeaponInGrade = true;
						break;
					}
				}
			}
		}
		if (allArmorsMap != null && !allArmorsMap.isEmpty())
		{
			ClassItemManager.GradeItems gradeItems = allArmorsMap.get(selectedGrade);
			if (gradeItems != null && gradeItems.getItems() != null)
			{
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (purchasedItems.contains(itemHolder.getId()))
					{
						hasPurchasedArmorInGrade = true;
						break;
					}
				}
			}
		}
		if (!hasPurchasedWeaponInGrade)
		{
			Map<String, ClassItemManager.GradeItems> weaponsMap = ClassItemManager.getInstance().getGradeItems(classId, "weapons");
			if (weaponsMap != null && !weaponsMap.isEmpty())
			{
				ClassItemManager.GradeItems gradeItems = weaponsMap.get(selectedGrade);
				if (gradeItems != null && gradeItems.getItems() != null && !gradeItems.getItems().isEmpty())
				{
					for (ItemHolder itemHolder : gradeItems.getItems())
					{
						Item itemTemplate = ItemTable.getInstance().getTemplate(itemHolder.getId());
						if (itemTemplate != null)
						{
							int price = (int) itemHolder.getPrice();
							String icon = itemTemplate.getIcon() != null ? itemTemplate.getIcon() : "icon.etc_quest_rotten_bread_i00";
							String entry = "<table width=350 border=0>" + "<tr>" + "<td width=32><img src=\"" + icon + "\" width=32 height=32></td>" + "<td width=300><button value=\"" + itemTemplate.getName() + " (" + price + " Points)\" action=\"bypass -h event_buy_item weapon_" + itemHolder.getId() + "_" + price + "\" width=200 height=40 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\"></td>" + "</tr>" + "</table>";
							weaponsList.add(entry);
						}
					}
				}
				else if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("No weapons available for grade " + selectedGrade + " for classId " + classId);
				}
			}
			else if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Weapons map is empty or null for classId " + classId);
			}
		}
		if (!hasPurchasedArmorInGrade)
		{
			Map<String, ClassItemManager.GradeItems> armorsMap = ClassItemManager.getInstance().getGradeItems(classId, "armors");
			if (armorsMap != null && !armorsMap.isEmpty())
			{
				ClassItemManager.GradeItems gradeItems = armorsMap.get(selectedGrade);
				if (gradeItems != null && gradeItems.getItems() != null && !gradeItems.getItems().isEmpty())
				{
					List<ItemHolder> uniqueItems = new ArrayList<>();
					Set<String> seenNames = new HashSet<>();
					for (ItemHolder itemHolder : gradeItems.getItems())
					{
						String itemName = itemHolder.getName() != null ? itemHolder.getName() : "";
						if (seenNames.add(itemName + "|" + itemHolder.getId()))
						{
							uniqueItems.add(itemHolder);
						}
					}
					if (GabrielEventsLoader.detailedDebug)
					{
						LOGGER.info("Player " + player.getName() + " - Total armor items before grouping (grade " + selectedGrade + "): " + gradeItems.getItems().size());
						LOGGER.info("Player " + player.getName() + " - Total unique armor items after filtering: " + uniqueItems.size());
					}
					List<List<ItemHolder>> sets = new ArrayList<>();
					List<ItemHolder> currentSet = new ArrayList<>();
					String lastName = null;
					for (ItemHolder itemHolder : uniqueItems)
					{
						String currentName = itemHolder.getName() != null ? itemHolder.getName() : "";
						if (lastName != null && !currentName.equals(lastName) && !currentSet.isEmpty())
						{
							sets.add(new ArrayList<>(currentSet));
							currentSet.clear();
						}
						currentSet.add(itemHolder);
						lastName = currentName;
					}
					if (!currentSet.isEmpty())
					{
						sets.add(new ArrayList<>(currentSet));
					}
					for (List<ItemHolder> set : sets)
					{
						if (!set.isEmpty())
						{
							StringBuilder setIds = new StringBuilder();
							for (ItemHolder setItem : set)
							{
								setIds.append(setItem.getId()).append(";");
							}
							String setIdString = setIds.toString();
							ItemHolder firstItem = set.get(0);
							String setName = firstItem.getName() != null ? firstItem.getName() : "Unknown Set";
							String icon = firstItem.getIcon() != null ? firstItem.getIcon() : "icon.etc_quest_rotten_bread_i00";
							int price = (int) firstItem.getPrice();
							String entry = "<table width=350 border=0>" + "<tr>" + "<td width=32><img src=\"" + icon + "\" width=32 height=32></td>" + "<td width=300><button value=\"" + setName + " - " + price + " Points\" action=\"bypass -h event_buy_item armorset_" + setIdString + "_" + price + "\" width=200 height=40 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\"></td>" + "</tr>" + "</table>";
							if (uniqueArmorNames.add(setName))
							{
								armorsList.add(entry);
							}
						}
					}
				}
				else if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("No armors available for grade " + selectedGrade + " for classId " + classId);
				}
			}
			else if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Armors map is empty or null for classId " + classId);
			}
		}

		// Build jewels list (simplified like armor sets)
		List<String> jewelsList = new ArrayList<>();
		Map<String, ClassItemManager.GradeItems> jewelsMap = ClassItemManager.getInstance().getGradeItems(classId, "jewels");
		if (jewelsMap != null && !jewelsMap.isEmpty())
		{
			ClassItemManager.GradeItems gradeItems = jewelsMap.get(selectedGrade);
			if (gradeItems != null && gradeItems.getItems() != null && !gradeItems.getItems().isEmpty())
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("DEBUG: Found " + gradeItems.getItems().size() + " jewel items for grade " + selectedGrade);
					for (ItemHolder item1 : gradeItems.getItems())
					{
						LOGGER.info("DEBUG: Jewel item ID=" + item1.getId() + ", count=" + item1.getCount() + ", price=" + item1.getPrice());
					}
				}

				// Get the first item for price, name and icon (all items in set have same price)
				ItemHolder firstItem = gradeItems.getItems().get(0);
				int setPrice = (int) firstItem.getPrice();
				String setName = firstItem.getName() != null ? firstItem.getName() : ("Jewel Set Grade " + selectedGrade);
				String icon = firstItem.getIcon() != null ? firstItem.getIcon() : "icon.accessory_ring_of_ages_i00";

				// Collect unique item IDs in the set (avoid duplicates)
				Set<Integer> uniqueIds = new HashSet<>();
				StringBuilder jewelIds = new StringBuilder();
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (!uniqueIds.contains(itemHolder.getId()))
					{
						uniqueIds.add(itemHolder.getId());
						if (jewelIds.length() > 0)
							jewelIds.append(",");
						jewelIds.append(itemHolder.getId());
					}
				}

				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("DEBUG: Unique jewel IDs for grade " + selectedGrade + ": " + jewelIds.toString() + " (total: " + uniqueIds.size() + " unique items)");
				}

				// Create single entry for jewel set
				String entry = "<table width=350 border=0>" + "<tr>" + "<td width=32><img src=\"" + icon + "\" width=32 height=32></td>" + "<td width=300><button value=\"" + setName + " (" + setPrice + " Points)\" action=\"bypass -h event_buy_item jewelset_" + jewelIds.toString() + "_" + setPrice + "\" width=200 height=40 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\"></td>" + "</tr>" + "</table>";
				jewelsList.add(entry);
			}
			else if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("No jewels available for grade " + selectedGrade + " for classId " + classId);
			}
		}
		else if (GabrielEventsLoader.detailedDebug)
		{
			LOGGER.info("Jewels map is empty or null for classId " + classId);
		}

		if (GabrielEventsLoader.detailedDebug)
		{
			LOGGER.info("Player " + player.getName() + " - Total weapons available for grade " + selectedGrade + ": " + weaponsList.size());
			LOGGER.info("Player " + player.getName() + " - Total armor sets available for grade " + selectedGrade + ": " + armorsList.size());
			LOGGER.info("Player " + player.getName() + " - Total jewels available for grade " + selectedGrade + ": " + jewelsList.size());
		}
		int weaponPage = weaponPages.getOrDefault(player.getObjectId(), 1);
		int totalWeaponPages = (int) Math.ceil((double) weaponsList.size() / ITEMS_PER_PAGE);
		weaponPage = Math.max(1, Math.min(weaponPage, totalWeaponPages));
		weaponPages.put(player.getObjectId(), weaponPage);
		StringBuilder weaponsDisplay = new StringBuilder();
		StringBuilder weaponsPagination = new StringBuilder();
		if (hasPurchasedWeaponInGrade || weaponsList.isEmpty())
		{
			weaponsDisplay.append(hasPurchasedWeaponInGrade ? "You have already purchased a weapon in this grade.<br>" : "No weapons available for your class in this grade.<br>");
			weaponsPagination.append("<table width=300 border=0 cellpadding=0 cellspacing=0><tr>" + "<td width=80 align=left><button value=\"Previous\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "<td width=140 align=center><font color=LEVEL>Page 0 of 0</font></td>" + "<td width=80 align=right><button value=\"Next\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "</tr></table>");
		}
		else
		{
			int weaponStart = (weaponPage - 1) * ITEMS_PER_PAGE;
			int weaponEnd = Math.min(weaponStart + ITEMS_PER_PAGE, weaponsList.size());
			for (int i = weaponStart; i < weaponEnd; i++)
			{
				weaponsDisplay.append(weaponsList.get(i));
			}
			weaponsPagination.append("<table width=300 border=0 cellpadding=0 cellspacing=0><tr>");
			weaponsPagination.append("<td width=80 align=left>");
			if (weaponPage > 1)
			{
				weaponsPagination.append("<button value=\"Previous\" action=\"bypass -h event_page weapons " + (weaponPage - 1) + "\" width=70 height=22 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
			}
			else
			{
				weaponsPagination.append("<button value=\"Previous\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
			}
			weaponsPagination.append("</td>");
			weaponsPagination.append("<td width=140 align=center><font color=LEVEL>Page " + weaponPage + " of " + totalWeaponPages + "</font></td>");
			weaponsPagination.append("<td width=80 align=right>");
			if (weaponPage < totalWeaponPages)
			{
				weaponsPagination.append("<button value=\"Next\" action=\"bypass -h event_page weapons " + (weaponPage + 1) + "\" width=70 height=22 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
			}
			else
			{
				weaponsPagination.append("<button value=\"Next\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
			}
			weaponsPagination.append("</td>");
			weaponsPagination.append("</tr></table>");
		}
		int armorPage = armorPages.getOrDefault(player.getObjectId(), 1);
		int totalArmorPages = (int) Math.ceil((double) armorsList.size() / ITEMS_PER_PAGE);
		armorPage = Math.max(1, Math.min(armorPage, totalArmorPages));
		armorPages.put(player.getObjectId(), armorPage);
		StringBuilder armorsDisplay = new StringBuilder();
		StringBuilder armorsPagination = new StringBuilder();
		if (hasPurchasedArmorInGrade || armorsList.isEmpty())
		{
			armorsDisplay.append(hasPurchasedArmorInGrade ? "You have already purchased an armor set in this grade.<br>" : "No armor sets available for your class in this grade.<br>");
			armorsPagination.append("<table width=300 border=0 cellpadding=0 cellspacing=0><tr>" + "<td width=80 align=left><button value=\"Previous\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "<td width=140 align=center><font color=LEVEL>Page 0 of 0</font></td>" + "<td width=80 align=right><button value=\"Next\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "</tr></table>");
		}
		else
		{
			int armorStart = (armorPage - 1) * ITEMS_PER_PAGE;
			int armorEnd = Math.min(armorStart + ITEMS_PER_PAGE, armorsList.size());
			for (int i = armorStart; i < armorEnd; i++)
			{
				armorsDisplay.append(armorsList.get(i));
			}
			armorsPagination.append("<table width=300 border=0 cellpadding=0 cellspacing=0><tr>");
			armorsPagination.append("<td width=80 align=left>");
			if (armorPage > 1)
			{
				armorsPagination.append("<button value=\"Previous\" action=\"bypass -h event_page armors " + (armorPage - 1) + "\" width=70 height=22 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
			}
			else
			{
				armorsPagination.append("<button value=\"Previous\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
			}
			armorsPagination.append("</td>");
			armorsPagination.append("<td width=140 align=center><font color=LEVEL>Page " + armorPage + " of " + totalArmorPages + "</font></td>");
			armorsPagination.append("<td width=80 align=right>");
			if (armorPage < totalArmorPages)
			{
				armorsPagination.append("<button value=\"Next\" action=\"bypass -h event_page armors " + (armorPage + 1) + "\" width=70 height=22 back=\"L2UI_CT1.Button_DF_Down\" fore=\"L2UI_CT1.Button_DF\">");
			}
			else
			{
				armorsPagination.append("<button value=\"Next\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\">");
			}
			armorsPagination.append("</td>");
			armorsPagination.append("</tr></table>");
		}

		// Jewels display (simplified - no pagination needed since it's just one set)
		StringBuilder jewelsDisplay = new StringBuilder();
		StringBuilder jewelsPagination = new StringBuilder();
		if (jewelsList.isEmpty())
		{
			jewelsDisplay.append("No jewel set available for your class in this grade.<br>");
		}
		else
		{
			// Display the single jewel set entry
			jewelsDisplay.append(jewelsList.get(0));
		}
		// Simple pagination display (no actual pagination needed)
		jewelsPagination.append("<table width=300 border=0 cellpadding=0 cellspacing=0><tr>" + "<td width=80 align=left><button value=\"Previous\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "<td width=140 align=center><font color=LEVEL>Jewel Set</font></td>" + "<td width=80 align=right><button value=\"Next\" width=70 height=22 back=\"L2UI_ct1.button_df\" fore=\"L2UI_ct1.button_df\"></td>" + "</tr></table>");

		final NpcHtmlMessage html = new NpcHtmlMessage();
		String htmlContent = HtmCache.getInstance().getHtm(player, "data/html/item/76000.htm");
		if (htmlContent == null)
		{
			player.sendMessage("Error: Could not load item shop interface.");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " cannot use item: HTML file (76000.htm) not found");
			}
			return false;
		}
		htmlContent = htmlContent.replace("%stage%", String.valueOf(stageNumber));
		htmlContent = htmlContent.replace("%points%", String.valueOf(event.getPlayerData(playerInfo).getEventPoints()));
		htmlContent = htmlContent.replace("%weaponsList%", weaponsDisplay.toString());
		htmlContent = htmlContent.replace("%weaponsPagination%", weaponsPagination.toString());
		htmlContent = htmlContent.replace("%armorsList%", armorsDisplay.toString());
		htmlContent = htmlContent.replace("%armorsPagination%", armorsPagination.toString());
		htmlContent = htmlContent.replace("%jewelsList%", jewelsDisplay.toString());
		htmlContent = htmlContent.replace("%jewelsPagination%", jewelsPagination.toString());
		html.setHtml(htmlContent);
		player.sendPacket(html);
		return true;
	}
	
	public static void handleBuyBypass(PlayerInstance player, String bypass)
	{
		if (!bypass.startsWith("event_buy_item"))
		{
			return;
		}
		final PlayerEventInfo playerInfo = player.getEventInfo();
		BattleRoyale event = (BattleRoyale) EventManager.getInstance().getMainEventManager().getCurrent();
		if (event == null || !event.getEventType().equals(EventType.BattleRoyales) || !event.getPlayers(playerInfo.getInstanceId()).contains(playerInfo))
		{
			player.sendMessage("You can only buy items during a Battle Royale event!");
			return;
		}
		BattleRoyale.BRData eventData = event.getEventData(playerInfo.getInstanceId());
		if (eventData == null)
		{
			player.sendMessage("Event data not found!");
			return;
		}
		int stageNumber = eventData.getCurrentStageIndex() >= 0 ? eventData.getStages().get(eventData.getCurrentStageIndex()).getStageNumber() : 0;
		String currentGrade;
		switch (stageNumber)
		{
			case 0:
			case 1:
				currentGrade = "C";
				break;
			case 2:
				currentGrade = "B";
				break;
			case 3:
				currentGrade = "A";
				break;
			case 4:
			case 5:
				currentGrade = "special";
				break;
			default:
				player.sendMessage("Invalid stage! Cannot buy items.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("Player " + player.getName() + " cannot buy item: Invalid stage (" + stageNumber + ")");
				}
				return;
		}

		// Rate limiting check
		long currentTime = System.currentTimeMillis();
		Long lastPurchase = lastPurchaseTime.get(player.getObjectId());
		if (lastPurchase != null && (currentTime - lastPurchase) < PURCHASE_COOLDOWN)
		{
			player.sendMessage("Please wait before making another purchase.");
			return;
		}

		String[] parts = bypass.split(" ");
		if (parts.length != 2)
		{
			player.sendMessage("Invalid buy command.");
			return;
		}
		String[] buyData = parts[1].split("_");
		if (buyData.length < 3)
		{
			player.sendMessage("Invalid buy command.");
			return;
		}
		String type = buyData[0];
		String idString = buyData[1];
		PvPEventPlayerData playerData = event.getPlayerData(playerInfo);
		int classId = player.getClassId().getId();
		Set<Integer> purchasedItems = eventData.getPlayerItemIds(playerInfo);
		boolean hasPurchasedWeaponInGrade = false;
		boolean hasPurchasedArmorInGrade = false;
		Map<String, ClassItemManager.GradeItems> allWeaponsMap = ClassItemManager.getInstance().getGradeItems(classId, "weapons");
		Map<String, ClassItemManager.GradeItems> allArmorsMap = ClassItemManager.getInstance().getGradeItems(classId, "armors");
		if (allWeaponsMap != null && !allWeaponsMap.isEmpty())
		{
			ClassItemManager.GradeItems gradeItems = allWeaponsMap.get(currentGrade);
			if (gradeItems != null && gradeItems.getItems() != null)
			{
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (purchasedItems.contains(itemHolder.getId()))
					{
						hasPurchasedWeaponInGrade = true;
						break;
					}
				}
			}
		}
		if (allArmorsMap != null && !allArmorsMap.isEmpty())
		{
			ClassItemManager.GradeItems gradeItems = allArmorsMap.get(currentGrade);
			if (gradeItems != null && gradeItems.getItems() != null)
			{
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (purchasedItems.contains(itemHolder.getId()))
					{
						hasPurchasedArmorInGrade = true;
						break;
					}
				}
			}
		}
		if (type.equals("weapon"))
		{
			int itemId = Integer.parseInt(idString);
			Item weaponTemplate = ItemTable.getInstance().getTemplate(itemId);
			if (weaponTemplate == null)
			{
				player.sendMessage("Item not found.");
				return;
			}
			Map<String, ClassItemManager.GradeItems> weaponsMap = ClassItemManager.getInstance().getGradeItems(classId, "weapons");
			if (weaponsMap == null || weaponsMap.isEmpty())
			{
				player.sendMessage("No weapons available for your class.");
				return;
			}
			boolean gradeMatch = false;
			ClassItemManager.GradeItems gradeItems = weaponsMap.get(currentGrade);
			if (gradeItems != null && gradeItems.getItems() != null)
			{
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (itemHolder.getId() == itemId)
					{
						gradeMatch = true;
						int price = (int) itemHolder.getPrice();

						// Validate price is not negative
						if (price < 0)
						{
							player.sendMessage("Invalid item price.");
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.warning("Player " + player.getName() + " attempted to buy weapon ID " + itemId + " with negative price: " + price);
							}
							return;
						}

						// Validate item template exists
						Item weaponItemTemplate = ItemTable.getInstance().getTemplate(itemId);
						if (weaponItemTemplate == null)
						{
							player.sendMessage("Item not found in database.");
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.warning("Player " + player.getName() + " attempted to buy weapon ID " + itemId + " but item template not found");
							}
							return;
						}

						// Check inventory space
						if (!player.getInventory().validateCapacity(1))
						{
							player.sendMessage("Your inventory is full.");
							return;
						}

						if (playerData.getEventPoints() < price)
						{
							player.sendMessage("You don't have enough points. Required: " + price);
							return;
						}
						if (hasPurchasedWeaponInGrade)
						{
							player.sendMessage("You have already purchased a weapon in this grade (Grade: " + currentGrade + ").");
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.info("Player " + player.getName() + " attempted to buy weapon ID " + itemId + " but already purchased a weapon in grade " + currentGrade + " in stage " + stageNumber);
							}
							return;
						}

						// Deduct points and add item
						playerData.addEventPoints(-price);
						ItemInstance itemInstance = player.getInventory().addItem("BattleRoyalePurchase", itemId, 1, player, null);
						if (itemInstance != null)
						{
							itemInstance.setCustomType1(9999);
							event.getEventData(playerInfo.getInstanceId()).addPlayerItem(playerInfo, itemId);

							// Auto-equip weapon
							try
							{
								if (itemInstance.isEquipable())
								{
									player.getInventory().equipItemAndRecord(itemInstance);
									player.sendMessage("You have purchased and equipped weapon ID " + itemId + " for " + price + " points.");
								}
								else
								{
									player.sendMessage("You have purchased weapon ID " + itemId + " for " + price + " points.");
								}
							}
							catch (Exception e)
							{
								player.sendMessage("You have purchased weapon ID " + itemId + " for " + price + " points. (Auto-equip failed)");
								if (GabrielEventsLoader.detailedDebug)
								{
									LOGGER.warning("Failed to auto-equip weapon " + itemId + " for player " + player.getName() + ": " + e.getMessage());
								}
							}

							lastPurchaseTime.put(player.getObjectId(), System.currentTimeMillis());
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.info("Player " + player.getName() + " successfully purchased weapon ID " + itemId + " for " + price + " points in grade " + currentGrade + " (stage " + stageNumber + ").");
							}
						}
						else
						{
							// Rollback points if item creation failed
							playerData.addEventPoints(price);
							player.sendMessage("Failed to create item. Points have been refunded.");
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.warning("Player " + player.getName() + " failed to create weapon ID " + itemId + ", points refunded");
							}
						}
						break;
					}
				}
			}
			if (!gradeMatch)
			{
				player.sendMessage("This item is not available for purchase in the current stage (Stage " + stageNumber + ", Grade: " + currentGrade + ").");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("Player " + player.getName() + " attempted to buy weapon ID " + itemId + " but grade does not match current stage (Stage " + stageNumber + ", Grade: " + currentGrade + ").");
				}
			}
		}
		else if (type.equals("armorset"))
		{
			String[] itemIds = idString.split(";");
			Map<String, ClassItemManager.GradeItems> armorsMap = ClassItemManager.getInstance().getGradeItems(classId, "armors");
			if (armorsMap == null || armorsMap.isEmpty())
			{
				player.sendMessage("No armors available for your class.");
				return;
			}
			ClassItemManager.GradeItems gradeItems = armorsMap.get(currentGrade);
			if (gradeItems == null || gradeItems.getItems() == null)
			{
				player.sendMessage("No armors available for the current stage.");
				return;
			}
			int firstItemId = Integer.parseInt(itemIds[0]);
			int price = 0;
			for (ItemHolder itemHolder : gradeItems.getItems())
			{
				if (itemHolder.getId() == firstItemId)
				{
					price = (int) itemHolder.getPrice();
					break;
				}
			}
			if (playerData.getEventPoints() < price)
			{
				player.sendMessage("You don't have enough points. Required: " + price);
				return;
			}
			for (String itemIdStr : itemIds)
			{
				if (itemIdStr.isEmpty())
					continue;
				int itemId = Integer.parseInt(itemIdStr);
				Item armorItemTemplate = ItemTable.getInstance().getTemplate(itemId);
				if (armorItemTemplate == null)
				{
					player.sendMessage("Item ID " + itemId + " not found in armor set.");
					return;
				}
				boolean gradeMatch = false;
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					if (itemHolder.getId() == itemId)
					{
						gradeMatch = true;
						break;
					}
				}
				if (!gradeMatch)
				{
					player.sendMessage("This armor set contains items not available for purchase in the current stage (Stage " + stageNumber + ", Grade: " + currentGrade + ").");
					if (GabrielEventsLoader.detailedDebug)
					{
						LOGGER.info("Player " + player.getName() + " attempted to buy armor set containing item ID " + itemId + " but grade does not match current stage (Stage " + stageNumber + ", Grade: " + currentGrade + ").");
					}
					return;
				}
			}
			if (hasPurchasedArmorInGrade)
			{
				player.sendMessage("You have already purchased an armor set in this grade (Grade: " + currentGrade + ").");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("Player " + player.getName() + " attempted to buy an armor set but already purchased one in grade " + currentGrade + " in stage " + stageNumber);
				}
				return;
			}

			// Validate price is not negative
			if (price < 0)
			{
				player.sendMessage("Invalid armor set price.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.warning("Player " + player.getName() + " attempted to buy armor set with negative price: " + price);
				}
				return;
			}

			// Check inventory space for all items in the set
			int itemCount = 0;
			for (String itemIdStr : itemIds)
			{
				if (!itemIdStr.isEmpty())
					itemCount++;
			}
			if (!player.getInventory().validateCapacity(itemCount))
			{
				player.sendMessage("Your inventory doesn't have enough space for this armor set (" + itemCount + " items).");
				return;
			}

			// Transaction safety: Create all items first, then equip them safely
			List<ItemInstance> createdItems = new ArrayList<>();
			boolean transactionSuccess = true;

			playerData.addEventPoints(-price);

			// First phase: Create all items without equipping
			for (String itemIdStr : itemIds)
			{
				if (itemIdStr.isEmpty())
					continue;
				int itemId = Integer.parseInt(itemIdStr);
				ItemInstance itemInstance = player.getInventory().addItem("BattleRoyalePurchase", itemId, 1, player, null);
				if (itemInstance != null)
				{
					itemInstance.setCustomType1(9999);
					event.getEventData(playerInfo.getInstanceId()).addPlayerItem(playerInfo, itemId);
					createdItems.add(itemInstance);
				}
				else
				{
					transactionSuccess = false;
					break;
				}
			}

			// Second phase: Equip all items safely with single inventory update
			if (transactionSuccess && !createdItems.isEmpty())
			{
				// Prepare single inventory update for all equipped items
				final InventoryUpdate combinedUpdate = new InventoryUpdate();
				boolean equipSuccess = true;

				for (ItemInstance itemInstance : createdItems)
				{
					try
					{
						if (itemInstance.isEquipable())
						{
							// Use appropriate equip method without sending individual updates
							if (isJewelryItem(itemInstance))
							{
								if (smartEquipJewelryWithoutUpdate(player, itemInstance))
								{
									combinedUpdate.addModifiedItem(itemInstance);
									if (GabrielEventsLoader.detailedDebug)
									{
										LOGGER.info("Successfully equipped jewelry armor item ID=" + itemInstance.getId());
									}
								}
								else
								{
									if (GabrielEventsLoader.detailedDebug)
									{
										LOGGER.info("Failed to equip jewelry armor item " + itemInstance.getId() + " - no available slot for player " + player.getName());
									}
								}
							}
							else
							{
								// For regular armor items, use normal equip without update
								if (equipItemWithoutUpdate(player, itemInstance))
								{
									combinedUpdate.addModifiedItem(itemInstance);
									if (GabrielEventsLoader.detailedDebug)
									{
										LOGGER.info("Successfully equipped armor item ID=" + itemInstance.getId());
									}
								}
								else
								{
									if (GabrielEventsLoader.detailedDebug)
									{
										LOGGER.info("Failed to equip armor item " + itemInstance.getId() + " - slot may be occupied for player " + player.getName());
									}
								}
							}
						}
					}
					catch (Exception e)
					{
						if (GabrielEventsLoader.detailedDebug)
						{
							LOGGER.warning("Failed to auto-equip armor " + itemInstance.getId() + " for player " + player.getName() + ": " + e.getMessage());
						}
						equipSuccess = false;
					}
				}

				// Send single combined inventory update for all equipped items with safety checks
				if (equipSuccess && combinedUpdate.getItems().size() > 0)
				{
					try
					{
						// Add small delay to prevent packet spam
						Thread.sleep(50);

						player.sendInventoryUpdate(combinedUpdate);

						// Add another small delay before broadcast
						Thread.sleep(50);

						player.broadcastUserInfo(); // Single broadcast for all changes

						if (GabrielEventsLoader.detailedDebug)
						{
							LOGGER.info("DEBUG: Safely sent combined inventory update for " + combinedUpdate.getItems().size() + " armor items");
						}
					}
					catch (Exception e)
					{
						LOGGER.warning("Failed to send inventory update for armor items: " + e.getMessage());
						// Try to send item list as fallback
						try
						{
							player.sendItemList();
						}
						catch (Exception e2)
						{
							LOGGER.warning("Fallback sendItemList also failed: " + e2.getMessage());
						}
					}
				}
			}

			if (!transactionSuccess)
			{
				// Rollback: Remove created items and refund points
				for (ItemInstance item : createdItems)
				{
					player.getInventory().destroyItem("ArmorSetRollback", item, player, null);
				}
				playerData.addEventPoints(price);
				player.sendMessage("Failed to create armor set. Points have been refunded.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.warning("Player " + player.getName() + " armor set purchase failed, transaction rolled back");
				}
				return;
			}

			lastPurchaseTime.put(player.getObjectId(), System.currentTimeMillis());
			player.sendMessage("You have purchased and equipped an armor set (" + createdItems.size() + " items) for " + price + " points.");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " successfully purchased and equipped an armor set (" + createdItems.size() + " items) for " + price + " points in grade " + currentGrade + " (stage " + stageNumber + ").");
			}
		}
		else if (type.equals("jewelset"))
		{
			// Handle jewel set purchase (like armor sets)
			String[] itemIds = idString.split(",");
			int price = Integer.parseInt(buyData[2]);

			// Validate price is not negative
			if (price < 0)
			{
				player.sendMessage("Invalid jewel set price.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.warning("Player " + player.getName() + " attempted to buy jewel set with negative price: " + price);
				}
				return;
			}

			// Check inventory space for all items in the set
			int itemCount = 0;
			for (String itemIdStr : itemIds)
			{
				if (!itemIdStr.isEmpty())
					itemCount++;
			}
			if (!player.getInventory().validateCapacity(itemCount))
			{
				player.sendMessage("Your inventory doesn't have enough space for this jewel set (" + itemCount + " items).");
				return;
			}

			if (playerData.getEventPoints() < price)
			{
				player.sendMessage("You don't have enough points. Required: " + price);
				return;
			}

			// Transaction safety: Create all items first, then add to inventory
			List<ItemInstance> createdItems = new ArrayList<>();
			boolean transactionSuccess = true;

			playerData.addEventPoints(-price);

			// Get jewel items from ClassItemManager to get correct counts
			Map<String, ClassItemManager.GradeItems> jewelsMap = ClassItemManager.getInstance().getGradeItems(classId, "jewels");
			ClassItemManager.GradeItems gradeItems = jewelsMap.get(currentGrade);

			if (gradeItems != null && gradeItems.getItems() != null)
			{
				// Create unique items map to avoid duplicates
				Map<Integer, ItemHolder> uniqueItems = new HashMap<>();
				for (ItemHolder itemHolder : gradeItems.getItems())
				{
					uniqueItems.put(itemHolder.getId(), itemHolder);
				}

				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("DEBUG: After deduplication, found " + uniqueItems.size() + " unique jewel items");
				}

				// Create items based on unique ItemHolder data (with correct counts)
				for (ItemHolder itemHolder : uniqueItems.values())
				{
					int itemId = itemHolder.getId();
					long count = itemHolder.getCount();

					if (GabrielEventsLoader.detailedDebug)
					{
						LOGGER.info("DEBUG: Creating jewel item ID=" + itemId + " with count=" + count);
					}

					// PHASE 1: Create individual items for jewelry (rings, earrings need separate instances)
					for (int i = 0; i < count; i++)
					{
						ItemInstance itemInstance = player.getInventory().addItem("BattleRoyalePurchase", itemId, 1, player, null);
						if (itemInstance != null)
						{
							itemInstance.setCustomType1(9999);
							event.getEventData(playerInfo.getInstanceId()).addPlayerItem(playerInfo, itemId);
							createdItems.add(itemInstance);

							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.info("DEBUG: Created jewel item ID=" + itemId + " (instance " + (i+1) + "/" + count + ")");
							}
						}
						else
						{
							transactionSuccess = false;
							break;
						}
					}

					if (!transactionSuccess)
					{
						break;
					}
				}

				// PHASE 2: Equip all created items safely with single inventory update
				if (transactionSuccess && !createdItems.isEmpty())
				{
					final InventoryUpdate combinedUpdate = new InventoryUpdate();
					boolean equipSuccess = true;

					for (ItemInstance itemInstance : createdItems)
					{
						try
						{
							if (itemInstance.isEquipable())
							{
								// Debug logging
								if (GabrielEventsLoader.detailedDebug)
								{
									LOGGER.info("DEBUG: Attempting to equip jewel item ID=" + itemInstance.getId() + ", bodyPart=" + itemInstance.getItem().getBodyPart() + ", isJewelry=" + isJewelryItem(itemInstance));
								}

								// Use smart equip for all jewelry items (without sending individual updates)
								if (isJewelryItem(itemInstance))
								{
									if (smartEquipJewelryWithoutUpdate(player, itemInstance))
									{
										combinedUpdate.addModifiedItem(itemInstance);
										if (GabrielEventsLoader.detailedDebug)
										{
											LOGGER.info("DEBUG: Successfully equipped jewelry item ID=" + itemInstance.getId());
										}
									}
								}
								else
								{
									// For non-jewelry items, use normal equip but add to combined update
									if (equipItemWithoutUpdate(player, itemInstance))
									{
										combinedUpdate.addModifiedItem(itemInstance);
										if (GabrielEventsLoader.detailedDebug)
										{
											LOGGER.info("DEBUG: Successfully equipped non-jewelry item ID=" + itemInstance.getId());
										}
									}
								}
							}
						}
						catch (Exception e)
						{
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.warning("Failed to auto-equip jewel " + itemInstance.getId() + " for player " + player.getName() + ": " + e.getMessage());
								e.printStackTrace();
							}
							equipSuccess = false;
						}
					}

					// Send single combined inventory update for all equipped items with safety checks
					if (equipSuccess && combinedUpdate.getItems().size() > 0)
					{
						try
						{
							// Add small delay to prevent packet spam
							Thread.sleep(50);

							player.sendInventoryUpdate(combinedUpdate);

							// Add another small delay before broadcast
							Thread.sleep(50);

							player.broadcastUserInfo(); // Single broadcast for all changes

							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.info("DEBUG: Safely sent combined inventory update for " + combinedUpdate.getItems().size() + " jewelry items");
							}
						}
						catch (Exception e)
						{
							LOGGER.warning("Failed to send inventory update for jewelry items: " + e.getMessage());
							// Try to send item list as fallback
							try
							{
								player.sendItemList();
							}
							catch (Exception e2)
							{
								LOGGER.warning("Fallback sendItemList also failed: " + e2.getMessage());
							}
						}
					}
				}
			}
			else
			{
				// Fallback to old method if gradeItems not found - also use combined update
				final InventoryUpdate combinedUpdate = new InventoryUpdate();

				for (String itemIdStr : itemIds)
				{
					if (itemIdStr.isEmpty())
						continue;
					int itemId = Integer.parseInt(itemIdStr);
					ItemInstance itemInstance = player.getInventory().addItem("BattleRoyalePurchase", itemId, 1, player, null);
					if (itemInstance != null)
					{
						itemInstance.setCustomType1(9999);
						event.getEventData(playerInfo.getInstanceId()).addPlayerItem(playerInfo, itemId);
						createdItems.add(itemInstance);
					}
					else
					{
						transactionSuccess = false;
						break;
					}
				}

				// Equip all fallback items with combined update
				if (transactionSuccess && !createdItems.isEmpty())
				{
					for (ItemInstance itemInstance : createdItems)
					{
						try
						{
							if (itemInstance.isEquipable())
							{
								if (isJewelryItem(itemInstance))
								{
									if (smartEquipJewelryWithoutUpdate(player, itemInstance))
									{
										combinedUpdate.addModifiedItem(itemInstance);
									}
								}
								else
								{
									if (equipItemWithoutUpdate(player, itemInstance))
									{
										combinedUpdate.addModifiedItem(itemInstance);
									}
								}
							}
						}
						catch (Exception e)
						{
							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.warning("Failed to auto-equip fallback jewel " + itemInstance.getId() + " for player " + player.getName() + ": " + e.getMessage());
							}
						}
					}

					// Send combined update for fallback items with safety checks
					if (combinedUpdate.getItems().size() > 0)
					{
						try
						{
							// Add small delay to prevent packet spam
							Thread.sleep(50);

							player.sendInventoryUpdate(combinedUpdate);

							// Add another small delay before broadcast
							Thread.sleep(50);

							player.broadcastUserInfo();

							if (GabrielEventsLoader.detailedDebug)
							{
								LOGGER.info("DEBUG: Safely sent combined inventory update for " + combinedUpdate.getItems().size() + " fallback items");
							}
						}
						catch (Exception e)
						{
							LOGGER.warning("Failed to send inventory update for fallback items: " + e.getMessage());
							// Try to send item list as fallback
							try
							{
								player.sendItemList();
							}
							catch (Exception e2)
							{
								LOGGER.warning("Fallback sendItemList also failed: " + e2.getMessage());
							}
						}
					}
				}
			}

			if (!transactionSuccess)
			{
				// Rollback: Remove created items and refund points
				for (ItemInstance item : createdItems)
				{
					player.getInventory().destroyItem("JewelSetRollback", item, player, null);
				}
				playerData.addEventPoints(price);
				player.sendMessage("Failed to create jewel set. Points have been refunded.");
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.warning("Player " + player.getName() + " jewel set purchase failed, transaction rolled back");
				}
				return;
			}

			// Calculate total item count
			long totalItemCount = 0;
			for (ItemInstance item : createdItems)
			{
				totalItemCount += item.getCount();
			}

			lastPurchaseTime.put(player.getObjectId(), System.currentTimeMillis());
			player.sendMessage("You have purchased and equipped a jewel set (" + createdItems.size() + " types, " + totalItemCount + " total items) for " + price + " points.");
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("Player " + player.getName() + " successfully purchased and equipped a jewel set (" + createdItems.size() + " types, " + totalItemCount + " total items) for " + price + " points in grade " + currentGrade + " (stage " + stageNumber + ").");
			}
		}
	}

	public static void handlePageBypass(PlayerInstance player, String bypass)
	{
		if (!bypass.startsWith("event_page"))
		{
			return;
		}
		String[] parts = bypass.split(" ");
		if (parts.length != 3)
		{
			player.sendMessage("Invalid page command.");
			return;
		}
		String listType = parts[1];
		int page = Integer.parseInt(parts[2]);
		if (listType.equals("weapons"))
		{
			weaponPages.put(player.getObjectId(), page);
		}
		else if (listType.equals("armors"))
		{
			armorPages.put(player.getObjectId(), page);
		}
		// No pagination needed for jewels since it's just one set
		BattleRoyalItemHandler handler = new BattleRoyalItemHandler();
		handler.useItem(player, player.getInventory().getItemByItemId(76000), false);
	}

	// Helper method to check if item is jewelry (rings, earrings)
	private static boolean isJewelryItem(ItemInstance item)
	{
		if (item == null || item.getItem() == null)
			return false;

		long bodyPart = item.getItem().getBodyPart();
		return (bodyPart == Item.BodyPart.SLOT_R_FINGER.getSlot()) ||
			   (bodyPart == Item.BodyPart.SLOT_L_FINGER.getSlot()) ||
			   (bodyPart == Item.BodyPart.SLOT_LR_FINGER.getSlot()) ||
			   (bodyPart == Item.BodyPart.SLOT_R_EAR.getSlot()) ||
			   (bodyPart == Item.BodyPart.SLOT_L_EAR.getSlot()) ||
			   (bodyPart == Item.BodyPart.SLOT_LR_EAR.getSlot());
	}

	// Safety check before using items to prevent client crashes
	private static boolean canSafelyUseItem(PlayerInstance player, ItemInstance item)
	{
		if (player == null || item == null || item.getItem() == null)
		{
			return false;
		}

		// Check if player is online and stable
		if (!player.isOnline() || player.isInStoreMode() || player.isCastingNow())
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("DEBUG: Cannot use item - player not in stable state");
			}
			return false;
		}

		// For equipable items, check if they can be equipped
		if (item.isEquipable())
		{
			if (item.isEquipped())
			{
				if (GabrielEventsLoader.detailedDebug)
				{
					LOGGER.info("DEBUG: Cannot equip item - already equipped");
				}
				return false;
			}
		}
		// For non-equipable items (like shop interface), allow usage
		else
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("DEBUG: Allowing usage of non-equipable item " + item.getId());
			}
		}

		return true;
	}

	// Smart equip method for jewelry items
	private static void smartEquipJewelry(PlayerInstance player, ItemInstance item) {
		if (item == null || item.getItem() == null) {
			if (GabrielEventsLoader.detailedDebug) {
				LOGGER.warning("DEBUG: smartEquipJewelry called with null item or item.getItem()");
			}
			return;
		}

		// Safety check before equipping
		if (!canSafelyUseItem(player, item))
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.info("DEBUG: Safety check failed for jewelry item " + item.getId());
			}
			return;
		}

		long bodyPart = item.getItem().getBodyPart();

		if (GabrielEventsLoader.detailedDebug) {
			LOGGER.info("DEBUG: smartEquipJewelry - item ID=" + item.getId() + ", bodyPart=" + bodyPart + ", player=" + player.getName());
		}

		try {
			// Handle rings
			if ((bodyPart == Item.BodyPart.SLOT_R_FINGER.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_L_FINGER.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_LR_FINGER.getSlot())) {
				// Try right finger first, then left finger
				ItemInstance rightRing = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_RFINGER);
				ItemInstance leftRing = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_LFINGER);

				if (rightRing == null) {
					// Equip to right finger
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_RFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_RFINGER);
				} else if (leftRing == null) {
					// Equip to left finger
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_LFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_LFINGER);
				} else {
					// Both slots occupied, replace right finger
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_RFINGER);
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_RFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_RFINGER);
				}
			}
			// Handle earrings
			else if ((bodyPart == Item.BodyPart.SLOT_R_EAR.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_L_EAR.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_LR_EAR.getSlot())) {
				// Try right ear first, then left ear
				ItemInstance rightEar = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_REAR);
				ItemInstance leftEar = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_LEAR);

				if (rightEar == null) {
					// Equip to right ear
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_REAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_REAR);
				} else if (leftEar == null) {
					// Equip to left ear
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_LEAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_LEAR);
				} else {
					// Both slots occupied, replace right ear
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_REAR);
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_REAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_REAR);
				}
			} else {
				// Not a ring or earring, use normal equip
				player.getInventory().equipItemAndRecord(item);
				return;
			}

			// Update item state and send inventory update with safety checks
			item.setLastChange(ItemInstance.MODIFIED);

			try
			{
				// Add small delay to prevent packet spam
				Thread.sleep(50);

				final InventoryUpdate iu = new InventoryUpdate();
				iu.addModifiedItem(item);
				player.sendInventoryUpdate(iu);

				// Add another small delay before broadcast
				Thread.sleep(50);

				player.broadcastUserInfo();

				if (GabrielEventsLoader.detailedDebug) {
					LOGGER.info("Smart equipped jewelry item " + item.getId() + " for player " + player.getName());
				}
			}
			catch (Exception updateException)
			{
				LOGGER.warning("Failed to send inventory update for jewelry item " + item.getId() + ": " + updateException.getMessage());
				// Try fallback
				try
				{
					player.sendItemList();
				}
				catch (Exception e2)
				{
					LOGGER.warning("Fallback sendItemList also failed: " + e2.getMessage());
				}
			}
		} catch (Exception e) {
			if (GabrielEventsLoader.detailedDebug) {
				LOGGER.warning("Failed to smart equip jewelry " + item.getId() + " for player " + player.getName() + ": " + e.getMessage());
			}
			// Fallback to normal equip
			try {
				player.getInventory().equipItemAndRecord(item);
			} catch (Exception e2) {
				LOGGER.warning("Fallback equip also failed for " + item.getId() + ": " + e2.getMessage());
			}
		}
	}

	// Smart equip method for jewelry items (without sending packets - for batch operations)
	private static boolean smartEquipJewelryWithoutUpdate (PlayerInstance player, ItemInstance item)
	{
		if (item == null || item.getItem() == null) {
			if (GabrielEventsLoader.detailedDebug) {
				LOGGER.warning("DEBUG: smartEquipJewelryWithoutUpdate called with null item or item.getItem()");
			}
			return false;
		}

		long bodyPart = item.getItem().getBodyPart();

		if (GabrielEventsLoader.detailedDebug) {
			LOGGER.info("DEBUG: smartEquipJewelryWithoutUpdate - item ID=" + item.getId() + ", bodyPart=" + bodyPart + ", player=" + player.getName());
		}

		try {
			// Handle rings
			if ((bodyPart == Item.BodyPart.SLOT_R_FINGER.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_L_FINGER.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_LR_FINGER.getSlot())) {
				// Try right finger first, then left finger
				ItemInstance rightRing = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_RFINGER);
				ItemInstance leftRing = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_LFINGER);

				if (rightRing == null) {
					// Equip to right finger
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_RFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_RFINGER);
				} else if (leftRing == null) {
					// Equip to left finger
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_LFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_LFINGER);
				} else {
					// Both slots occupied, replace right ring
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_RFINGER);
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_RFINGER, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_RFINGER);
				}
			}
			// Handle earrings
			else if ((bodyPart == Item.BodyPart.SLOT_R_EAR.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_L_EAR.getSlot()) ||
					(bodyPart == Item.BodyPart.SLOT_LR_EAR.getSlot())) {
				// Try right ear first, then left ear
				ItemInstance rightEar = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_REAR);
				ItemInstance leftEar = player.getInventory().getPaperdollItem(Inventory.PAPERDOLL_LEAR);

				if (rightEar == null) {
					// Equip to right ear
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_REAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_REAR);
				} else if (leftEar == null) {
					// Equip to left ear
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_LEAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_LEAR);
				} else {
					// Both slots occupied, replace right ear
					player.getInventory().unEquipItemInSlot(Inventory.PAPERDOLL_REAR);
					player.getInventory().setPaperdollItem(Inventory.PAPERDOLL_REAR, item);
					item.setItemLocation(ItemLocation.PAPERDOLL, Inventory.PAPERDOLL_REAR);
				}
			} else {
				// Not a ring or earring, use normal equip without update
				return equipItemWithoutUpdate(player, item);
			}

			// Update item state but don't send packets
			item.setLastChange(ItemInstance.MODIFIED);
			return true;
		} catch (Exception e) {
			if (GabrielEventsLoader.detailedDebug) {
				LOGGER.warning("Failed to smart equip jewelry " + item.getId() + " for player " + player.getName() + ": " + e.getMessage());
			}
			// Fallback to normal equip without update
			return equipItemWithoutUpdate(player, item);
		}
	}

	// Helper method to equip non-jewelry items without sending packets
	private static boolean equipItemWithoutUpdate(PlayerInstance player, ItemInstance item)
	{
		try
		{
			// Get the target slot for this item
			long bodyPart = item.getItem().getBodyPart();
			int targetSlot = Inventory.getPaperdollIndex(bodyPart);

			if (targetSlot != -1)
			{
				// Unequip existing item if any
				ItemInstance existingItem = player.getInventory().getPaperdollItem(targetSlot);
				if (existingItem != null)
				{
					player.getInventory().unEquipItemInSlot(targetSlot);
				}

				// Equip new item
				player.getInventory().setPaperdollItem(targetSlot, item);
				item.setItemLocation(ItemLocation.PAPERDOLL, targetSlot);
				item.setLastChange(ItemInstance.MODIFIED);
				return true;
			}
			return false;
		}
		catch (Exception e)
		{
			if (GabrielEventsLoader.detailedDebug)
			{
				LOGGER.warning("Failed to equip item " + item.getId() + " without update for player " + player.getName() + ": " + e.getMessage());
			}
			return false;
		}
	}
}