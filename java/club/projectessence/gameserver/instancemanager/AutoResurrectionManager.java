/*
 * This file is part of the L2J Mobius project.
 * 
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * 
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * 
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.instancemanager;

import java.util.concurrent.ScheduledFuture;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.instancemanager.GveRewardManager;
import club.projectessence.gameserver.instancemanager.HeavenlyRiftManager;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.geoengine.GeoEngine;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.EtcStatusUpdate;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

/**
 * Auto-resurrection manager for autoplay system.
 * Handles automatic resurrection with L-Coin cost and return to original location.
 * 
 * <AUTHOR>
 */
public class AutoResurrectionManager
{
	private static final Logger LOGGER = Logger.getLogger(AutoResurrectionManager.class.getName());
	
	/**
	 * Checks if auto-resurrection is allowed for the given player.
	 *
	 * @param player the player to check
	 * @return true if auto-resurrection is allowed, false otherwise
	 */
	public static boolean canAutoResurrect(PlayerInstance player)
	{
		// Safety checks to prevent crashes during login or when player is not fully initialized
		if (player == null)
		{
			return false;
		}

		// Check if player is fully online and initialized
		if (!player.isOnline() || player.getClient() == null)
		{
			return false;
		}

		// Check if auto-resurrection is enabled in config
		if (!Config.AUTOPLAY_AUTO_RESURRECTION_ENABLED)
		{
			return false;
		}

		// Must be in autoplay mode - add safety check
		try
		{
			if (!AutoPlayTaskManager.getInstance().isAutoPlay(player))
			{
				return false;
			}
		}
		catch (Exception e)
		{
			return false;
		}

		// Special handling for offline players
		if (player.isInOfflineMode())
		{
			if (!Config.OFFLINE_PLAY_AUTO_RESURRECTION_ENABLED)
			{
				return false;
			}

			// Additional offline-specific checks can be added here
		}
		
		// Check if auto-resurrection is enabled in settings - add safety check
		try
		{
			if (player.getAutoPlaySettings() == null || !player.getAutoPlaySettings().isAutoResurrectionEnabled(player))
			{
				return false;
			}
		}
		catch (Exception e)
		{
			return false;
		}

		// Check cooldown
		long currentTime = System.currentTimeMillis();
		long lastResTime = player.getAutoPlaySettings().getLastAutoResurrectionTime(player);
		long cooldownRemaining = (Config.AUTOPLAY_AUTO_RESURRECTION_COOLDOWN * 1000L) - (currentTime - lastResTime);
		if (cooldownRemaining > 0)
		{
			// Schedule auto-resurrection when cooldown ends (if player is dead and conditions still met)
			schedulePendingAutoResurrection(player, cooldownRemaining);
			return false;
		}

		// Check daily usage limit (different for premium vs regular players)
		int maxUsesToday = player.hasPremiumStatus() ?
			Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY_PREMIUM :
			Config.AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY;
		int usesToday = player.getAutoPlaySettings().getAutoResurrectionUsesToday(player);

		if (usesToday >= maxUsesToday)
		{
			return false;
		}
		
		// Check forbidden zones
		if (isInForbiddenZone(player))
		{
			LOGGER.fine("Auto-resurrection blocked for " + player.getName() + " - in forbidden zone");
			return false;
		}

		// Check if player has enough L-Coin
		int cost = calculateResurrectionCost(player);
		long currentLCoin = player.getInventory().getInventoryItemCount(Inventory.LCOIN_ID, -1);
		if (currentLCoin < cost)
		{
			return false;
		}
		
		// Check GvE death penalty
//		if (GveRewardManager.getInstance().hasDeathPenalty(player))
//		{
//			return false;
//		}
		
		return true;
	}
	
	/**
	 * Checks if the player is in a forbidden zone for auto-resurrection.
	 *
	 * @param player the player to check
	 * @return true if in forbidden zone, false otherwise
	 */
	private static boolean isInForbiddenZone(PlayerInstance player)
	{
		// Siege zones
		if (player.isInsideZone(ZoneId.SIEGE))
		{
			return true;
		}

		// PvP zones
		if (player.isInsideZone(ZoneId.PVP))
		{
			return true;
		}

		// Special hunting zones
		if (player.isInsideZone(ZoneId.SPECIAL_HUNTING))
		{
			return true;
		}

		// Heavenly Rift zones
		if (HeavenlyRiftManager.isInside(player))
		{
			return true;
		}

		// In instance
		if (player.isInInstance())
		{
			return true;
		}

		// In event
		if (player.isOnEvent())
		{
			return true;
		}

		// In Olympiad
		if (player.isInOlympiadMode())
		{
			return true;
		}

		// In observer mode
		if (player.inObserverMode())
		{
			return true;
		}

		return false;
	}
	
	/**
	 * Calculates the L-Coin cost for auto-resurrection based on usage count.
	 * Uses progressive cost system: cost increases gradually with each use.
	 *
	 * @param player the player
	 * @return the L-Coin cost
	 */
	private static int calculateResurrectionCost(PlayerInstance player)
	{
		ResurrectionFeesData.FeeHolder baseFee = ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeesData.ResurrectionFeeItem.LCOIN);
		if (baseFee == null)
		{
			return 100; // Default cost if not found
		}

		int baseCost = baseFee.getCost();
		int usesToday = player.getAutoPlaySettings().getAutoResurrectionUsesToday(player);

		// Progressive cost formula: baseCost * (1.0 + (usesToday * step))
		// Example with step 0.1: Use 1=100, Use 2=110, Use 3=120, etc.
		float multiplier = 1.0f + (usesToday * Config.AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_STEP);

		// Apply cap to prevent excessive costs
		if (multiplier > Config.AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_CAP)
		{
			multiplier = Config.AUTOPLAY_AUTO_RESURRECTION_COST_MULTIPLIER_CAP;
		}

		// Apply premium modifier (same as normal resurrection)
		double premiumMod = PremiumManager.getInstance().hasAnyPremiumBenefits(player) ?
			PremiumManager.getInstance().getEffectivePremiumRate(player, "RESURRECTION_COST") : 1.0;

		// Apply stat modifier (same as normal resurrection)
		double statMod = Math.max(0, player.getStat().getValue(Stat.RESURRECTION_FEE_MOD, 1));

		return (int) (baseCost * multiplier * premiumMod * statMod);
	}
	
	/**
	 * Performs auto-resurrection for the given player using paid resurrection logic.
	 * This ensures no EXP loss and proper resurrection handling.
	 *
	 * @param player the player to resurrect
	 */
	public static synchronized void performAutoResurrection(PlayerInstance player)
	{
		if (!canAutoResurrect(player))
		{
			return;
		}

		// Double-check player is actually dead
		if (!player.isDead())
		{
			return;
		}

		// Check if auto-resurrection is already in progress for this player
		if (player.getVariables().getBoolean("AUTO_RESURRECTION_IN_PROGRESS", false))
		{
			return;
		}

		// Mark auto-resurrection as in progress
		player.getVariables().set("AUTO_RESURRECTION_IN_PROGRESS", true);

		try
		{
			// Force paid resurrection (equivalent to case 9 in RequestRestartPoint)
			forcePaidResurrection(player);
		}
		finally
		{
			// Clear the flag after a delay to prevent immediate re-triggering
			ThreadPool.schedule(() -> {
				player.getVariables().remove("AUTO_RESURRECTION_IN_PROGRESS");
			}, 5000); // 5 second protection
		}
	}

	/**
	 * Forces paid resurrection for auto-resurrection (equivalent to case 9 in RequestRestartPoint).
	 * This ensures no EXP loss and proper resurrection handling.
	 *
	 * @param player the player to resurrect
	 */
	private static void forcePaidResurrection(PlayerInstance player)
	{
		// Save current location before resurrection
		ILocational currentLocation = player.getLocation();

		// For offline players, store hunting location if enabled
		if (player.isInOfflineMode() && Config.OFFLINE_PLAY_STORE_HUNTING_LOCATION)
		{
			// Store in PlayerVariables for persistence across server restarts
			player.getVariables().set("OFFLINE_HUNTING_LOCATION_X", currentLocation.getX());
			player.getVariables().set("OFFLINE_HUNTING_LOCATION_Y", currentLocation.getY());
			player.getVariables().set("OFFLINE_HUNTING_LOCATION_Z", currentLocation.getZ());
		}

		player.getAutoPlaySettings().setLastAutoPlayLocation(currentLocation);

		// Get resurrection fee data
		ResurrectionFeesData.FeeHolder baseFee = ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeesData.ResurrectionFeeItem.LCOIN);
		if (baseFee == null)
		{
			String message = "Auto-resurrection failed: Resurrection fee data not found.";
			player.sendMessage(message);
			LOGGER.warning("Auto-resurrection failed for " + player.getName() + " - resurrection fee data not found");
			return;
		}

		// Calculate and charge L-Coin cost
		int cost = calculateResurrectionCost(player);

		// Final check for L-Coin availability
		if (player.getInventory().getInventoryItemCount(Inventory.LCOIN_ID, -1) < cost)
		{
			// Handle insufficient L-Coin differently for offline vs online players
			handleInsufficientLCoin(player, cost);
			return;
		}

		// Charge L-Coin
		player.destroyItemByItemId("Auto-Resurrection", Inventory.LCOIN_ID, cost, player, true);

		// Update resurrection statistics (same as normal paid resurrection)
		player.getVariables().increaseInt(PlayerVariables.RESURRECTION_COUNT_LCOIN, 2, 1);

		// Update auto-resurrection statistics
		player.getAutoPlaySettings().setLastAutoResurrectionTime(player, System.currentTimeMillis());
		player.getAutoPlaySettings().incrementAutoResurrectionUsesToday(player);

		// Get resurrection power (same as normal paid resurrection)
		int power = baseFee.getRecovery();

		// Force L-Coin resurrection without dialog (bypass RequestRestartPoint)
		forceResurrectionWithLCoin(player, power);

		// Stop autoplay temporarily
		AutoPlayTaskManager.getInstance().stopAutoPlay(player);

		// Teleport to town
		Location townLocation = MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN);
		if (townLocation != null)
		{
			player.teleToLocation(townLocation);
		}
		else
		{
			// Fallback to a safe location if town location is null
			player.teleToLocation(new Location(83400, 147943, -3404)); // Giran as fallback
		}

		// Send message
		String successMessage = "Auto-resurrection activated! You will return to your hunting location in " +
			(Config.AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY / 60) + " minutes. Cost: " + cost + " L-Coin.";
		player.sendMessage(successMessage);

		// Schedule return to original location
		scheduleReturnToLocation(player, currentLocation);

		// Auto-resurrection completed successfully
	}

	/**
	 * Handles insufficient L-Coin for auto-resurrection.
	 *
	 * @param player the player
	 * @param cost the required cost
	 */
	private static void handleInsufficientLCoin(PlayerInstance player, int cost)
	{
		// Handle insufficient L-Coin differently for offline vs online players
		if (player.isInOfflineMode())
		{
			if (Config.OFFLINE_PLAY_LOGOUT_ON_INSUFFICIENT_LCOIN)
			{
				// Log out offline player using Disconnection
				String message = "Auto-resurrection failed: Not enough L-Coin. Logging out from offline mode.";
				player.sendMessage(message);
				club.projectessence.gameserver.network.Disconnection.of(player).logout(false, false);
				LOGGER.info("Offline player " + player.getName() + " logged out due to insufficient L-Coin for auto-resurrection");
			}
			else
			{
				// Disable auto-resurrection but keep player offline
				player.getAutoPlaySettings().setAutoResurrectionEnabled(player, false);
				String message = "Auto-resurrection disabled due to insufficient L-Coin. You will remain in offline mode.";
				player.sendMessage(message);
			}
		}
		else
		{
			// Online player handling
			String message1 = "Auto-resurrection failed: Not enough L-Coin. Required: " + cost + " L-Coin. Please purchase more L-Coin or disable auto-resurrection.";
			player.sendMessage(message1);

			// Disable auto-resurrection to prevent spam messages
			player.getAutoPlaySettings().setAutoResurrectionEnabled(player, false);
			String message2 = "Auto-resurrection has been automatically disabled due to insufficient L-Coin.";
			player.sendMessage(message2);
		}
	}
	
	/**
	 * Schedules the player's return to their original autoplay location.
	 * 
	 * @param player the player
	 * @param originalLocation the original location to return to
	 */
	private static void scheduleReturnToLocation(PlayerInstance player, ILocational originalLocation)
	{
		player.getAutoPlaySettings().setWaitingForReturn(true);
		player.getAutoPlaySettings().setReturnScheduledTime(System.currentTimeMillis() + (Config.AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY * 1000L));
		
		ScheduledFuture<?> returnTask = ThreadPool.schedule(() -> {
			if (player.isOnline() && player.getAutoPlaySettings().isWaitingForReturn())
			{
				returnToAutoPlayLocation(player, originalLocation);
			}
		}, Config.AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY * 1000L);
		
		// Store the task reference if needed for cancellation
		// Could be added to AutoPlaySettingsHolder if needed
	}
	
	/**
	 * Returns the player to their original autoplay location and resumes autoplay.
	 *
	 * @param player the player
	 * @param originalLocation the original location
	 */
	private static void returnToAutoPlayLocation(PlayerInstance player, ILocational originalLocation)
	{
		// Check if auto-resurrection is still enabled
		if (!player.getAutoPlaySettings().isAutoResurrectionEnabled(player))
		{
			String message = "Auto-resurrection was disabled. Return to hunting location cancelled.";
			player.sendMessage(message);
			player.getAutoPlaySettings().setWaitingForReturn(false);
			return;
		}

		// Check if player is still waiting for return (not cancelled)
		if (!player.getAutoPlaySettings().isWaitingForReturn())
		{
			String message = "Return to hunting location was cancelled.";
			player.sendMessage(message);
			return;
		}

		// Validate location is still accessible
		if (!isLocationValid(player, originalLocation))
		{
			String message = "Cannot return to original location - it may be blocked or unsafe.";
			player.sendMessage(message);
			player.getAutoPlaySettings().setWaitingForReturn(false);
			return;
		}
		
		// Teleport back to original location
		player.teleToLocation(originalLocation);
		
		// Wait a moment then resume autoplay
		ThreadPool.schedule(() -> {
			if (player.isOnline() && !player.isDead())
			{
				// Resume autoplay with previous settings
				AutoPlayTaskManager.getInstance().doAutoPlay(player,
					player.getAutoPlaySettings().doPickup(),
					player.getAutoPlaySettings().isLongRange(),
					player.getAutoPlaySettings().isRespectfulHunting(),
					player.getAutoPlaySettings().getNextTargetMode());

				if (player.isInOfflineMode())
				{
					LOGGER.info("Offline player " + player.getName() + " returned to autoplay location: " + originalLocation);
				}
				else
				{
					String message = "Returned to hunting location. Autoplay resumed!";
					player.sendMessage(message);
				}

				player.getAutoPlaySettings().setWaitingForReturn(false);
				LOGGER.info("Player " + player.getName() + " returned to autoplay location: " + originalLocation);
			}
		}, 3000); // 3 second delay to ensure teleport is complete
	}
	
	/**
	 * Validates if a location is still accessible and safe.
	 * 
	 * @param player the player
	 * @param location the location to validate
	 * @return true if location is valid, false otherwise
	 */
	private static boolean isLocationValid(PlayerInstance player, ILocational location)
	{
		// Check if location is reachable via geodata
		if (!GeoEngine.getInstance().canMoveToTarget(player.getX(), player.getY(), player.getZ(), 
			location.getX(), location.getY(), location.getZ(), player.getInstanceWorld()))
		{
			return false;
		}
		
		// Check if location is in a forbidden zone
		// This would require zone checking at specific coordinates
		// For now, we'll assume it's valid if geodata allows it
		
		return true;
	}
	
	/**
	 * Forces L-Coin resurrection without showing dialog.
	 * This bypasses the normal RequestRestartPoint flow.
	 *
	 * @param player the player to resurrect
	 * @param power the resurrection power (HP/MP/CP recovery percentage)
	 */
	private static void forceResurrectionWithLCoin(PlayerInstance player, int power)
	{
		if (!player.isDead())
		{
			return;
		}

		// Use the standard doRevive method with power percentage
		player.doRevive(power);

		// Clear any pending revive requests
		player.setIsPendingRevive(false);

		// Update player status
		player.updateEffectIcons(true);
		player.sendPacket(new EtcStatusUpdate(player));

		LOGGER.fine("Force L-Coin resurrection completed for " + player.getName() + " with " + power + "% recovery");
	}

	/**
	 * Schedules auto-resurrection when cooldown ends for players who died during cooldown.
	 * This ensures AFK players don't get stuck dead when cooldown expires.
	 *
	 * @param player the player who died during cooldown
	 * @param cooldownRemaining remaining cooldown time in milliseconds
	 */
	private static void schedulePendingAutoResurrection(PlayerInstance player, long cooldownRemaining)
	{
		// Only schedule if not already scheduled for this player
		String playerKey = "PENDING_AUTO_RES_" + player.getObjectId();

		// Cancel any existing pending resurrection for this player
		// (In a full implementation, you'd store these tasks in a Map to cancel them)

		ThreadPool.schedule(() -> {
			try
			{
				// Check if player is still dead and conditions are still met
				if (player.isOnline() && player.isDead() && canAutoResurrect(player))
				{
					performAutoResurrection(player);
				}
			}
			catch (Exception e)
			{
				// Ignore exceptions to prevent crashes
			}
		}, cooldownRemaining + 1000); // Add 1 second buffer to ensure cooldown is definitely over
	}

	/**
	 * Resets daily auto-resurrection usage for all players.
	 * Should be called daily at server reset time.
	 */
	public static void resetDailyUsage()
	{
		// This would typically be called by a scheduled task
		// For now, individual players reset their own counters
		LOGGER.info("Daily auto-resurrection usage reset completed.");
	}
}
