/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.appearance;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExShapeShiftingResult extends ServerPacket {
	public static final int RESULT_FAILED = 0x00;
	public static final int RESULT_SUCCESS = 0x01;
	public static final int RESULT_CLOSE = 0x02;

	public static final ExShapeShiftingResult FAILED = new ExShapeShiftingResult(RESULT_FAILED, 0, 0);
	public static final ExShapeShiftingResult CLOSE = new ExShapeShiftingResult(RESULT_CLOSE, 0, 0);

	private final int _result;
	private final int _targetItemId;
	private final int _extractItemId;

	public ExShapeShiftingResult(int result, int targetItemId, int extractItemId) {
		_result = result;
		_targetItemId = targetItemId;
		_extractItemId = extractItemId;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_SHAPE_SHIFTING_RESULT, buffer);

		buffer.writeInt(_result);
		buffer.writeInt(_targetItemId);
		buffer.writeInt(_extractItemId);
	}
}
