/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.gameassistant;

import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ExPremiumManagerShowHtml;

/**
 * <AUTHOR>
 */
public class ExPremiumManagerLinkHtml extends ClientPacket {
	private String _link;

	@Override
	public void readImpl() {
		_link = readString();
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (_link.isEmpty()) {
			LOGGER.warning("Player " + player.getName() + " sent empty game assistant html link!");
			return;
		}

		if (_link.contains("..")) {
			LOGGER.warning("Player " + player.getName() + " sent invalid game assistan html link " + _link);
			return;
		}

		String html = HtmCache.getInstance().getHtm(player, "data/html/GameAssistant/" + _link);
		if (html != null) {
			client.sendPacket(new ExPremiumManagerShowHtml(html));
		} else {
			LOGGER.warning(player + " requested invalid Game Assistant html: " + _link);
		}
	}
}
