/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.autoplay;

import java.awt.Color;

import club.projectessence.Config;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.autoplay.ExAutoPlaySettingSend;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

/**
 * <AUTHOR>
 */
public class ExAutoPlaySetting extends ClientPacket
{
	@SuppressWarnings("unused")
	private int				_options;
	private boolean			_active;
	private boolean			_pickUp;
	private NextTargetMode	_nextTargetMode;
	private boolean			_longRange;
	private int				_potionData;
	private int				_petPotionPercent;
	private boolean			_respectfulHunting;
	
	@Override
	public void readImpl()
	{
		_options = readShort();
		_active = readByte() == 1;
		_pickUp = readByte() == 1;
		int nextTargetMode = readShort();
		if (nextTargetMode > (NextTargetMode.values().length - 1))
		{
			return;
		}
		_nextTargetMode = NextTargetMode.values()[nextTargetMode];
		_longRange = readByte() == 0;
		_potionData = readInt();
		_petPotionPercent = readInt();
		_respectfulHunting = readByte() == 1;
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (Config.CUSTOM_INTERFACE_ENABLED)
		{
			player.getAutoPlaySettings().setAutoHpPotionPercent(_potionData & 255);
			player.getAutoPlaySettings().setAutoHp2PotionPercent((_potionData >> 8) & 255);
			player.getAutoPlaySettings().setAutoMpPotionPercent((_potionData >> 16) & 255);
			player.getAutoPlaySettings().setAutoCpPotionPercent((_potionData >> 24) & 255);
		}
		else
		{
			player.getAutoPlaySettings().setAutoHpPotionPercent(_potionData);
			player.getAutoPlaySettings().setAutoMpPotionPercent(_potionData);
		}
		player.getAutoPlaySettings().setPetAutoPotionPercent(_petPotionPercent);
		if (_active)
		{
			AutoPlayTaskManager.getInstance().doAutoPlay(player, _pickUp, _longRange, _respectfulHunting, _nextTargetMode);
		}
		else
		{
			if (!AutoPlayTaskManager.getInstance().isPlayerAutoHunting(player))
			{
				player.sendPacket(new ExAutoPlaySettingSend(16, false, _pickUp, _nextTargetMode, _longRange, _potionData, _petPotionPercent, _respectfulHunting));
			}
			else
			{
				AutoPlayTaskManager.getInstance().stopAutoPlay(player);
			}
		}
	}
	
	public static enum NextTargetMode
	{
		ANY_TARGET(Color.YELLOW),
		MONSTER(Color.GREEN),
		CHARACTERS(Color.MAGENTA),
		NPC(Color.GREEN),
		PAYBACK_ATTACK(Color.RED);
		
		private final Color _color;
		
		private NextTargetMode(Color color)
		{
			_color = color;
		}
		
		public Color getColor()
		{
			return _color;
		}
	}
}
