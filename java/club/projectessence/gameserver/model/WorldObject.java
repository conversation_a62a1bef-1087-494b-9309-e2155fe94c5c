/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.handler.ActionHandler;
import club.projectessence.gameserver.handler.ActionShiftHandler;
import club.projectessence.gameserver.handler.IActionHandler;
import club.projectessence.gameserver.handler.IActionShiftHandler;
import club.projectessence.gameserver.instancemanager.IdManager;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.interfaces.IDecayable;
import club.projectessence.gameserver.model.interfaces.IIdentifiable;
import club.projectessence.gameserver.model.interfaces.ILocational;
import club.projectessence.gameserver.model.interfaces.INamable;
import club.projectessence.gameserver.model.interfaces.IPositionable;
import club.projectessence.gameserver.model.interfaces.ISpawnable;
import club.projectessence.gameserver.model.interfaces.IUniqueId;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.DeleteObject;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import club.projectessence.gameserver.util.Util;

/**
 * Base class for all interactive objects.
 */
public abstract class WorldObject extends ListenersContainer implements IIdentifiable, INamable, ISpawnable, IUniqueId, IDecayable, IPositionable
{
	/**
	 * Name
	 */
	private String				_name;
	/**
	 * Object ID
	 */
	private int					_objectId;
	/**
	 * World Region
	 */
	private WorldRegion			_worldRegion;
	/**
	 * Instance type
	 */
	private InstanceType		_instanceType;
	private Map<String, Object>	_scripts;
	/**
	 * X coordinate
	 */
	private volatile int		_x				= 0;
	/**
	 * Y coordinate
	 */
	private volatile int		_y				= 0;
	/**
	 * Z coordinate
	 */
	private volatile int		_z				= -10000;
	/**
	 * Orientation
	 */
	private volatile int		_heading		= 0;
	/**
	 * Instance id of object. 0 - Global
	 */
	private Instance			_instance;
	private boolean				_isSpawned;
	private boolean				_isInvisible;
	private boolean				_isTargetable	= true;
	
	public WorldObject(int objectId)
	{
		setInstanceType(InstanceType.WorldObject);
		_objectId = objectId;
	}
	
	/**
	 * Gets the instance type of object.
	 *
	 * @return the instance type
	 */
	public InstanceType getInstanceType()
	{
		return _instanceType;
	}
	
	/**
	 * Sets the instance type.
	 *
	 * @param newInstanceType
	 *            the instance type to set
	 */
	protected final void setInstanceType(InstanceType newInstanceType)
	{
		_instanceType = newInstanceType;
	}
	
	/**
	 * Verifies if object is of any given instance types.
	 *
	 * @param instanceTypes
	 *            the instance types to verify
	 * @return {@code true} if object is of any given instance types, {@code false} otherwise
	 */
	public boolean isInstanceTypes(InstanceType... instanceTypes)
	{
		return _instanceType.isTypes(instanceTypes);
	}
	
	public void onAction(PlayerInstance player)
	{
		onAction(player, true);
	}
	
	public void onAction(PlayerInstance player, boolean interact)
	{
		final IActionHandler handler = ActionHandler.getInstance().getHandler(getInstanceType());
		if (handler != null)
		{
			handler.action(player, this, interact);
		}
		player.sendPacket(ActionFailed.STATIC_PACKET);
	}
	
	public void onActionShift(PlayerInstance player)
	{
		final IActionShiftHandler handler = ActionShiftHandler.getInstance().getHandler(getInstanceType());
		if (handler != null)
		{
			handler.action(player, this, true);
		}
		player.sendPacket(ActionFailed.STATIC_PACKET);
	}
	
	public void onForcedAttack(PlayerInstance player)
	{
		player.sendPacket(ActionFailed.STATIC_PACKET);
	}
	
	public void onSpawn()
	{}
	
	@Override
	public boolean decayMe()
	{
		_isSpawned = false;
		World.getInstance().removeVisibleObject(this, _worldRegion);
		World.getInstance().removeObject(this);
		return true;
	}
	
	public void refreshId()
	{
		World.getInstance().removeObject(this);
		IdManager.getInstance().releaseId(getObjectId());
		_objectId = IdManager.getInstance().getNextId();
	}
	
	@Override
	public boolean spawnMe()
	{
		synchronized (this)
		{
			// Set the x,y,z position of the WorldObject spawn and update its _worldregion
			_isSpawned = true;
			setWorldRegion(World.getInstance().getRegion(this));
			// Add the WorldObject spawn in the _allobjects of World
			World.getInstance().addObject(this);
			// Add the WorldObject spawn to _visibleObjects and if necessary to _allplayers of its WorldRegion
			_worldRegion.addVisibleObject(this);
		}
		// this can synchronize on others instances, so it's out of synchronized, to avoid deadlocks
		// Add the WorldObject spawn in the world as a visible object
		World.getInstance().addVisibleObject(this, getWorldRegion());
		onSpawn();
		return true;
	}
	
	public void spawnMe(int x, int y, int z)
	{
		synchronized (this)
		{
			int spawnX = x;
			if (spawnX > World.MAP_MAX_X)
			{
				spawnX = World.MAP_MAX_X - 5000;
			}
			if (spawnX < World.MAP_MIN_X)
			{
				spawnX = World.MAP_MIN_X + 5000;
			}
			int spawnY = y;
			if (spawnY > World.MAP_MAX_Y)
			{
				spawnY = World.MAP_MAX_Y - 5000;
			}
			if (spawnY < World.MAP_MIN_Y)
			{
				spawnY = World.MAP_MIN_Y + 5000;
			}
			// Set the x,y,z position of the WorldObject. If flagged with _isSpawned, setXYZ will automatically update world region, so avoid that.
			setXYZ(spawnX, spawnY, z);
		}
		// Spawn and update its _worldregion
		spawnMe();
	}
	
	/**
	 * Verify if object can be attacked.
	 *
	 * @return {@code true} if object can be attacked, {@code false} otherwise
	 */
	public boolean canBeAttacked()
	{
		return false;
	}
	
	public abstract boolean isAutoAttackable(Creature attacker);
	
	public boolean isSpawned()
	{
		return _isSpawned;
	}
	
	public void setSpawned(boolean value)
	{
		_isSpawned = value;
	}
	
	@Override
	public String getName()
	{
		return _name;
	}
	
	public void setName(String value)
	{
		_name = value;
	}
	
	@Override
	public int getObjectId()
	{
		return _objectId;
	}
	
	public abstract void sendInfo(PlayerInstance player);
	
	public void sendPacket(ServerPacket packet)
	{}
	
	public void sendPacket(SystemMessageId id)
	{}
	
	public PlayerInstance getActingPlayer()
	{
		return null;
	}
	
	/**
	 * Verify if object is instance of Attackable.
	 *
	 * @return {@code true} if object is instance of Attackable, {@code false} otherwise
	 */
	public boolean isAttackable()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of Creature.
	 *
	 * @return {@code true} if object is instance of Creature, {@code false} otherwise
	 */
	public boolean isCreature()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of DoorInstance.
	 *
	 * @return {@code true} if object is instance of DoorInstance, {@code false} otherwise
	 */
	public boolean isDoor()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of ArtefactInstance.
	 *
	 * @return {@code true} if object is instance of ArtefactInstance, {@code false} otherwise
	 */
	public boolean isArtefact()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of MonsterInstance.
	 *
	 * @return {@code true} if object is instance of MonsterInstance, {@code false} otherwise
	 */
	public boolean isMonster()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of Npc.
	 *
	 * @return {@code true} if object is instance of Npc, {@code false} otherwise
	 */
	public boolean isNpc()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of PetInstance.
	 *
	 * @return {@code true} if object is instance of PetInstance, {@code false} otherwise
	 */
	public boolean isPet()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of PlayerInstance.
	 *
	 * @return {@code true} if object is instance of PlayerInstance, {@code false} otherwise
	 */
	public boolean isPlayer()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of Playable.
	 *
	 * @return {@code true} if object is instance of Playable, {@code false} otherwise
	 */
	public boolean isPlayable()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of ServitorInstance.
	 *
	 * @return {@code true} if object is instance of ServitorInstance, {@code false} otherwise
	 */
	public boolean isServitor()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of Summon.
	 *
	 * @return {@code true} if object is instance of Summon, {@code false} otherwise
	 */
	public boolean isSummon()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of TrapInstance.
	 *
	 * @return {@code true} if object is instance of TrapInstance, {@code false} otherwise
	 */
	public boolean isTrap()
	{
		return false;
	}
	
	/**
	 * Verify if object is instance of ItemInstance.
	 *
	 * @return {@code true} if object is instance of ItemInstance, {@code false} otherwise
	 */
	public boolean isItem()
	{
		return false;
	}
	
	/**
	 * Verifies if the object is a walker NPC.
	 *
	 * @return {@code true} if object is a walker NPC, {@code false} otherwise
	 */
	public boolean isWalker()
	{
		return false;
	}
	
	/**
	 * Verifies if this object is a vehicle.
	 *
	 * @return {@code true} if object is Vehicle, {@code false} otherwise
	 */
	public boolean isVehicle()
	{
		return false;
	}
	
	/**
	 * @return {@code true} if the object can be targetted by other players, {@code false} otherwise.
	 */
	public boolean isTargetable()
	{
		return _isTargetable;
	}
	
	public void setTargetable(boolean targetable)
	{
		if (_isTargetable != targetable)
		{
			_isTargetable = targetable;
			if (!targetable)
			{
				World.getInstance().forEachVisibleObject(this, Creature.class, creature ->
				{
					if (creature.getTarget() == this)
					{
						creature.setTarget(null);
						creature.abortAttack();
						creature.abortCast();
					}
				});
			}
		}
	}
	
	/**
	 * Check if the object is in the given zone Id.
	 *
	 * @param zone
	 *            the zone Id to check
	 * @return {@code true} if the object is in that zone Id
	 */
	public boolean isInsideZone(ZoneId zone)
	{
		return false;
	}
	
	/**
	 * @param <T>
	 * @param script
	 * @return
	 */
	public <T> T addScript(T script)
	{
		if (_scripts == null)
		{
			// Double-checked locking
			synchronized (this)
			{
				if (_scripts == null)
				{
					_scripts = new ConcurrentHashMap<>();
				}
			}
		}
		_scripts.put(script.getClass().getName(), script);
		return script;
	}
	
	/**
	 * @param <T>
	 * @param script
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public <T> T removeScript(Class<T> script)
	{
		if (_scripts == null)
		{
			return null;
		}
		return (T) _scripts.remove(script.getName());
	}
	
	/**
	 * @param <T>
	 * @param script
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public <T> T getScript(Class<T> script)
	{
		if (_scripts == null)
		{
			return null;
		}
		return (T) _scripts.get(script.getName());
	}
	
	public void removeStatusListener(Creature object)
	{}
	
	public void setXYZInvisible(int x, int y, int z)
	{
		int correctX = x;
		if (correctX > World.MAP_MAX_X)
		{
			correctX = World.MAP_MAX_X - 5000;
		}
		if (correctX < World.MAP_MIN_X)
		{
			correctX = World.MAP_MIN_X + 5000;
		}
		int correctY = y;
		if (correctY > World.MAP_MAX_Y)
		{
			correctY = World.MAP_MAX_Y - 5000;
		}
		if (correctY < World.MAP_MIN_Y)
		{
			correctY = World.MAP_MIN_Y + 5000;
		}
		setXYZ(correctX, correctY, z);
		setSpawned(false);
	}
	
	public void setLocationInvisible(ILocational loc)
	{
		setXYZInvisible(loc.getX(), loc.getY(), loc.getZ());
	}
	
	public WorldRegion getWorldRegion()
	{
		return _worldRegion;
	}
	
	public void setWorldRegion(WorldRegion region)
	{
		if ((region == null) && (_worldRegion != null))
		{
			_worldRegion.removeVisibleObject(this);
		}
		_worldRegion = region;
	}
	
	/**
	 * Gets the X coordinate.
	 *
	 * @return the X coordinate
	 */
	@Override
	public int getX()
	{
		return _x;
	}
	
	/**
	 * Gets the Y coordinate.
	 *
	 * @return the Y coordinate
	 */
	@Override
	public int getY()
	{
		return _y;
	}
	
	/**
	 * Gets the Z coordinate.
	 *
	 * @return the Z coordinate
	 */
	@Override
	public int getZ()
	{
		return _z;
	}
	
	/**
	 * Gets the heading.
	 *
	 * @return the heading
	 */
	@Override
	public int getHeading()
	{
		return _heading;
	}
	
	/**
	 * Sets heading of object.
	 *
	 * @param newHeading
	 *            the new heading
	 */
	@Override
	public void setHeading(int newHeading)
	{
		_heading = newHeading;
	}
	
	/**
	 * Gets the instance ID.
	 *
	 * @return the instance ID
	 */
	public int getInstanceId()
	{
		final Instance instance = _instance;
		return (instance != null) ? instance.getId() : 0;
	}
	
	/**
	 * Check if object is inside instance world.
	 *
	 * @return {@code true} when object is inside any instance world, otherwise {@code false}
	 */
	public boolean isInInstance()
	{
		return _instance != null;
	}
	
	/**
	 * Get instance world where object is currently located.
	 *
	 * @return {@link Instance} if object is inside instance world, otherwise {@code null}
	 */
	public Instance getInstanceWorld()
	{
		return _instance;
	}
	
	/**
	 * Gets the location object.
	 *
	 * @return the location object
	 */
	@Override
	public Location getLocation()
	{
		return new Location(_x, _y, _z, _heading);
	}
	
	/**
	 * Sets location of object.
	 *
	 * @param loc
	 *            the location object
	 */
	@Override
	public void setLocation(Location loc)
	{
		_x = loc.getX();
		_y = loc.getY();
		_z = loc.getZ();
		_heading = loc.getHeading();
	}
	
	/**
	 * Sets the x, y, z coordinate.
	 *
	 * @param newX
	 *            the X coordinate
	 * @param newY
	 *            the Y coordinate
	 * @param newZ
	 *            the Z coordinate
	 */
	@Override
	public void setXYZ(int newX, int newY, int newZ)
	{
		_x = newX;
		_y = newY;
		_z = newZ;
		if (_isSpawned)
		{
			final WorldRegion newRegion = World.getInstance().getRegion(this);
			if ((newRegion != null) && (newRegion != _worldRegion))
			{
				if (_worldRegion != null)
				{
					_worldRegion.removeVisibleObject(this);
				}
				newRegion.addVisibleObject(this);
				World.getInstance().switchRegion(this, newRegion);
				setWorldRegion(newRegion);
			}
		}
	}
	
	/**
	 * Sets the x, y, z coordinate.
	 *
	 * @param loc
	 *            the location object
	 */
	@Override
	public void setXYZ(ILocational loc)
	{
		setXYZ(loc.getX(), loc.getY(), loc.getZ());
	}
	
	/**
	 * Sets instance for current object by instance ID.
	 *
	 * @param id
	 *            ID of instance world which should be set (0 means normal world)
	 */
	public void setInstanceById(int id)
	{
		final Instance instance = InstanceManager.getInstance().getInstance(id);
		if ((id != 0) && (instance == null))
		{
			return;
		}
		setInstance(instance);
	}
	
	/**
	 * Sets instance where current object belongs.
	 *
	 * @param newInstance
	 *            new instance world for object
	 */
	public synchronized void setInstance(Instance newInstance)
	{
		// Check if new and old instances are identical
		if (_instance == newInstance)
		{
			return;
		}
		// Leave old instance
		if (_instance != null)
		{
			_instance.onInstanceChange(this, false);
		}
		// Set new instance
		_instance = newInstance;
		// Enter into new instance
		if (newInstance != null)
		{
			newInstance.onInstanceChange(this, true);
		}
	}
	
	/**
	 * Calculates 2D distance between this WorldObject and given x, y, z.
	 *
	 * @param x
	 *            the X coordinate
	 * @param y
	 *            the Y coordinate
	 * @param z
	 *            the Z coordinate
	 * @return distance between object and given x, y, z.
	 */
	public double calculateDistance2D(int x, int y, int z)
	{
		return Math.sqrt(Math.pow(x - _x, 2) + Math.pow(y - _y, 2));
	}
	
	/**
	 * Calculates the 2D distance between this WorldObject and given location.
	 *
	 * @param loc
	 *            the location object
	 * @return distance between object and given location.
	 */
	public double calculateDistance2D(ILocational loc)
	{
		return calculateDistance2D(loc.getX(), loc.getY(), loc.getZ());
	}
	
	/**
	 * Calculates the 3D distance between this WorldObject and given x, y, z.
	 *
	 * @param x
	 *            the X coordinate
	 * @param y
	 *            the Y coordinate
	 * @param z
	 *            the Z coordinate
	 * @return distance between object and given x, y, z.
	 */
	public double calculateDistance3D(int x, int y, int z)
	{
		return Math.sqrt(Math.pow(x - _x, 2) + Math.pow(y - _y, 2) + Math.pow(z - _z, 2));
	}
	
	/**
	 * Calculates 3D distance between this WorldObject and given location.
	 *
	 * @param loc
	 *            the location object
	 * @return distance between object and given location.
	 */
	public double calculateDistance3D(ILocational loc)
	{
		return calculateDistance3D(loc.getX(), loc.getY(), loc.getZ());
	}
	
	/**
	 * Calculates the Z distance between this WorldObject and given z.
	 * 
	 * @param z
	 *            the Z coordinate
	 * @return distance between object and given x, y, z.
	 */
	public double calculateDistanceZ(int z)
	{
		return Math.sqrt(Math.pow(z - _z, 2));
	}
	
	/**
	 * Calculates Z distance between this WorldObject and given location.
	 * 
	 * @param loc
	 *            the location object
	 * @return distance between object and given location.
	 */
	public double calculateDistanceZ(ILocational loc)
	{
		return calculateDistanceZ(loc.getZ());
	}
	
	/**
	 * Calculates the non squared 2D distance between this WorldObject and given x, y, z.
	 *
	 * @param x
	 *            the X coordinate
	 * @param y
	 *            the Y coordinate
	 * @param z
	 *            the Z coordinate
	 * @return distance between object and given x, y, z.
	 */
	public double calculateDistanceSq2D(int x, int y, int z)
	{
		return Math.pow(x - _x, 2) + Math.pow(y - _y, 2);
	}
	
	/**
	 * Calculates the non squared 2D distance between this WorldObject and given location.
	 *
	 * @param loc
	 *            the location object
	 * @return distance between object and given location.
	 */
	public double calculateDistanceSq2D(ILocational loc)
	{
		return calculateDistanceSq2D(loc.getX(), loc.getY(), loc.getZ());
	}
	
	/**
	 * Calculates the non squared 3D distance between this WorldObject and given x, y, z.
	 *
	 * @param x
	 *            the X coordinate
	 * @param y
	 *            the Y coordinate
	 * @param z
	 *            the Z coordinate
	 * @return distance between object and given x, y, z.
	 */
	public double calculateDistanceSq3D(int x, int y, int z)
	{
		return Math.pow(x - _x, 2) + Math.pow(y - _y, 2) + Math.pow(z - _z, 2);
	}
	
	/**
	 * Calculates the non squared 3D distance between this WorldObject and given location.
	 *
	 * @param loc
	 *            the location object
	 * @return distance between object and given location.
	 */
	public double calculateDistanceSq3D(ILocational loc)
	{
		return calculateDistanceSq3D(loc.getX(), loc.getY(), loc.getZ());
	}
	
	/**
	 * Calculates the angle in degrees from this object to the given object.<br>
	 * The return value can be described as how much this object has to turn<br>
	 * to have the given object directly in front of it.
	 *
	 * @param target
	 *            the object to which to calculate the angle
	 * @return the angle this object has to turn to have the given object in front of it
	 */
	public double calculateDirectionTo(ILocational target)
	{
		return Util.calculateAngleFrom(this, target);
	}
	
	/**
	 * @return {@code true} if this object is invisible, {@code false} otherwise.
	 */
	public boolean isInvisible()
	{
		return _isInvisible;
	}
	
	/**
	 * Sets this object as invisible or not
	 *
	 * @param invis
	 */
	public void setInvisible(boolean invis)
	{
		_isInvisible = invis;
		if (invis)
		{
			final DeleteObject deletePacket = new DeleteObject(this);
			World.getInstance().forEachVisibleObject(this, PlayerInstance.class, player ->
			{
				if (!isVisibleFor(player))
				{
					player.sendPacket(deletePacket);
				}
			});
		}
		// Broadcast information regarding the object to those which are suppose to see.
		broadcastInfo();
	}
	
	/**
	 * @param player
	 * @return {@code true} if player can see an invisible object if it's invisible, {@code false} otherwise.
	 */
	public boolean isVisibleFor(PlayerInstance player)
	{
		// Nếu object này là GM
		if (this.isPlayer() && this.getActingPlayer().isGM())
		{
			// GM tàng hình chỉ hiển thị cho GM khác (bảo mật)
			if (_isInvisible)
			{
				return player.isGM();
			}
			// GM không tàng hình thì tất cả đều nhìn thấy
			else
			{
				return true;
			}
		}
		// Người chơi thường tàng hình chỉ hiển thị cho GM hoặc người có SEE_INVISIBLE
		// Lưu ý: SEE_INVISIBLE chỉ hoạt động với player thường, không ảnh hưởng đến GM
		return !_isInvisible || player.isGM() || player.canSeeInvisiblePeopleNotGM();
	}
	
	/**
	 * Broadcasts describing info to known players.
	 */
	public void broadcastInfo()
	{
		World.getInstance().forEachVisibleObject(this, PlayerInstance.class, player ->
		{
			if (isVisibleFor(player))
			{
				sendInfo(player);
			}
		});
	}
	
	public boolean isInvul()
	{
		return false;
	}
	
	public boolean isInSurroundingRegion(WorldObject worldObject)
	{
		if (worldObject == null)
		{
			return false;
		}
		final WorldRegion worldRegion = worldObject.getWorldRegion();
		if (worldRegion == null)
		{
			return false;
		}
		if (_worldRegion == null)
		{
			return false;
		}
		return worldRegion.isSurroundingRegion(_worldRegion);
	}
	
	@Override
	public boolean equals(Object obj)
	{
		return (obj instanceof WorldObject) && (((WorldObject) obj).getObjectId() == getObjectId());
	}
	
	@Override
	public String toString()
	{
		return getClass().getSimpleName() + ":" + _name + "[" + _objectId + "]";
	}
	
	public boolean isFakePlayer()
	{
		return false;
	}
}
