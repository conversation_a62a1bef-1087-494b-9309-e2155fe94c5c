package club.projectessence.gameserver.network.clientpackets.balok;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.balok.BalrogWarShowUI;

public class ExBalrogWarShowUI extends ClientPacket
{
	@Override
	protected void runImpl() throws Exception
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		player.sendPacket(new BalrogWarShowUI(player));
	}
	
	@Override
	protected void readImpl() throws Exception
	{
		// TODO Auto-generated method stub
	}
}
