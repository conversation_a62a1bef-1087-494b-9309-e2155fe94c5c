/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.events.impl.creature.player;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.IBaseEvent;
import club.projectessence.gameserver.model.itemcontainer.ItemContainer;
import club.projectessence.gameserver.model.items.instance.ItemInstance;

/**
 * <AUTHOR>
 */
public class OnPlayerClanWHItemAdd implements IBaseEvent {
	private final String _process;
	private final PlayerInstance _player;
	private final ItemInstance _item;
	private final ItemContainer _container;

	public OnPlayerClanWHItemAdd(String process, PlayerInstance player, ItemInstance item, ItemContainer container) {
		_process = process;
		_player = player;
		_item = item;
		_container = container;
	}

	public String getProcess() {
		return _process;
	}

	public PlayerInstance getPlayer() {
		return _player;
	}

	public ItemInstance getItem() {
		return _item;
	}

	public ItemContainer getContainer() {
		return _container;
	}

	@Override
	public EventType getType() {
		return EventType.ON_PLAYER_CLAN_WH_ITEM_ADD;
	}
}
