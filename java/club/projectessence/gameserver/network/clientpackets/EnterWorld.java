/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.gameserver.LoginServerThread;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.data.sql.AnnouncementsTable;
import club.projectessence.gameserver.data.sql.OfflineTraderTable;
import club.projectessence.gameserver.data.xml.AdminData;
import club.projectessence.gameserver.data.xml.BalthusEventData;
import club.projectessence.gameserver.data.xml.BeautyShopData;
import club.projectessence.gameserver.data.xml.ClanHallData;
import club.projectessence.gameserver.data.xml.ItemDeletionData;
import club.projectessence.gameserver.data.xml.L2PassData;
import club.projectessence.gameserver.data.xml.LetterCollectorData;
import club.projectessence.gameserver.data.xml.MableGameEventData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.StatusUpdateType;
import club.projectessence.gameserver.enums.SubclassInfoType;
import club.projectessence.gameserver.enums.TeleportWhereType;
import club.projectessence.gameserver.features.museum.MuseumManager;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.CursedWeaponsManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.instancemanager.FortSiegeManager;
import club.projectessence.gameserver.instancemanager.InstanceManager;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.instancemanager.MapRegionManager;
import club.projectessence.gameserver.instancemanager.PetitionManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.instancemanager.RandomCraftPremiumManager;
import club.projectessence.gameserver.instancemanager.ServerRestartManager;
import club.projectessence.gameserver.instancemanager.SiegeManager;
import club.projectessence.gameserver.instancemanager.UniqueOnlineManager;
import club.projectessence.gameserver.instancemanager.WorldExchangeManager;
import club.projectessence.gameserver.instancemanager.events.GameEvent;
import club.projectessence.gameserver.instancemanager.events.GoldFestival.GoldFestivalEvent;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.holders.AttendanceInfoHolder;
import club.projectessence.gameserver.model.holders.ClientHardwareInfoHolder;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.model.quest.Quest;
import club.projectessence.gameserver.model.residences.ClanHall;
import club.projectessence.gameserver.model.siege.FortSiege;
import club.projectessence.gameserver.model.siege.Siege;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.model.variables.AccountVariables;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.instancemanager.PremiumManager;

import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.ConnectionState;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.DeleteObject;
import club.projectessence.gameserver.network.serverpackets.Die;
import club.projectessence.gameserver.network.serverpackets.EtcStatusUpdate;
import club.projectessence.gameserver.network.serverpackets.ExAutoSoulShot;
import club.projectessence.gameserver.network.serverpackets.ExBasicActionList;
import club.projectessence.gameserver.network.serverpackets.ExBeautyItemList;
import club.projectessence.gameserver.network.serverpackets.ExEnterWorld;
import club.projectessence.gameserver.network.serverpackets.ExGetBookMarkInfoPacket;
import club.projectessence.gameserver.network.serverpackets.ExItemAnnounceSetting;
import club.projectessence.gameserver.network.serverpackets.ExItemDeletionInfo;
import club.projectessence.gameserver.network.serverpackets.ExNoticePostArrived;
import club.projectessence.gameserver.network.serverpackets.ExNotifyPremiumItem;
import club.projectessence.gameserver.network.serverpackets.ExPCCafePointInfo;
import club.projectessence.gameserver.network.serverpackets.ExQuestItemList;
import club.projectessence.gameserver.network.serverpackets.ExRotation;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.ExStorageMaxCount;
import club.projectessence.gameserver.network.serverpackets.ExSubjobInfo;
import club.projectessence.gameserver.network.serverpackets.ExUnReadMailCount;
import club.projectessence.gameserver.network.serverpackets.ExUserInfoAbnormalVisualEffect;
import club.projectessence.gameserver.network.serverpackets.ExUserInfoEquipSlot;
import club.projectessence.gameserver.network.serverpackets.ExUserInfoInvenWeight;
import club.projectessence.gameserver.network.serverpackets.ExVitalExInfo;
import club.projectessence.gameserver.network.serverpackets.ExVoteSystemInfo;
import club.projectessence.gameserver.network.serverpackets.ExWorldChatCnt;
import club.projectessence.gameserver.network.serverpackets.InventoryUpdate;
import club.projectessence.gameserver.network.serverpackets.ItemList;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListAll;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListUpdate;
import club.projectessence.gameserver.network.serverpackets.PledgeSkillList;
import club.projectessence.gameserver.network.serverpackets.QuestList;
import club.projectessence.gameserver.network.serverpackets.ShortCutInit;
import club.projectessence.gameserver.network.serverpackets.SkillCoolTime;
import club.projectessence.gameserver.network.serverpackets.SkillList;
import club.projectessence.gameserver.network.serverpackets.StatusUpdate;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.UserInfo;
import club.projectessence.gameserver.network.serverpackets.attendance.ExVipAttendanceItemList;
import club.projectessence.gameserver.network.serverpackets.collection.ExCollectionInfo;
import club.projectessence.gameserver.network.serverpackets.dailymission.ExConnectedTimeAndGettableReward;
import club.projectessence.gameserver.network.serverpackets.dailymission.ExOneDayReceiveRewardList;
import club.projectessence.gameserver.network.serverpackets.elementalspirits.ElementalSpiritInfo;
import club.projectessence.gameserver.network.serverpackets.friend.L2FriendList;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExInitGlobalEventUI;
import club.projectessence.gameserver.network.serverpackets.l2pass.ExL2PassSimpleInfo;
import club.projectessence.gameserver.network.serverpackets.lettercollector.ExLetterCollectorUILauncher;
import club.projectessence.gameserver.network.serverpackets.limitshop.ExBloodyCoinCount;
import club.projectessence.gameserver.network.serverpackets.mablegame.ExMableGameUILauncher;
import club.projectessence.gameserver.network.serverpackets.magiclamp.ExMagicLampExpInfo;
import club.projectessence.gameserver.network.serverpackets.olympiad.ExOlympiadInfo;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeCoinInfo;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeContributionList;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeCount;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeWaitingListAlarm;
import club.projectessence.gameserver.network.serverpackets.randomcraft.ExCraftInfo;
import club.projectessence.gameserver.network.serverpackets.resurrectionsettings.ExUserRestartLockerList;
import club.projectessence.gameserver.network.serverpackets.steadybox.ExSteadyBoxUiInit;
import club.projectessence.gameserver.network.serverpackets.subjugation.ExSubjugationSidebar;
import club.projectessence.gameserver.network.serverpackets.surveillance.ExUserWatcherTargetList;
import club.projectessence.gameserver.network.serverpackets.surveillance.ExUserWatcherTargetStatus;
import club.projectessence.gameserver.util.BuilderUtil;
import custom.gve.service.FactionBalanceService;
import gabriel.eventEngine.interf.GabrielEvents;

/**
 * Enter World Packet Handler
 * <p>
 * <p>
 * 0000: 03
 * <p>
 * packet format rev87 bddddbdcccccccccccccccccccc
 * <p>
 */
public class EnterWorld extends ClientPacket
{
	private static final Map<String, ClientHardwareInfoHolder>	TRACE_HWINFO	= new ConcurrentHashMap<>();
	private final int[][]										_tracert		= new int[5][4];
	
	@Override
	public void readImpl()
	{
		for (int i = 0; i < 5; i++)
		{
			for (int o = 0; o < 4; o++)
			{
				_tracert[i][o] = readByte();
			}
		}
		readInt(); // Unknown Value
		readInt(); // Unknown Value
		readInt(); // Unknown Value
		readInt(); // Unknown Value
		readBytes(new byte[64]); // Unknown Byte Array
		readInt(); // Unknown Value
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			LOGGER.warning("EnterWorld failed! player returned 'null'.");
			Disconnection.of(client).logout(false, false);
			return;
		}
		client.setConnectionState(ConnectionState.IN_GAME);
		final String[] adress = new String[5];
		for (int i = 0; i < 5; i++)
		{
			adress[i] = _tracert[i][0] + "." + _tracert[i][1] + "." + _tracert[i][2] + "." + _tracert[i][3];
		}
		LoginServerThread.getInstance().sendClientTracert(player.getAccountName(), adress);
		client.setClientTracert(_tracert);
		// Send packet that olympiad is opened.
		if (Config.OLY_ENABLED)
		{
			final long remain = Olympiad.getInstance().inCompPeriod() ? Olympiad.getInstance().getMillisToCompEnd() : 0;
			player.sendPacket(new ExOlympiadInfo(remain));
		}
		player.sendPacket(new UserInfo(player));
		// Restore to instanced area if enabled
		if (Config.RESTORE_PLAYER_INSTANCE)
		{
			final PlayerVariables vars = player.getVariables();
			final Instance instance = InstanceManager.getInstance().getPlayerInstance(player, false);
			if ((instance != null) && (instance.getId() == vars.getInt(PlayerVariables.INSTANCE_RESTORE, 0)))
			{
				player.setInstance(instance);
			}
			vars.set(PlayerVariables.INSTANCE_RESTORE, 0);
		}
		player.updatePvpTitleAndColor(false);
		// Apply special GM properties to the GM when entering
		if (player.isGM())
		{
			gmStartupProcess:
			{
				if (Config.GM_STARTUP_BUILDER_HIDE && AdminData.getInstance().hasAccess("admin_hide", player.getAccessLevel()))
				{
					BuilderUtil.setHiding(player, true);
					BuilderUtil.sendSysMessage(player, "hide is default for builder.");
					BuilderUtil.sendSysMessage(player, "FriendAddOff is default for builder.");
					BuilderUtil.sendSysMessage(player, "whisperoff is default for builder.");
					// It isn't recommend to use the below custom L2J GMStartup functions together with retail-like GMStartupBuilderHide, so breaking the process at that stage.
					break gmStartupProcess;
				}
				if (Config.GM_STARTUP_INVULNERABLE && AdminData.getInstance().hasAccess("admin_invul", player.getAccessLevel()))
				{
					player.setInvul(true);
				}
				if (Config.GM_STARTUP_INVISIBLE && AdminData.getInstance().hasAccess("admin_invisible", player.getAccessLevel()))
				{
					player.setInvisible(true);
					player.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.STEALTH);
					player.sendPacket(new ExUserInfoAbnormalVisualEffect(player));
					// Gửi DeleteObject cho người chơi không phải GM
					final DeleteObject deletePacket = new DeleteObject(player);
					World.getInstance().forEachVisibleObject(player, Creature.class, target ->
					{
						if (target instanceof PlayerInstance && !target.getActingPlayer().isGM())
						{
							target.sendPacket(deletePacket);
						}
						if ((target != null) && (target.getTarget() == player))
						{
							target.setTarget(null);
							target.abortAttack();
							target.abortCast();
							target.getAI().setIntention(CtrlIntention.AI_INTENTION_IDLE);
						}
					});
					// Gửi CharInfo cho GM khác
					player.broadcastCharInfo();
				}
				if (Config.GM_STARTUP_SILENCE && AdminData.getInstance().hasAccess("admin_silence", player.getAccessLevel()))
				{
					player.setSilenceMode(true);
				}
				if (Config.GM_STARTUP_DIET_MODE && AdminData.getInstance().hasAccess("admin_diet", player.getAccessLevel()))
				{
					player.setDietMode(true);
					player.refreshOverloaded(true);
				}
			}
			if (Config.GM_STARTUP_AUTO_LIST && AdminData.getInstance().hasAccess("admin_gmliston", player.getAccessLevel()))
			{
				AdminData.getInstance().addGm(player, false);
			}
			else
			{
				AdminData.getInstance().addGm(player, true);
			}
			if (Config.GM_GIVE_SPECIAL_SKILLS)
			{
				SkillTreeData.getInstance().addSkills(player, false);
			}
			if (Config.GM_GIVE_SPECIAL_AURA_SKILLS)
			{
				SkillTreeData.getInstance().addSkills(player, true);
			}
		}
		PremiumManager.getInstance().onPlayerLogin(player);
		RandomCraftPremiumManager.getInstance().onPlayerLogin(player);
		// Chat banned icon.
		if (player.isChatBanned())
		{
			player.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.NO_CHAT);
		}
		// Set dead status if applies
		if (player.getCurrentHp() < 0.5)
		{
			player.setDead(true);
		}
		boolean showClanNotice = false;
		// Clan related checks are here
		final Clan clan = player.getClan();
		if (clan != null)
		{
			notifyClanMembers(player);
			notifySponsorOrApprentice(player);
			for (Siege siege : SiegeManager.getInstance().getSieges())
			{
				if (!siege.isInProgress())
				{
					continue;
				}
				if (siege.checkIsAttacker(clan))
				{
					player.setSiegeState((byte) 1);
					player.setSiegeSide(siege.getCastle().getResidenceId());
				}
				else if (siege.checkIsDefender(clan))
				{
					player.setSiegeState((byte) 2);
					player.setSiegeSide(siege.getCastle().getResidenceId());
				}
			}
			for (FortSiege siege : FortSiegeManager.getInstance().getSieges())
			{
				if (!siege.isInProgress())
				{
					continue;
				}
				if (siege.checkIsAttacker(clan))
				{
					player.setSiegeState((byte) 1);
					player.setSiegeSide(siege.getFort().getResidenceId());
				}
				else if (siege.checkIsDefender(clan))
				{
					player.setSiegeState((byte) 2);
					player.setSiegeSide(siege.getFort().getResidenceId());
				}
			}
			// Residential skills support
			if (player.getClan().getCastleId() > 0)
			{
				CastleManager.getInstance().getCastleByOwner(clan).giveResidentialSkills(player);
			}
			if (player.getClan().getFortId() > 0)
			{
				FortManager.getInstance().getFortByOwner(clan).giveResidentialSkills(player);
			}
			showClanNotice = clan.isNoticeEnabled();
		}
		// Send time.
		player.sendPacket(new ExEnterWorld());
		// Send Macro List
		player.getMacros().sendAllMacros();
		// Send Teleport Bookmark List
		client.sendPacket(new ExGetBookMarkInfoPacket(player));
		// Send Item List
		client.sendPacket(new ItemList(1, player));
		client.sendPacket(new ItemList(2, player));
		// Send Quest Item List
		client.sendPacket(new ExQuestItemList(1, player));
		client.sendPacket(new ExQuestItemList(2, player));
		player.sendAdenaInvenCount();
		// Send Shortcuts
		client.sendPacket(new ShortCutInit(player));
		// Send Action list
		player.sendPacket(ExBasicActionList.STATIC_PACKET);
		// Send blank skill list
		player.sendPacket(new SkillList());
		// Send GG check
		// player.queryGameGuard();
		// Send Dye Information
		// player.sendPacket(new HennaInfo(player));
		// Send Skill list
		player.sendSkillList();
		// Send EtcStatusUpdate
		player.sendPacket(new EtcStatusUpdate(player));
		// Clan packets
		if (clan != null)
		{
			clan.broadcastToOnlineMembers(new PledgeShowMemberListUpdate(player));
			PledgeShowMemberListAll.sendAllTo(player);
			clan.broadcastToOnlineMembers(new ExPledgeCount(clan));
			player.sendPacket(new PledgeSkillList(clan));
			final ClanHall ch = ClanHallData.getInstance().getClanHallByClan(clan);
			if ((ch != null) && (ch.getCostFailDay() > 0) && (ch.getResidenceId() < 186))
			{
				final SystemMessage sm = new SystemMessage(SystemMessageId.THE_PAYMENT_FOR_YOUR_CLAN_HALL_HAS_NOT_BEEN_MADE_PLEASE_DEPOSIT_THE_NECESSARY_AMOUNT_OF_ADENA_TO_YOUR_CLAN_WAREHOUSE_BY_S1_TOMORROW);
				sm.addInt(ch.getLease());
				player.sendPacket(sm);
			}
		}
		else
		{
			player.sendPacket(ExPledgeWaitingListAlarm.STATIC_PACKET);
		}
		// Send SubClass Info
		player.sendPacket(new ExSubjobInfo(player, SubclassInfoType.NO_CHANGES));
		// Send Inventory Info
		player.sendPacket(new ExUserInfoInvenWeight(player));
		player.sendAdenaInvenCount();
		player.sendPacket(new ExBloodyCoinCount(player));
		player.sendPacket(new ExPledgeCoinInfo(player));
		// Send Unread Mail Count
		if (MailManager.getInstance().hasUnreadPost(player))
		{
			player.sendPacket(new ExUnReadMailCount(player));
		}
		// Load Faction War Skills when the player logs in
		player.loadFactionWarSkills();
		Quest.playerEnter(player);
		// Send Quest List
		player.sendPacket(new QuestList(player));
		if (Config.PLAYER_SPAWN_PROTECTION > 0)
		{
			player.setSpawnProtection(true);
		}
		player.spawnMe(player.getX(), player.getY(), player.getZ());
		player.sendPacket(new ExRotation(player.getObjectId(), player.getHeading()));
		player.getInventory().applyItemSkills();


		if (GameEvent.isParticipant(player))
		{
			GameEvent.restorePlayerEventStatus(player);
		}
		if (player.isCursedWeaponEquipped())
		{
			CursedWeaponsManager.getInstance().getCursedWeapon(player.getCursedWeaponEquippedId()).cursedOnLogin();
		}
		if (Config.PC_CAFE_ENABLED)
		{
			if (player.getPcCafePoints() > 0)
			{
				player.sendPacket(new ExPCCafePointInfo(player.getPcCafePoints(), 0, 1));
			}
			else
			{
				player.sendPacket(new ExPCCafePointInfo());
			}
		}
		// Expand Skill
		player.sendPacket(new ExStorageMaxCount(player));
		// Send Equipped Items
		player.sendPacket(new ExUserInfoEquipSlot(player));
		// Friend list
		client.sendPacket(new L2FriendList(player));
		SystemMessage sm = new SystemMessage(SystemMessageId.YOUR_FRIEND_S1_JUST_LOGGED_IN);
		sm.addString(player.getName());
		for (int id : player.getFriendList())
		{
			final WorldObject obj = World.getInstance().findObject(id);
			if (obj != null)
			{
				obj.sendPacket(sm);
			}
		}
		// Surveillance list
		client.sendPacket(new ExUserWatcherTargetList(player));
		final ExUserWatcherTargetStatus surveillanceUpdate = new ExUserWatcherTargetStatus(player.getName(), true);
		sm = new SystemMessage(SystemMessageId.C1_FROM_YOUR_SURVEILLANCE_LIST_IS_ONLINE);
		sm.addString(player.getName());
		for (PlayerInstance p : World.getInstance().getPlayers())
		{
			if (p.getSurveillanceList().contains(player.getObjectId()))
			{
				p.sendPacket(sm);
				p.sendPacket(surveillanceUpdate);
			}
		}
		player.sendPacket(SystemMessageId.WELCOME_TO_THE_WORLD_OF_LINEAGE_II);
		AnnouncementsTable.getInstance().showAnnouncements(player);
		// ThreadPool.schedule(() ->
		// {
		// Broadcast.toAllOnlinePlayers("To create the 100M Adena Pack, press CTRL + left mouse button !!!", true);
		// }, 10_000);
		if ((Config.SERVER_RESTART_SCHEDULE_ENABLED) && (Config.SERVER_RESTART_SCHEDULE_MESSAGE))
		{
			player.sendPacket(new CreatureSay(null, ChatType.BATTLEFIELD, "[SERVER]", "Next restart is scheduled at " + ServerRestartManager.getInstance().getNextRestartTime() + ".", 0));
		}
		if (showClanNotice)
		{
			final NpcHtmlMessage notice = new NpcHtmlMessage();
			notice.setFile(player, "data/html/clanNotice.htm");
			notice.replace("%clan_name%", player.getClan().getName());
			notice.replace("%notice_text%", player.getClan().getNotice().replaceAll("\r\n", "<br>"));
			notice.disableValidation();
			client.sendPacket(notice);
		}
		else if (Config.SERVER_NEWS)
		{
			final String serverNews = HtmCache.getInstance().getHtm(player, "data/html/servnews.htm");
			if (serverNews != null)
			{
				client.sendPacket(new NpcHtmlMessage(serverNews));
			}
		}
		if (Config.PETITIONING_ALLOWED)
		{
			PetitionManager.getInstance().checkPetitionMessages(player);
		}
		if (player.isAlikeDead()) // dead or fake dead
		{
			// no broadcast needed since the player will already spawn dead to others
			client.sendPacket(new Die(player));
		}
		player.onPlayerEnter();
		client.sendPacket(new SkillCoolTime(player));
		client.sendPacket(new ExVoteSystemInfo(player));
		for (ItemInstance item : player.getInventory().getItems())
		{
			if (item.isTimeLimitedItem())
			{
				item.scheduleLifeTimeTask();
			}
			if (item.isShadowItem() && item.isEquipped())
			{
				item.decreaseMana(false);
			}
		}
		for (ItemInstance whItem : player.getWarehouse().getItems())
		{
			if (whItem.isTimeLimitedItem())
			{
				whItem.scheduleLifeTimeTask();
			}
		}
		if (player.getClanJoinExpiryTime() > System.currentTimeMillis())
		{
			player.sendPacket(SystemMessageId.YOU_ARE_DISMISSED_FROM_A_CLAN_YOU_CANNOT_JOIN_ANOTHER_FOR_24_H);
		}
		// remove combat flag before teleporting
		if (player.getInventory().getItemByItemId(FortManager.COMBAT_FLAG_ID) != null)
		{
			FortSiegeManager.getInstance().dropCombatFlag(player, FortManager.ORC_FORTRESS_ID);
		}
		// Attacker or spectator logging in to a siege zone.
		// Actually should be checked for inside castle only?
		if (!player.canOverrideCond(PlayerCondOverride.ZONE_CONDITIONS) && player.isInsideZone(ZoneId.SIEGE) && (!player.isInSiege() || (player.getSiegeState() < 2)))
		{
			player.teleToLocation(TeleportWhereType.TOWN);
		}
		// Remove demonic weapon if character is not cursed weapon equipped.
		if ((player.getInventory().getItemByItemId(8190) != null) && !player.isCursedWeaponEquipped())
		{
			player.destroyItem("Zariche", player.getInventory().getItemByItemId(8190), null, true);
		}
		if ((player.getInventory().getItemByItemId(8689) != null) && !player.isCursedWeaponEquipped())
		{
			player.destroyItem("Akamanah", player.getInventory().getItemByItemId(8689), null, true);
		}
		if (Config.ALLOW_MAIL)
		{
			if (MailManager.getInstance().hasUnreadPost(player))
			{
				client.sendPacket(ExNoticePostArrived.valueOf(false));
			}
		}
		// Secret Code Safety Lock on Login
		String filename = null;
		if ((player.getSecretCode() == null) || player.getSecretCode().equalsIgnoreCase("")) // doesn't have a secret code set
		{
			filename = "data/html/mods/password/setsecretcode.htm";
			NpcHtmlMessage itemReply = new NpcHtmlMessage(1);
			itemReply.setFile(player, filename);
			itemReply.replace("%dtn%", "You need to create a secret code or your character will have limited functions!");
			player.sendPacket(itemReply);
			player.doLockdown(504); // Lock for 21 days until secret code is set
			player.sendMessage("Your account is locked down and has limited functions. Please create a secret code.");
		}
		else // has a secret code but hasn't verified it yet
		{
			filename = "data/html/mods/password/secretcodeconfirmation.htm";
			NpcHtmlMessage itemReply = new NpcHtmlMessage(1);
			itemReply.setFile(player, filename);
			itemReply.replace("%dtn%", "Please enter your secret code to unlock your account.");
			player.sendPacket(itemReply);
			player.doLockdown(504);
			player.sendMessage("Your account is locked down and has limited functions. Please verify your secret code.");
		}
		if (Config.WELCOME_MESSAGE_ENABLED)
		{
			player.sendPacket(new ExShowScreenMessage(Config.WELCOME_MESSAGE_TEXT, Config.WELCOME_MESSAGE_TIME));
		}
		if (!player.getPremiumItemList().isEmpty())
		{
			player.sendPacket(ExNotifyPremiumItem.STATIC_PACKET);
		}
		if ((Config.OFFLINE_TRADE_ENABLE || Config.OFFLINE_CRAFT_ENABLE) && Config.STORE_OFFLINE_TRADE_IN_REALTIME)
		{
			OfflineTraderTable.onTransaction(player, true, false);
		}
		// Check if expoff is enabled.
		if (player.getVariables().getBoolean("EXPOFF", false))
		{
			player.disableExpGain();
			player.sendMessage("Experience gain is disabled.");
		}
		player.sendPacket(new UserInfo(player));
		if (BeautyShopData.getInstance().hasBeautyData(player.getRace(), player.getAppearance().getSexType()))
		{
			player.sendPacket(new ExBeautyItemList(player));
		}
		if (Config.ENABLE_WORLD_CHAT)
		{
			player.sendPacket(new ExWorldChatCnt(player));
		}
		player.sendPacket(new ExItemAnnounceSetting(player.getVariables().getBoolean(PlayerVariables.ITEM_ANNOUNCE_HIDE_NAME, false)));
		player.sendPacket(new ExConnectedTimeAndGettableReward(player));
		player.sendPacket(new ExOneDayReceiveRewardList(player, true));
		// Handle soulshots, disable all on EnterWorld
		player.sendPacket(new ExAutoSoulShot(0, true, 0));
		player.sendPacket(new ExAutoSoulShot(0, true, 1));
		player.sendPacket(new ExAutoSoulShot(0, true, 2));
		player.sendPacket(new ExAutoSoulShot(0, true, 3));
		// Event Engine
		GabrielEvents.onLogin(player);
		// Fix for equipped item skills
		if (!player.getEffectList().getAbnormalVisualEffectsFiltered().isEmpty())
		{
			player.updateAbnormalVisualEffects();
		}
		// Check if in time limited hunting zone.
		if (player.isInsideZone(ZoneId.SPECIAL_HUNTING))
		{
			player.teleToLocation(MapRegionManager.getInstance().getTeleToLocation(player, TeleportWhereType.TOWN));
		}
		// World Trade.
		WorldExchangeManager.getInstance().checkPlayerSellAlarm(player);
		if (Config.ENABLE_ATTENDANCE_REWARDS)
		{
			ThreadPool.schedule(() ->
			{
				// Check if player can receive reward today.
				final AttendanceInfoHolder attendanceInfo = player.getAttendanceInfo();
				if (attendanceInfo.isRewardAvailable())
				{
					final int lastRewardIndex = attendanceInfo.getRewardIndex() + 1;
					player.sendPacket(new ExShowScreenMessage("Your attendance day " + lastRewardIndex + " reward is ready.", ExShowScreenMessage.TOP_CENTER, 7000, 0, true, true));
					player.sendMessage("Your attendance day " + lastRewardIndex + " reward is ready.");
					player.sendMessage("Click on General Menu -> Attendance Check.");
					if (Config.ATTENDANCE_POPUP_WINDOW)
					{
						player.sendPacket(new ExVipAttendanceItemList(player));
					}
				}
			}, Config.ATTENDANCE_REWARD_DELAY * 60 * 1000);
			if (Config.ATTENDANCE_POPUP_START)
			{
				player.sendPacket(new ExVipAttendanceItemList(player));
			}
		}
		if (Config.HARDWARE_INFO_ENABLED)
		{
			ThreadPool.schedule(() ->
			{
				// Generate trace string.
				final StringBuilder sb = new StringBuilder();
				for (int[] i : _tracert)
				{
					for (int j : i)
					{
						sb.append(j);
						sb.append(".");
					}
				}
				final String trace = sb.toString();
				// Get hardware info from client.
				ClientHardwareInfoHolder hwInfo = client.getHardwareInfo();
				if (hwInfo != null)
				{
					hwInfo.store(player);
					TRACE_HWINFO.put(trace, hwInfo);
				}
				else
				{
					// Get hardware info from stored tracert map.
					hwInfo = TRACE_HWINFO.get(trace);
					if (hwInfo != null)
					{
						hwInfo.store(player);
						client.setHardwareInfo(hwInfo);
					}
					// Get hardware info from account variables.
					else
					{
						final String storedInfo = player.getAccountVariables().getString(AccountVariables.HWID, "");
						if (!storedInfo.isEmpty())
						{
							hwInfo = new ClientHardwareInfoHolder(storedInfo);
							TRACE_HWINFO.put(trace, hwInfo);
							client.setHardwareInfo(hwInfo);
						}
					}
				}
				// Check max players.
				if (Config.KICK_MISSING_HWID && (hwInfo == null))
				{
					Disconnection.of(client).logout(false, false);
				}
				else if (Config.MAX_PLAYERS_PER_HWID > 0)
				{
					// Check if HWID is in NoBoxLimitHwids whitelist
					String playerHwid = (hwInfo != null) ? hwInfo.getMacAddress() : null;
					if (playerHwid != null && Config.NO_BOX_LIMIT_HWIDS.contains(playerHwid))
					{
						// HWID is whitelisted, skip limit check
					}
					else
					{
						int count = 0;
						int maxAllowedForHwid = Config.MAX_PLAYERS_PER_HWID; // Default limit

						// Check all players with same HWID to find the highest premium status
						for (PlayerInstance plr : World.getInstance().getPlayers())
						{
							if (plr == null || plr.getClient() == null || plr.getClient().getHardwareInfo() == null)
							{
								continue;
							}
							if (plr == player) // Skip current player
							{
								continue;
							}
							String plrHwid = plr.getClient().getHardwareInfo().getMacAddress();
							String currentHwid = hwInfo.getMacAddress();
							if ((plr.isOnlineInt() == 1) && currentHwid != null && currentHwid.equals(plrHwid))
							{
								count++;
								// Check if any existing player has premium - if so, use premium limit for all
								int plrMaxAllowed = PremiumManager.getInstance().getMaxPlayersPerHwid(plr);
								if (plrMaxAllowed > maxAllowedForHwid)
								{
									maxAllowedForHwid = plrMaxAllowed;
								}
							}
						}

						// Also check current player's premium status
						int currentPlayerMaxAllowed = PremiumManager.getInstance().getMaxPlayersPerHwid(player);
						if (currentPlayerMaxAllowed > maxAllowedForHwid)
						{
							maxAllowedForHwid = currentPlayerMaxAllowed;
						}

						if (count >= maxAllowedForHwid)
						{
							Disconnection.of(client).logout(false, false);
						}
					}
				}

				// Add HWID to UniqueOnlineManager after all checks are done
				String clientHwid = (hwInfo != null) ? hwInfo.getMacAddress() : null;
				if (clientHwid != null)
				{
					UniqueOnlineManager.getInstance().addNewHwid(clientHwid);
				}
			}, 5000);
		}
		// if (multiBoxCheck(player, adress))
		// {
		// ThreadPool.get().schedule(() ->
		// {
		// if (client != null)
		// {
		// Disconnection.of(client).defaultSequence(false);
		// }
		// }, 1_000);
		// return;
		// }
		if (multiBoxCheckStrix(player))
		{
			return;
		}
		// Disabled AAC multibox check since AAC is not used
		// if (multiBoxCheckAAC(player))
		// {
		// 	return;
		// }
		// if (multiBoxCheckStrix(player))
		// {
		// if (StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// String hwid = player.getClient().getStrixClientData().getClientHWID();
		// List<String> players = new ArrayList<>();
		// for (PlayerInstance temp : World.getInstance().getPlayers())
		// {
		// if (temp.getClient().getStrixClientData().getClientHWID().equals(hwid))
		// {
		// players.add(temp.getName());
		// }
		// }
		// LOGGER.warning("Player " + player.getName() + " tried to log too many windows. [HWID: " + hwid + "]" + "[Windows: " + player.getClient().getStrixClientData().getActiveWindowCount() + "] [Chars: " + players + "]");
		// }
		//
		// ThreadPool.get().schedule(() -> player.sendPacket(new ExShowScreenMessage("Too many Lineage II windows running.", 2, 10000, 0, true, true)), 5000);
		// ThreadPool.get().schedule(() ->
		// {
		// if (client != null)
		// {
		// Disconnection.of(client).defaultSequence(false);
		// }
		// }, 10_000);
		// return;
		// }
		// HWID will be added to UniqueOnlineManager inside ThreadPool.schedule after hwInfo is set
		// TODO[K] - Strix section start
		// if (StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// UniqueOnlineManager.getInstance().addNewHwid(player.getClient().getStrixClientData().getClientHWID());
		// }
		// TODO[K] - Strix section end
		player.applyKarmaPenalty();
		player.sendPacket(new ExVitalExInfo(player));
		player.sendPacket(new ElementalSpiritInfo(player, player.getActiveElementalSpiritType(), (byte) 0));
		player.sendPacket(new ExMagicLampExpInfo(player));
		player.sendPacket(new ExInitGlobalEventUI());
		player.sendPacket(new ExL2PassSimpleInfo(L2PassData.isEnabled()));
		player.sendPacket(new ExCraftInfo(player));
		player.sendPacket(new ExSteadyBoxUiInit(player));
		player.sendPacket(new ExItemDeletionInfo(ItemDeletionData.getInstance().getItems(), ItemDeletionData.getInstance().getSkills()));
		SiegeManager.getInstance().sendSiegeHUDInfo(player);
		if (player.isDeathKnight())
		{
			StatusUpdate su = new StatusUpdate(player);
			su.addUpdate(StatusUpdateType.MAX_DP, player.getMaxDp());
			su.addUpdate(StatusUpdateType.CUR_DP, player.getCurrentDp());
			// Required x2, first only enables showing DP bar
			player.sendPacket(su);
			player.sendPacket(su);
		}
		else if (player.isVanguard())
		{
			StatusUpdate su = new StatusUpdate(player);
			su.addUpdate(StatusUpdateType.CUR_BP, player.getCurrentBp());
			su.addUpdate(StatusUpdateType.MAX_BP, player.getMaxBp());
			// Required x2
			player.sendPacket(su);
			player.sendPacket(su);
		}
		player.sendToggledShortcuts();
		if (player.isDeathKnight())
		{
			player.startDevastatingMindTask();
		}
		else if (player.isVanguard())
		{
			player.startBlazingBeastTask();
		}
		player.restoreServitorsAndPets();
		// Events
		if (LetterCollectorData.getInstance().isEnabled())
		{
			player.sendPacket(new ExLetterCollectorUILauncher());
		}
		if (MableGameEventData.getInstance().isEnabled())
		{
			player.sendPacket(ExMableGameUILauncher.STATIC_PACKET);
		}
		if (BalthusEventData.getInstance().isEnabled())
		{
			BalthusEventData.getInstance().sendEventInfo(player);
		}
		GoldFestivalEvent.getInstance().onPlayerLogin(player);
		// Custom Login Pre-Order
		// if (!player.isGM())
		// {
		// player.setBlockActions(true);
		// player.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.FLESH_STONE);
		// }
		// temp fix for items not working?
		player.statsTempFix();
		// fix abnormal visual effects from others pov
		World.getInstance().forEachVisibleObject(player, PlayerInstance.class, wo ->
		{
			if (wo.isPlayer() && player.isVisibleFor(wo))
			{
				player.sendInfo(wo);
			}
		});
		if (Config.COLLECTIONS_ENABLED)
		{
			for (int category = 1; category <= 7; category++)
			{
				player.sendPacket(new ExCollectionInfo(player, category));
			}
		}
		if (Config.PURGE_ENABLE)
		{
			PlayerVariables vars = player.getVariables();
			player.sendPacket(new ExSubjugationSidebar(1, vars.getInt("PURGE_DATA_POINTS_1", 0), vars.getInt("PURGE_DATA_CURRENT_KEYS_1", 0)));
		}
		player.sendPacket(new ExPledgeContributionList(player));
		player.sendPacket(new ExUserRestartLockerList(player));
		// Sylph Blessing Event
		// if (Config.ENABLE_EVENT_SYLPH_BLESSING)
		// {
		// if (player.hasPremiumStatus())
		// {
		// SYLPH_BLESSING.applyEffects(player, player);
		// }
		// }
		// Start Bonus
		if (player.getLevel() == 80)
		{
			if (player.getCreateDate().getTimeInMillis() >= 1717545600000L) // 2025.06.05
			{
				if (PremiumManager.getInstance().getPremiumExpiration(player.getObjectId()) <= 0)
				{
					if (player.getAccountVariables().getString(AccountVariables.START_BONUS_PREMIUM, "N").equals("N"))
					{
						PremiumManager.getInstance().addPremiumTime(player.getObjectId(), 7, TimeUnit.DAYS, 1);
						player.getAccountVariables().set(AccountVariables.START_BONUS_PREMIUM, "Y");
					}
				}
				if (player.getAccountVariables().getString(AccountVariables.START_BONUS_START, "N").equals("N"))
				{
					final int devotedJewelsIds[] =
					{
						72090, 72092, 72093, 72094, 72095,
					};
					for (int itemId : devotedJewelsIds)
					{
						final ItemInstance item = player.addItem("Start Bonus", itemId, 1, player, true);
						item.setTime(1_209_600_000L);
					}
					player.getAccountVariables().set(AccountVariables.START_BONUS_START, "Y");
				}
			}
		}
		// Faction System
		if (Config.FACTION_SYSTEM_ENABLED)
		{
			// Check if faction selection is required
			if (player.getFaction() == null || player.getFaction() == Faction.NONE)
			{
				if (Config.MANUAL_FACTION_SELECTION)
				{
					// Lock player actions until faction is selected
					player.setBlockActions(true);
					player.getEffectList().startAbnormalVisualEffect(AbnormalVisualEffect.FLESH_STONE); // Visual lock effect
					player.sendMessage("You must select a faction to continue. Please choose from the interface.");
					// Show faction selection UI
					String htmlContent = HtmCache.getInstance().getHtm(player, "data/html/gve/faction_selection.htm");
					if (htmlContent == null)
					{
						LOGGER.warning("Could not load HTML file: data/html/gve/faction_selection.htm");
						player.sendMessage("Error: Could not load faction selection interface.");
						Disconnection.of(client).logout(false, false);
						return;
					}
					// Get faction percentages
					double firePercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.FIRE);
					double waterPercent = FactionBalanceService.getInstance().getFactionPlayerPercentage(Faction.WATER);
					// Get faction counts for display
					int fireCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.FIRE);
					int waterCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.WATER);
					// Check if factions are balanced
					boolean fireDisabled = (firePercent >= Config.FACTION_BALANCE_MAX_PERCENT);
					boolean waterDisabled = (waterPercent >= Config.FACTION_BALANCE_MAX_PERCENT);
					// Replace placeholders in HTML
					htmlContent = htmlContent.replace("%fire_count%", String.valueOf(fireCount));
					htmlContent = htmlContent.replace("%fire_percent%", String.format("%.1f", firePercent));
					htmlContent = htmlContent.replace("%water_count%", String.valueOf(waterCount));
					htmlContent = htmlContent.replace("%water_percent%", String.format("%.1f", waterPercent));
					htmlContent = htmlContent.replace("%fire_disabled%", fireDisabled ? "disabled" : "");
					htmlContent = htmlContent.replace("%water_disabled%", waterDisabled ? "disabled" : "");
					// Send HTML to player
					NpcHtmlMessage html = new NpcHtmlMessage(0);
					html.setHtml(htmlContent);
					player.sendPacket(html);
					return;
				}
				else if (Config.AUTO_FACTION_SELECTION)
				{
					// Auto assign faction
					Faction newFaction = FactionBalanceService.getInstance().assignFaction(player, null);
					if (newFaction == null || newFaction == Faction.NONE)
					{
						LOGGER.severe("FactionBalanceService failed to assign a valid faction for player " + player.getName() + ". Disconnecting...");
						Disconnection.of(client).logout(false, false);
						return;
					}
					player.setFaction(newFaction);
					// Teleport to faction base if configured
					if (Config.FACTION_RESPAWN_AT_BASE)
					{
						player.teleToLocation(newFaction == Faction.FIRE ? Config.FACTION_FIRE_BASE_LOCATION : Config.FACTION_WATER_BASE_LOCATION);
					}
					// Update faction counts from online players
					FactionBalanceService.getInstance().updateFactionCounts();
					// Save to database
					FactionBalanceService.getInstance().saveToDatabase();
				}
				else
				{
					LOGGER.warning("No faction selection mode enabled! Check FactionSystem.ini for ManualFactionSelection or AutoFactionSelection settings.");
					player.sendMessage("Faction system is not configured. Contact an administrator.");
					Disconnection.of(client).logout(false, false); // Disconnect if no mode is enabled
					return;
				}
			}
			else
			{
				// Player already has a faction, notify and update UI
				if (player.getFaction() == Faction.FIRE)
				{
					// player.applyFactionColors();
					// int fireCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.FIRE);
					// int waterCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.WATER);
					// player.sendMessage("Welcome " + player.getName() + ", you are fighting for the " + Config.FACTION_FIRE_TEAM_NAME + " faction.");
					// player.sendMessage("Current faction balance - FIRE: " + fireCount + " players, WATER: " + waterCount + " players.");
					player.sendPacket(new ExShowScreenMessage("Welcome " + player.getName() + ", you are fighting for the " + Config.FACTION_FIRE_TEAM_NAME + " faction.", 10000));
				}
				else if (player.getFaction() == Faction.WATER)
				{
					// player.applyFactionColors();
					// int fireCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.FIRE);
					// int waterCount = FactionBalanceService.getInstance().getOnlineFactionCount(Faction.WATER);
					// player.sendMessage("Welcome " + player.getName() + ", you are fighting for the " + Config.FACTION_WATER_TEAM_NAME + " faction.");
					// player.sendMessage("Current faction balance - FIRE: " + fireCount + " players, WATER: " + waterCount + " players.");
					player.sendPacket(new ExShowScreenMessage("Welcome " + player.getName() + ", you are fighting for the " + Config.FACTION_WATER_TEAM_NAME + " faction.", 10000));
				}
			}
			// Apply faction colors at the end
			player.applyFactionColors();
		}
		// Remove event items once at the end
		removeEventItems(player);
		MuseumManager.getInstance().giveReward(player);
	}
	
	public void removeEventItems(PlayerInstance player)
	{
		if (player == null)
		{
			return;
		}
		InventoryUpdate iu = new InventoryUpdate();
		List<ItemInstance> currentItems = new ArrayList<>(player.getInventory().getItems());
		for (ItemInstance item : currentItems)
		{
			if (item.getCustomType1() == 9999)
			{
				if (item.isEquipped())
				{
					player.getInventory().unEquipItemInSlot(item.getLocationSlot());
					iu.addModifiedItem(item);
				}
				player.getInventory().destroyItem("EventItemRemoval", item, player, null);
				iu.addRemovedItem(item);
			}
		}
		if (player.isOnline())
		{
			player.sendInventoryUpdate(iu);
			player.sendItemList();
			player.broadcastUserInfo();
		}
	}
	
	@SuppressWarnings("unused")
	private boolean multiBoxCheck(PlayerInstance player, String[] adress)
	{
		final String trace = Arrays.toString(player.getClient().getTrace()[0]);
		final String addr = player.getClient().getHostAddress();
		int i = 0;
		for (PlayerInstance temp : World.getInstance().getPlayers())
		{
			if (temp == null)
			{
				continue;
			}
			if (player == temp)
			{
				continue;
			}
			if (temp.getClient() == null)
			{
				continue;
			}
			if (temp.getClient().isDetached())
			{
				continue;
			}
			if (temp.getClient().getHostAddress() == null)
			{
				continue;
			}
			if (temp.getClient().getHostAddress() == null)
			{
				continue;
			}
			if (addr.equals(temp.getClient().getHostAddress()))
			{
				if (trace.equals(Arrays.toString(temp.getClient().getTrace()[0])))
				{
					i++;
					if (i >= 1)
					{
						return true;
					}
				}
			}
		}
		return false;
	}
	
	private boolean multiBoxCheckStrix(PlayerInstance player)
	{
		// TODO[K] - Strix section start
		// if (!StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// return false;
		// }
		// if (Config.NO_BOX_LIMIT_HWIDS.contains(player.getClient().getStrixClientData().getClientHWID()))
		// {
		// return false;
		// }
		//
		// final GameClient client = player.getClient();
		// String hwid = client.getStrixClientData().getClientHWID();
		// for (PlayerInstance temp : World.getInstance().getPlayers())
		// {
		// if (temp == null)
		// {
		// continue;
		// }
		// if (temp == player)
		// {
		// continue;
		// }
		// if (temp.getClient() == null)
		// {
		// continue;
		// }
		// if (temp.getClient().isDetached())
		// {
		// continue;
		// }
		// if (temp.getClient().getStrixClientData() == null)
		// {
		// continue;
		// }
		// if (temp.getClient().getStrixClientData().getClientHWID() == null)
		// {
		// continue;
		// }
		// if (hwid.equals(temp.getClient().getStrixClientData().getClientHWID()))
		// {
		// LOGGER.warning("Player " + player.getName() + " tried to log too many windows. [HWID: " + hwid + "]" + "[Strix count: " + player.getClient().getStrixClientData().getActiveWindowCount() + "] [Other char: " + temp.getName() + "]");
		//
		// ThreadPool.get().schedule(() -> player.sendPacket(new ExShowScreenMessage("Too many Lineage II windows running.", 2, 10000, 0, true, true)), 5000);
		// ThreadPool.get().schedule(() ->
		// {
		// Disconnection.of(client).logout(false, false);
		// }, 10_000);
		// return true;
		// }
		// }
		// TODO[K] - Strix section end
		return false;
	}
	
	/**
	 * @param player
	 */
	private void notifyClanMembers(PlayerInstance player)
	{
		final Clan clan = player.getClan();
		if (clan != null)
		{
			clan.getClanMember(player.getObjectId()).setPlayerInstance(player);
			final SystemMessage msg = new SystemMessage(SystemMessageId.CLAN_MEMBER_S1_HAS_LOGGED_INTO_GAME);
			msg.addString(player.getName());
			clan.broadcastToOtherOnlineMembers(msg, player);
			clan.broadcastToOtherOnlineMembers(new PledgeShowMemberListUpdate(player), player);
		}
	}
	
	/**
	 * @param player
	 */
	private void notifySponsorOrApprentice(PlayerInstance player)
	{
		if (player.getSponsor() != 0)
		{
			final PlayerInstance sponsor = World.getInstance().getPlayer(player.getSponsor());
			if (sponsor != null)
			{
				final SystemMessage msg = new SystemMessage(SystemMessageId.YOUR_APPRENTICE_C1_HAS_LOGGED_IN);
				msg.addString(player.getName());
				sponsor.sendPacket(msg);
			}
		}
		else if (player.getApprentice() != 0)
		{
			final PlayerInstance apprentice = World.getInstance().getPlayer(player.getApprentice());
			if (apprentice != null)
			{
				final SystemMessage msg = new SystemMessage(SystemMessageId.YOUR_SPONSOR_C1_HAS_LOGGED_IN);
				msg.addString(player.getName());
				apprentice.sendPacket(msg);
			}
		}
	}
	
	private boolean multiBoxCheckAAC(PlayerInstance player)
	{
		final GameClient client = player.getClient();
		String hwid = (client.getHardwareInfo() != null) ? client.getHardwareInfo().getMacAddress() : null;

		if (hwid != null && Config.IGNORED_HWIDS.contains(hwid))
		{
			return false;
		}
		for (PlayerInstance temp : World.getInstance().getPlayers())
		{
			if (temp == null)
			{
				continue;
			}
			if (temp == player)
			{
				continue;
			}
			if (temp.getClient() == null)
			{
				continue;
			}
			if (temp.getClient().isDetached())
			{
				continue;
			}
			if (temp.getClient().getHardwareInfo() == null || temp.getClient().getHardwareInfo().getMacAddress() == null)
			{
				continue;
			}
			if (hwid != null && hwid.equals(temp.getClient().getHardwareInfo().getMacAddress()))
			{
				ThreadPool.schedule(() -> player.sendPacket(new ExShowScreenMessage("Too many Lineage II windows running.", 2, 10000, 0, true, true)), 5000);
				ThreadPool.schedule(() ->
				{
					Disconnection.of(client).logout(false, false);
				}, 10_000);
				return true;
			}
		}
		return false;
	}
}
