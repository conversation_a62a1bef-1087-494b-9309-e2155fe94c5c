/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.bypasshandlers;

import club.projectessence.Config;
import club.projectessence.gameserver.handler.IBypassHandler;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.WareHouseDepositList;
import club.projectessence.gameserver.network.serverpackets.WareHouseWithdrawalList;

import java.util.logging.Level;

public class PrivateWarehouse implements IBypassHandler {
	private static final String[] COMMANDS =
			{
					"withdrawp",
					"depositp"
			};

	@Override
	public boolean useBypass(String command, PlayerInstance player, Creature target) {
		if (!Config.ALLOW_WAREHOUSE) {
			return false;
		}

		if (!target.isNpc()) {
			return false;
		}

		if (player.hasItemRequest()) {
			return false;
		}

		try {
			if (command.toLowerCase().startsWith(COMMANDS[0])) // WithdrawP
			{
				showWithdrawWindow(player);
				return true;
			} else if (command.toLowerCase().startsWith(COMMANDS[1])) // DepositP
			{
				player.sendPacket(ActionFailed.STATIC_PACKET);
				player.setActiveWarehouse(player.getWarehouse());
				player.setInventoryBlockingStatus(true);
				player.sendPacket(new WareHouseDepositList(1, player, WareHouseDepositList.PRIVATE));
				player.sendPacket(new WareHouseDepositList(2, player, WareHouseDepositList.PRIVATE));
				return true;
			}

			return false;
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, "Exception in " + getClass().getSimpleName(), e);
		}
		return false;
	}

	private void showWithdrawWindow(PlayerInstance player) {
		player.sendPacket(ActionFailed.STATIC_PACKET);
		player.setActiveWarehouse(player.getWarehouse());

		if (player.getActiveWarehouse().getSize() == 0) {
			player.sendPacket(SystemMessageId.YOU_HAVE_NOT_DEPOSITED_ANY_ITEMS_IN_YOUR_WAREHOUSE);
			return;
		}

		player.sendPacket(new WareHouseWithdrawalList(1, player, WareHouseWithdrawalList.PRIVATE));
		player.sendPacket(new WareHouseWithdrawalList(2, player, WareHouseWithdrawalList.PRIVATE));
	}

	@Override
	public String[] getBypassList() {
		return COMMANDS;
	}
}
