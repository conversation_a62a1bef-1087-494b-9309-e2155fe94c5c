package club.projectessence.gameserver.network.clientpackets.privatestore;

import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.privatestore.RequestPrivateStoreSearchStatistics;

public class ExRequestPrivateStoreSearchStatistics extends ClientPacket {
	@Override
	public void readImpl() {
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		player.sendPacket(new RequestPrivateStoreSearchStatistics());
	}
}
