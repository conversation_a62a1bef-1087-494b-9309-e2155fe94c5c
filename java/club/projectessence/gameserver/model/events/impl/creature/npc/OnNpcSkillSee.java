/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.events.impl.creature.npc;

import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventType;
import club.projectessence.gameserver.model.events.impl.IBaseEvent;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * <AUTHOR>
 */
public class OnNpcSkillSee implements IBaseEvent {
	private final Npc _npc;
	private final PlayerInstance _caster;
	private final Skill _skill;
	private final WorldObject[] _targets;
	private final boolean _isSummon;

	public OnNpcSkillSee(Npc npc, PlayerInstance caster, Skill skill, boolean isSummon, WorldObject... targets) {
		_npc = npc;
		_caster = caster;
		_skill = skill;
		_isSummon = isSummon;
		_targets = targets;
	}

	public Npc getTarget() {
		return _npc;
	}

	public PlayerInstance getCaster() {
		return _caster;
	}

	public Skill getSkill() {
		return _skill;
	}

	public WorldObject[] getTargets() {
		return _targets;
	}

	public boolean isSummon() {
		return _isSummon;
	}

	@Override
	public EventType getType() {
		return EventType.ON_NPC_SKILL_SEE;
	}
}
