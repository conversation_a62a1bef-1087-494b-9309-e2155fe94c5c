/*
 * ; * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.data.xml;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Logger;

import org.w3c.dom.Document;
import org.w3c.dom.NamedNodeMap;
import org.w3c.dom.Node;

import club.projectessence.Config;
import club.projectessence.commons.concurrent.ThreadPool;
import club.projectessence.commons.util.IXmlReader;
import club.projectessence.gameserver.model.Purge;
import club.projectessence.gameserver.model.holders.ItemChanceHolder;
import club.projectessence.gameserver.model.holders.ItemHolder;

/**
 * <AUTHOR>
 */
public class PurgeData implements IXmlReader
{
	private static final Logger			LOGGER	= Logger.getLogger(PurgeData.class.getName());
	private final Map<Integer, Purge>	_purges	= new ConcurrentHashMap<>();
	
	protected PurgeData()
	{
		load();
	}
	
	public static PurgeData getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	@Override
	public void load()
	{
		if (!Config.PURGE_ENABLE)
		{
			LOGGER.info(getClass().getSimpleName() + ": Purge disabled.");
			return;
		}
		_purges.clear();
		parseDatapackFile("data/PurgeData.xml");
		if (!_purges.isEmpty())
		{
			LOGGER.info(getClass().getSimpleName() + ": Loaded " + _purges.size() + " purges.");
		}
		else
		{
			LOGGER.info(getClass().getSimpleName() + ": System is disabled.");
		}
		ThreadPool.scheduleAtFixedRate(() ->
		{
			for (Purge purge : _purges.values())
			{
				purge.updateRanking();
			}
		}, 1000, 5 * 60 * 1000);
		scheduleMondayReset();
	}
	
	@Override
	public void parseDocument(Document doc, File f)
	{
		for (Node n = doc.getFirstChild(); n != null; n = n.getNextSibling())
		{
			if ("list".equalsIgnoreCase(n.getNodeName()))
			{
				for (Node d = n.getFirstChild(); d != null; d = d.getNextSibling())
				{
					if ("purge".equalsIgnoreCase(d.getNodeName()))
					{
						NamedNodeMap attrs = d.getAttributes();
						final int id = parseInteger(attrs, "id");
						int minLevel = 0;
						int maxLevel = ExperienceData.getInstance().getMaxLevel();
						int maxSubjugationPoint = 0;
						int maxGachaPoint = 0;
						int maxPeriodicGachaPoint = 0;
						ItemHolder gachaCost = null;
						int maxUsePoint = 0;
						Map<Integer, ItemHolder> rankingRewards = new HashMap<>();
						List<ItemChanceHolder> rewards = new ArrayList<>();
						String monsterIds = null;
						for (Node b = d.getFirstChild(); b != null; b = b.getNextSibling())
						{
							attrs = b.getAttributes();
							if ("level".equalsIgnoreCase(b.getNodeName()))
							{
								minLevel = parseInteger(attrs, "min");
								maxLevel = parseInteger(attrs, "max");
							}
							else if ("maxSubjugationPoint".equalsIgnoreCase(b.getNodeName()))
							{
								maxSubjugationPoint = Integer.parseInt(b.getTextContent());
							}
							else if ("maxGachaPoint".equalsIgnoreCase(b.getNodeName()))
							{
								maxGachaPoint = Integer.parseInt(b.getTextContent());
							}
							else if ("maxPeriodicGachaPoint".equalsIgnoreCase(b.getNodeName()))
							{
								maxPeriodicGachaPoint = Integer.parseInt(b.getTextContent());
							}
							else if ("gachaCost".equalsIgnoreCase(b.getNodeName()))
							{
								int costId = parseInteger(attrs, "id");
								long costCount = parseLong(attrs, "count");
								gachaCost = new ItemHolder(costId, costCount);
							}
							else if ("maxUsePoint".equalsIgnoreCase(b.getNodeName()))
							{
								maxUsePoint = Integer.parseInt(b.getTextContent());
							}
							else if ("rankingRewards".equalsIgnoreCase(b.getNodeName()))
							{
								for (Node t = b.getFirstChild(); t != null; t = t.getNextSibling())
								{
									if ("reward".equals(t.getNodeName()))
									{
										final int rewardRank = parseInteger(t.getAttributes(), "rank");
										final int rewardId = parseInteger(t.getAttributes(), "id");
										final long rewardCount = parseInteger(t.getAttributes(), "count");
										rankingRewards.put(rewardRank, new ItemHolder(rewardId, rewardCount));
									}
								}
							}
							else if ("rewards".equalsIgnoreCase(b.getNodeName()))
							{
								for (Node t = b.getFirstChild(); t != null; t = t.getNextSibling())
								{
									if ("reward".equals(t.getNodeName()))
									{
										final int rewardId = parseInteger(t.getAttributes(), "id");
										final long rewardCount = parseInteger(t.getAttributes(), "count");
										final double rewardChance = parseDouble(t.getAttributes(), "chance");
										rewards.add(new ItemChanceHolder(rewardId, rewardChance, rewardCount));
									}
								}
							}
							else if ("monsterIds".equalsIgnoreCase(b.getNodeName()))
							{
								monsterIds = b.getTextContent();
							}
						}
						_purges.put(id, new Purge(id, minLevel, maxLevel, maxSubjugationPoint, maxGachaPoint, maxPeriodicGachaPoint, gachaCost, maxUsePoint, rankingRewards, rewards, monsterIds));
					}
				}
			}
		}
	}
	
	public boolean isHotTime()
	{
		int from = (12 * 100) + 0;
		int to = (14 * 100) + 0;
		Calendar c = Calendar.getInstance();
		c.setTime(new Date());
		int t = (c.get(Calendar.HOUR_OF_DAY) * 100) + c.get(Calendar.MINUTE);
		boolean isHotTime = ((to > from) && (t >= from) && (t <= to)) || ((to < from) && ((t >= from) || (t <= to)));
		if (isHotTime)
		{
			return true;
		}
		from = (19 * 100) + 0;
		to = (23 * 100) + 0;
		return ((to > from) && (t >= from) && (t <= to)) || ((to < from) && ((t >= from) || (t <= to)));
	}
	
	public Purge getPurge(int id)
	{
		return _purges.get(id);
	}
	
	public Collection<Purge> getPurges()
	{
		return _purges.values();
	}
	
	private void scheduleMondayReset()
	{
		final Calendar cal = Calendar.getInstance();
		cal.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
		cal.set(Calendar.HOUR_OF_DAY, 00);
		cal.set(Calendar.MINUTE, 00);
		cal.set(Calendar.SECOND, 0);
		cal.set(Calendar.MILLISECOND, 0);
		if (cal.getTimeInMillis() < Calendar.getInstance().getTimeInMillis())
		{
			cal.add(Calendar.WEEK_OF_MONTH, 1);
		}
		ThreadPool.schedule(new MondayResetTask(), cal.getTimeInMillis() - System.currentTimeMillis());
	}
	
	private static class SingletonHolder
	{
		protected static final PurgeData INSTANCE = new PurgeData();
	}
	
	protected class MondayResetTask implements Runnable
	{
		public MondayResetTask()
		{}
		
		@Override
		public void run()
		{
			if (!Config.PURGE_ENABLE)
			{
				return;
			}
			for (Purge purge : getPurges())
			{
				purge.onMonday();
			}
			LOGGER.info(getClass().getSimpleName() + ": Purges reset");
			scheduleMondayReset();
		}
	}
}
