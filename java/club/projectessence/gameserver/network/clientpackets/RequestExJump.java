/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.instancemanager.CeremonyOfChaosManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;

public class RequestExJump extends ClientPacket {
	private int _param1;
	private int _param2;
	private int _param3;
	private int _param4;
	private int _param5;
	private int _param6;

	@Override
	public void readImpl() {
		_param1 = readInt();
		_param2 = readInt();
		_param3 = readInt();
		_param4 = readInt();
		_param5 = readInt();
		_param6 = readInt();
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		CeremonyOfChaosManager.getInstance().addTrap(_param1, _param2, _param3, _param4, _param5, _param6);
	}
}
