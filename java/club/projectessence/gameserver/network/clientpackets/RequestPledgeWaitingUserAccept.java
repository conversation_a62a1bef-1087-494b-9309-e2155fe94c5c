/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.ClanEntryManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.JoinPledge;
import club.projectessence.gameserver.network.serverpackets.PledgeShowInfoUpdate;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListAdd;
import club.projectessence.gameserver.network.serverpackets.PledgeShowMemberListAll;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;
import club.projectessence.gameserver.network.serverpackets.pledge.ExPledgeCount;

/**
 * <AUTHOR>
 */
public class RequestPledgeWaitingUserAccept extends ClientPacket
{
	private boolean	_acceptRequest;
	private int		_playerId;
	
	@Override
	public void readImpl()
	{
		_acceptRequest = readInt() == 1;
		_playerId = readInt();
		readInt(); // clan id (?)
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		final Clan clan = player.getClan();
		if (clan == null)
		{
			return;
		}
		final int clanId = clan.getId();
		if (_acceptRequest)
		{
			final PlayerInstance target = World.getInstance().getPlayer(_playerId);
			if (target != null)
			{
				final long currentTime = System.currentTimeMillis();
				if ((target.getClan() == null) && (target.getClanJoinExpiryTime() < currentTime))
				{
					// Kiểm tra Faction: Từ chối nếu Faction của target không khớp với Faction của clan
					if (target.getFaction() != clan.getFaction())
					{
						player.sendMessage(target.getName() + "'s faction (" + target.getFaction() + ") does not match the clan's faction (" + clan.getFaction() + ").");
						return;
					}
					target.sendPacket(new JoinPledge(clan.getId()));
					// player.setPowerGrade(9); // academy
					target.setPowerGrade(5); // New member starts at 5, not confirmed.
					clan.addClanMember(target);
					target.setClanPrivileges(target.getClan().getRankPrivs(target.getPowerGrade()));
					target.sendPacket(SystemMessageId.ENTERED_THE_CLAN);
					final SystemMessage sm = new SystemMessage(SystemMessageId.S1_HAS_JOINED_THE_CLAN);
					sm.addString(target.getName());
					clan.broadcastToOnlineMembers(sm);
					if (clan.getCastleId() > 0)
					{
						final Castle castle = CastleManager.getInstance().getCastleByOwner(clan);
						if (castle != null)
						{
							castle.giveResidentialSkills(target);
						}
					}
					if (clan.getFortId() > 0)
					{
						final Fort fort = FortManager.getInstance().getFortByOwner(clan);
						if (fort != null)
						{
							fort.giveResidentialSkills(target);
						}
					}
					target.sendSkillList();
					clan.broadcastToOtherOnlineMembers(new PledgeShowMemberListAdd(target), target);
					clan.broadcastToOnlineMembers(new PledgeShowInfoUpdate(clan));
					clan.broadcastToOnlineMembers(new ExPledgeCount(clan));
					// This activates the clan tab on the new member.
					PledgeShowMemberListAll.sendAllTo(target);
					target.setClanJoinExpiryTime(0);
					// player.setClanJoinTime(currentTime);
					target.broadcastUserInfo();
					ClanEntryManager.getInstance().removePlayerApplication(clanId, _playerId);
				}
				else if (target.getClanJoinExpiryTime() > currentTime)
				{
					final SystemMessage sm = new SystemMessage(SystemMessageId.C1_WILL_BE_ABLE_TO_JOIN_YOUR_CLAN_IN_24_H_AFTER_LEAVING_THE_PREVIOUS_ONE);
					sm.addString(target.getName());
					player.sendPacket(sm);
				}
			}
		}
		else
		{
			ClanEntryManager.getInstance().removePlayerApplication(clanId, _playerId);
		}
	}
}
