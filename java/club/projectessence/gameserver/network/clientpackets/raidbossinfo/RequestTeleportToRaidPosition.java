/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.raidbossinfo;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.RaidTeleportData;
import club.projectessence.gameserver.enums.RaidBossStatus;
import club.projectessence.gameserver.instancemanager.GrandBossManager;
import club.projectessence.gameserver.instancemanager.RaidSpawnManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.itemcontainer.Inventory;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.network.serverpackets.raidbossinfo.ExRaidTeleportInfo;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;

/**
 * <AUTHOR>
 */
public class RequestTeleportToRaidPosition extends ClientPacket {
	private int _bossId;

	@Override
	public void readImpl() {
		_bossId = readInt();
	}

	@Override
	public void runImpl() {
		PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		if (Config.ONLY_CHARACTER_CREATE && !player.isGM()) {
			// player.sendMessage("Server has not started yet!");
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendPacket(new ExShowScreenMessage("Server has not started yet!", 2, 2000, 0, true, false));
			// Disconnection.of(client).defaultSequence(true);
			return;
		}

		if (player.isDead()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_TELEPORT_WHILE_YOU_ARE_DEAD);
			return;
		}

		// if (player.isInCombat()) // Custom
		// {
		// player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
		// return;
		// }

		if (player.isMovementDisabled() || player.cannotEscape() || player.hasBlockActions() || player.isAffected(EffectFlag.FEAR)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
			return;
		}

		if (player.isAffected(EffectFlag.CANNOT_ESCAPE)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
			return;
		}

		if (player.isInOlympiadMode() || player.inObserverMode() || player.isInTraingCamp() || player.isInsideZone(ZoneId.SPECIAL_HUNTING)) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_TELEPORT_RIGHT_NOW);
			return;
		}

		if (player.isInInstance()) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_USE_TELEPORT_IN_AN_INSTANCE_ZONE);
			return;
		}

		final Location teleportLoc = RaidTeleportData.getInstance().getTeleportLocation(_bossId);
		if (teleportLoc == null) {
			LOGGER.warning(player + " request teleport to forbidden raidboss: " + _bossId);
			return;
		}
		if ((RaidSpawnManager.getInstance().getNpcStatusId(_bossId) == RaidBossStatus.ALIVE) || (GrandBossManager.getInstance().getBossVisualStatus(_bossId) > 0)) {
			int price = player.getVariables().getBoolean(PlayerVariables.FREE_RAID_TELEPORT_USED, false) ? 10 : 0;
			ItemInstance lcoin = player.getInventory().getItemByItemId(Inventory.LCOIN_ID);
			if ((price <= 0) || ((lcoin != null) && (lcoin.getCount() >= price))) {
				if (price > 0) {
					player.destroyItem("Raidboss Teleport", lcoin, price, player, true);
				}

				player.getVariables().set(PlayerVariables.FREE_RAID_TELEPORT_USED, true);
				player.sendPacket(new ExRaidTeleportInfo(player));

				player.abortCast();
				player.stopMove(null);
				AutoPlayTaskManager.getInstance().stopAutoPlay(player);
				player.setTeleportLoc(teleportLoc);
				player.doCast(CommonSkill.TELEPORT.getSkill());
			}
		}
	}
}
