package club.projectessence.gameserver.network.clientpackets.balok;

import club.projectessence.gameserver.instancemanager.BattleWithBalokManager;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.balok.BalrogWarShowRanking;

public class ExBalrogWarShowRanking extends ClientPacket
{
	@Override
	protected void runImpl() throws Exception
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		if (!BattleWithBalokManager.getInstance().getInBattle())
		{
			return;
		}
		player.sendPacket(new BalrogWarShowRanking());
	}
	
	@Override
	protected void readImpl() throws Exception
	{
		// TODO Auto-generated method stub
	}
}
