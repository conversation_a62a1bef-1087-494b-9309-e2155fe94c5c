/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.mablegame;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.MableGameEventData;
import club.projectessence.gameserver.data.xml.MableGameEventData.MableGamePlayerState;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.holders.ItemHolder;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExMableGameShowPlayerState extends ServerPacket {
	private final int _commonDiceLimit;
	private final int _dailyAvailableRounds;
	private final int _highestCellId;
	private final ItemHolder _finishReward;
	private final List<ItemHolder> _resetItems;
	private final MableGamePlayerState _playerState;

	public ExMableGameShowPlayerState(PlayerInstance player) {
		final MableGameEventData data = MableGameEventData.getInstance();
		_commonDiceLimit = data.getCommonDiceLimit();
		_dailyAvailableRounds = data.getDailyAvailableRounds();
		_highestCellId = data.getHighestCellId();
		_finishReward = data.getRoundReward();
		_resetItems = data.getResetItems();
		_playerState = data.getPlayerState(player.getAccountName());
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_MABLE_GAME_SHOW_PLAYER_STATE, buffer);
		buffer.writeInt(_playerState.getRound()); // nMableGamePlayCount (Round)
		buffer.writeInt(_playerState.getCurrentCellId()); // nCurrentCellId
		buffer.writeInt(_playerState.getRemainCommonDice()); // nRemainNormalDiceUseCount
		buffer.writeInt(_commonDiceLimit); // nMaxNormalDiceUseCount
		buffer.writeByte(_playerState.getCurrentCellId() == _highestCellId ? (_dailyAvailableRounds == _playerState.getRound() ? 6 : 5) : 0); // (cCurrentState // 0-3 unk / 4 = just finished / buttoncCurrentState / 5 = reset / 6 = all rounds completed)

		buffer.writeInt(_dailyAvailableRounds); // vFinishRewards
		for (int i = 1; i <= _dailyAvailableRounds; i++) {
			buffer.writeInt(i); // nPlayCount
			buffer.writeInt(_finishReward.getId()); // nItemClassID
			buffer.writeLong(_finishReward.getCount()); // nItemAmount
		}

		buffer.writeInt(_resetItems.size()); // vResetItems
		for (ItemHolder item : _resetItems) {
			buffer.writeInt(item.getId()); // nItemClassID
			buffer.writeLong(item.getCount()); // nItemAmount
		}
	}
}
