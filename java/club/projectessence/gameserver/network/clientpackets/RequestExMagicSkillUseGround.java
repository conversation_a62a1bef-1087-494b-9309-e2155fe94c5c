/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.ActionFailed;
import club.projectessence.gameserver.network.serverpackets.ValidateLocation;
import club.projectessence.gameserver.util.Broadcast;
import club.projectessence.gameserver.util.Util;

/**
 * Fromat:(ch) dddddc
 *
 * <AUTHOR>
 */
public class RequestExMagicSkillUseGround extends ClientPacket {
	private int _x;
	private int _y;
	private int _z;
	private int _skillId;
	private boolean _ctrlPressed;
	private boolean _shiftPressed;

	@Override
	public void readImpl() {
		_x = readInt();
		_y = readInt();
		_z = readInt();
		_skillId = readInt();
		_ctrlPressed = readInt() != 0;
		_shiftPressed = readByte() != 0;
	}

	@Override
	public void runImpl() {
		// Get the current PlayerInstance of the player
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		// Get the level of the used skill
		final int level = player.getSkillLevel(_skillId);
		if (level <= 0) {
			client.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}

		// Get the Skill template corresponding to the skillID received from the client
		final Skill skill = SkillData.getInstance().getSkill(_skillId, level);

		// Check the validity of the skill
		if (skill != null) {
			player.setCurrentSkillWorldPosition(new Location(_x, _y, _z));

			// normally magicskilluse packet turns char client side but for these skills, it doesn't (even with correct target)
			player.setHeading(Util.calculateHeadingFrom(player.getX(), player.getY(), _x, _y));
			Broadcast.toKnownPlayers(player, new ValidateLocation(player));
			player.useMagic(skill, null, _ctrlPressed, _shiftPressed, true);
		} else {
			client.sendPacket(ActionFailed.STATIC_PACKET);
			LOGGER.warning("No skill found with id " + _skillId + " and level " + level + " !!");
		}
	}
}
