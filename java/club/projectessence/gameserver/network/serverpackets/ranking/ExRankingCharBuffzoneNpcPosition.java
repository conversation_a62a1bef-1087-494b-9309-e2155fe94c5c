/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.ranking;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.instancemanager.RankManager;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExRankingCharBuffzoneNpcPosition extends ServerPacket {

	public ExRankingCharBuffzoneNpcPosition() {
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_RANKING_CHAR_BUFFZONE_NPC_POSITION, buffer);
		final Location npcLocation = RankManager.getInstance().getBuffzoneNpcLocation();
		buffer.writeByte(npcLocation != null ? 0x01 : 0x00); // bIsInWorld
		buffer.writeInt(npcLocation != null ? npcLocation.getX() : 0x00); // nPosX
		buffer.writeInt(npcLocation != null ? npcLocation.getY() : 0x00); // nPosY
		buffer.writeInt(npcLocation != null ? npcLocation.getZ() : 0x00); // nPosZ
	}
}
