package club.projectessence.gameserver.model.faction;

import club.projectessence.gameserver.enums.LeadershipTask;

/**
 * Class representing progress on a leadership task.
 */
public class FactionLeaderTaskProgress
{
	private final int _objId;
	private final LeadershipTask _task;
	private int _progress;
	private final int _target;
	private boolean _completed;
	private long _resetTime;
	private boolean _rewardClaimed;
	private long _startTime;
	
	public FactionLeaderTaskProgress(int objId, LeadershipTask task)
	{
		_objId = objId;
		_task = task;
		_progress = 0;
		_target = task.getTarget();
		_completed = false;
		_rewardClaimed = false;
		_startTime = System.currentTimeMillis();
		calculateResetTime();
	}
	
	public FactionLeaderTaskProgress(int objId, LeadershipTask task, int progress, boolean completed, long resetTime, boolean rewardClaimed)
	{
		_objId = objId;
		_task = task;
		_progress = progress;
		_target = task.getTarget();
		_completed = completed;
		_resetTime = resetTime;
		_rewardClaimed = rewardClaimed;
		_startTime = System.currentTimeMillis();
	}
	
	// Getters
	public int getObjId() { return _objId; }
	public LeadershipTask getTask() { return _task; }
	public int getProgress() { return _progress; }
	public int getTarget() { return _target; }
	public boolean isCompleted() { return _completed; }
	public long getResetTime() { return _resetTime; }
	public boolean isRewardClaimed() { return _rewardClaimed; }
	public long getStartTime() { return _startTime; }
	
	// Progress Management
	public void setProgress(int progress)
	{
		_progress = Math.min(progress, _target);
		checkCompletion();
	}
	
	public void addProgress(int amount)
	{
		setProgress(_progress + amount);
	}
	
	public void incrementProgress()
	{
		addProgress(1);
	}
	
	private void checkCompletion()
	{
		if (_progress >= _target && !_completed)
		{
			_completed = true;
		}
	}
	
	// Reward Management
	public boolean canClaimReward()
	{
		return _completed && !_rewardClaimed;
	}
	
	public void claimReward()
	{
		if (canClaimReward())
		{
			_rewardClaimed = true;
		}
	}
	
	// Reset Management
	public boolean needsReset()
	{
		return System.currentTimeMillis() >= _resetTime;
	}
	
	public void reset()
	{
		_progress = 0;
		_completed = false;
		_rewardClaimed = false;
		_startTime = System.currentTimeMillis();
		calculateResetTime();
	}
	
	private void calculateResetTime()
	{
		long currentTime = System.currentTimeMillis();
		long interval = _task.getCategory().getResetInterval();
		
		switch (_task.getCategory())
		{
			case DAILY:
				// Reset at 6:30 AM next day (like GvE system)
				java.util.Calendar cal = java.util.Calendar.getInstance();
				cal.setTimeInMillis(currentTime);
				cal.add(java.util.Calendar.DAY_OF_MONTH, 1);
				cal.set(java.util.Calendar.HOUR_OF_DAY, 6);
				cal.set(java.util.Calendar.MINUTE, 30);
				cal.set(java.util.Calendar.SECOND, 0);
				cal.set(java.util.Calendar.MILLISECOND, 0);
				_resetTime = cal.getTimeInMillis();
				break;
				
			case WEEKLY:
				// Reset every Monday at 6:30 AM
				java.util.Calendar weekCal = java.util.Calendar.getInstance();
				weekCal.setTimeInMillis(currentTime);
				weekCal.add(java.util.Calendar.WEEK_OF_YEAR, 1);
				weekCal.set(java.util.Calendar.DAY_OF_WEEK, java.util.Calendar.MONDAY);
				weekCal.set(java.util.Calendar.HOUR_OF_DAY, 6);
				weekCal.set(java.util.Calendar.MINUTE, 30);
				weekCal.set(java.util.Calendar.SECOND, 0);
				weekCal.set(java.util.Calendar.MILLISECOND, 0);
				_resetTime = weekCal.getTimeInMillis();
				break;
				
			case MONTHLY:
				// Reset on 1st of next month at 6:30 AM
				java.util.Calendar monthCal = java.util.Calendar.getInstance();
				monthCal.setTimeInMillis(currentTime);
				monthCal.add(java.util.Calendar.MONTH, 1);
				monthCal.set(java.util.Calendar.DAY_OF_MONTH, 1);
				monthCal.set(java.util.Calendar.HOUR_OF_DAY, 6);
				monthCal.set(java.util.Calendar.MINUTE, 30);
				monthCal.set(java.util.Calendar.SECOND, 0);
				monthCal.set(java.util.Calendar.MILLISECOND, 0);
				_resetTime = monthCal.getTimeInMillis();
				break;
				
			default:
				_resetTime = currentTime + interval;
				break;
		}
	}
	
	// Utility Methods
	public double getProgressPercentage()
	{
		if (_target == 0) return 100.0;
		return Math.min(100.0, (_progress * 100.0) / _target);
	}
	
	public String getProgressString()
	{
		return _progress + "/" + _target;
	}
	
	public long getTimeRemaining()
	{
		return Math.max(0, _resetTime - System.currentTimeMillis());
	}
	
	public String getTimeRemainingString()
	{
		long remaining = getTimeRemaining();
		if (remaining <= 0) return "Expired";
		
		long days = remaining / (24 * 60 * 60 * 1000);
		remaining %= (24 * 60 * 60 * 1000);
		long hours = remaining / (60 * 60 * 1000);
		remaining %= (60 * 60 * 1000);
		long minutes = remaining / (60 * 1000);
		
		if (days > 0)
		{
			return days + "d " + hours + "h";
		}
		else if (hours > 0)
		{
			return hours + "h " + minutes + "m";
		}
		else
		{
			return minutes + "m";
		}
	}
	
	public String getDifficultyString()
	{
		int difficulty = _task.getDifficulty();
		StringBuilder stars = new StringBuilder();
		for (int i = 0; i < 5; i++)
		{
			if (i < difficulty)
			{
				stars.append("★");
			}
			else
			{
				stars.append("☆");
			}
		}
		return stars.toString();
	}
	
	public String getStatusString()
	{
		if (_rewardClaimed)
		{
			return "✅ Completed";
		}
		else if (_completed)
		{
			return "🎁 Ready to Claim";
		}
		else if (needsReset())
		{
			return "⏰ Expired";
		}
		else
		{
			return "📋 In Progress";
		}
	}
	
	public String getCategoryIcon()
	{
		switch (_task.getCategory())
		{
			case DAILY:
				return "📅";
			case WEEKLY:
				return "📆";
			case MONTHLY:
				return "🗓️";
			default:
				return "📋";
		}
	}
	
	@Override
	public String toString()
	{
		return String.format("TaskProgress[%s: %s (%s)]", 
			_task.getName(), getProgressString(), getStatusString());
	}
}
