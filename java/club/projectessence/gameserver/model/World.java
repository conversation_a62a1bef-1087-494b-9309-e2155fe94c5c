/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.gameserver.ai.CreatureAI;
import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PetInstance;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.npc.OnNpcCreatureSee;
import club.projectessence.gameserver.model.zone.ZoneId;
import club.projectessence.gameserver.network.Disconnection;
import club.projectessence.gameserver.network.serverpackets.DeleteObject;

public class World
{
	/**
	 * Gracia border Flying objects not allowed to the east of it.
	 */
	public static final int								GRACIA_MAX_X				= -166168;
	public static final int								GRACIA_MAX_Z				= 6105;
	public static final int								GRACIA_MIN_Z				= -895;
	/**
	 * Bit shift, defines number of regions note, shifting by 15 will result in regions corresponding to map tiles shifting by 11 divides one tile to 16x16 regions.
	 */
	public static final int								SHIFT_BY					= 11;
	public static final int								TILE_SIZE					= 32768;
	/**
	 * Map dimensions.
	 */
	public static final int								TILE_X_MIN					= 11;
	public static final int								TILE_Y_MIN					= 10;
	public static final int								TILE_X_MAX					= 28;
	public static final int								TILE_Y_MAX					= 26;
	public static final int								TILE_ZERO_COORD_X			= 20;
	public static final int								TILE_ZERO_COORD_Y			= 18;
	public static final int								MAP_MIN_X					= (TILE_X_MIN - TILE_ZERO_COORD_X) * TILE_SIZE;
	/**
	 * Calculated offset used so top left region is 0,0
	 */
	public static final int								OFFSET_X					= Math.abs(MAP_MIN_X >> SHIFT_BY);
	public static final int								MAP_MIN_Y					= (TILE_Y_MIN - TILE_ZERO_COORD_Y) * TILE_SIZE;
	public static final int								OFFSET_Y					= Math.abs(MAP_MIN_Y >> SHIFT_BY);
	public static final int								MAP_MAX_X					= ((TILE_X_MAX - TILE_ZERO_COORD_X) + 1) * TILE_SIZE;
	/**
	 * Number of regions.
	 */
	private static final int							REGIONS_X					= (MAP_MAX_X >> SHIFT_BY) + OFFSET_X;
	public static final int								MAP_MAX_Y					= ((TILE_Y_MAX - TILE_ZERO_COORD_Y) + 1) * TILE_SIZE;
	private static final int							REGIONS_Y					= (MAP_MAX_Y >> SHIFT_BY) + OFFSET_Y;
	private static final WorldRegion[][]				_worldRegions				= new WorldRegion[REGIONS_X + 1][REGIONS_Y + 1];
	private static final Logger							LOGGER						= Logger.getLogger(World.class.getName());
	/**
	 * Map containing all the players in game.
	 */
	private static final Map<Integer, PlayerInstance>	_allPlayers					= new ConcurrentHashMap<>();
	/** Map containing all the Good players in game. */
	private static final Map<Integer, PlayerInstance>	_allFirePlayers				= new ConcurrentHashMap<>();
	/** Map containing all the Evil players in game. */
	private static final Map<Integer, PlayerInstance>	_allWaterPlayers			= new ConcurrentHashMap<>();
	/**
	 * Map containing all visible objects.
	 */
	private static final Map<Integer, WorldObject>		_allObjects					= new ConcurrentHashMap<>();
	/**
	 * Map with the pets instances and their owner ID.
	 */
	private static final Map<Integer, PetInstance>		_petsInstance				= new ConcurrentHashMap<>();
	private static final AtomicInteger					_partyNumber				= new AtomicInteger();
	private static final AtomicInteger					_memberInPartyNumber		= new AtomicInteger();
	private static final Set<PlayerInstance>			_pkPlayers					= ConcurrentHashMap.newKeySet();
	private static final AtomicInteger					_lastPkTime					= new AtomicInteger((int) (System.currentTimeMillis() / 100));
	public static volatile int							MAX_CONNECTED_COUNT			= 0;
	public static volatile int							OFFLINE_TRADE_COUNT			= 0;
	public static int									LOOT_BOXES_OPENED			= 0;
	public static long									L_COINS_FARMED				= 0;
	public static long									LAST_PRIVATE_STORE_UPDATE	= 0;
	// Event Sibi Stats
	public static long									BALTHUS_KNIGHT_MARK_FARMED	= 0;
	public static long									LIFE_CONTROL_TOWER_FARMED	= 0;
	public static long									MID_GRADE_HP_POTION_FARMED	= 0;
	public static long									SCROLL_BOOST_ATTACK_FARMED	= 0;
	public static long									SCROLL_BOOST_DEFENSE_FARMED	= 0;
	public static long									SAYHA_COOKIE_FARMED			= 0;
	public static long									SAYHA_BLESSING_FARMED		= 0;
	public static long									ADENA_TAKE					= 0;
	public static long									L_COINS_TAKE				= 0;
	public static long									SIBI_COIN_TAKE				= 0;
	// public static long WATERMELON_EVENT_ADENA_TAKE = 0;
	// public static long WATERMELON_EVENT_L_COINS_TAKE = 0;
	/**
	 * Map containing all the players in Store Buy or Sell mode.
	 */
	private static Map<Integer, PlayerInstance>			_allStoreModeBuySellPlayers	= new ConcurrentHashMap<>();
	
	/**
	 * Constructor of World.
	 */
	protected World()
	{
		// Initialize regions.
		for (int x = 0; x <= REGIONS_X; x++)
		{
			for (int y = 0; y <= REGIONS_Y; y++)
			{
				_worldRegions[x][y] = new WorldRegion(x, y);
			}
		}
		// Set surrounding regions.
		for (int rx = 0; rx <= REGIONS_X; rx++)
		{
			for (int ry = 0; ry <= REGIONS_Y; ry++)
			{
				final List<WorldRegion> surroundingRegions = new ArrayList<>();
				for (int sx = rx - 1; sx <= (rx + 1); sx++)
				{
					for (int sy = ry - 1; sy <= (ry + 1); sy++)
					{
						if (((sx >= 0) && (sx < REGIONS_X) && (sy >= 0) && (sy < REGIONS_Y)))
						{
							surroundingRegions.add(_worldRegions[sx][sy]);
						}
					}
				}
				WorldRegion[] regionArray = new WorldRegion[surroundingRegions.size()];
				regionArray = surroundingRegions.toArray(regionArray);
				_worldRegions[rx][ry].setSurroundingRegions(regionArray);
			}
		}
		LOGGER.info(getClass().getSimpleName() + ": (" + REGIONS_X + " by " + REGIONS_Y + ") World Region Grid set up.");
	}
	
	/**
	 * @return the current instance of World
	 */
	public static World getInstance()
	{
		return SingletonHolder.INSTANCE;
	}
	
	/**
	 * Adds an object to the world.<br>
	 * <br>
	 * <b><u>Example of use</u>:</b>
	 * <ul>
	 * <li>Withdraw an item from the warehouse, create an item</li>
	 * <li>Spawn a Creature (PC, NPC, Pet)</li>
	 * </ul>
	 *
	 * @param object
	 */
	public void addObject(WorldObject object)
	{
		_allObjects.putIfAbsent(object.getObjectId(), object);
		// if (_allObjects.putIfAbsent(object.getObjectId(), object) != null)
		// {
		// LOGGER.warning(getClass().getSimpleName() + ": Object " + object + " already exists in the world.");// Stack Trace: " + CommonUtil.getTraceString(Thread.currentThread().getStackTrace()));
		// }
		if (object.isPlayer())
		{
			final PlayerInstance newPlayer = (PlayerInstance) object;
			if (newPlayer.isTeleporting()) // TODO: Drop when we stop removing player from the world while teleporting.
			{
				return;
			}
			final PlayerInstance existingPlayer = _allPlayers.putIfAbsent(object.getObjectId(), newPlayer);
			if (existingPlayer != null)
			{
				Disconnection.of(existingPlayer).logout(false, false);
				Disconnection.of(newPlayer).logout(false, false);
				LOGGER.warning(getClass().getSimpleName() + ": Duplicate character!? Disconnected both characters (" + newPlayer.getName() + ")");
			}
			else if (Config.FACTION_SYSTEM_ENABLED)
			{
				addFactionPlayerToWorld(newPlayer);
			}
		}
	}
	
	/**
	 * Removes an object from the world.<br>
	 * <br>
	 * <b><u>Example of use</u>:</b>
	 * <ul>
	 * <li>Delete item from inventory, transfer Item from inventory to warehouse</li>
	 * <li>Crystallize item</li>
	 * <li>Remove NPC/PC/Pet from the world</li>
	 * </ul>
	 *
	 * @param object
	 *            the object to remove
	 */
	public void removeObject(WorldObject object)
	{
		_allObjects.remove(object.getObjectId());
		if (object.isPlayer())
		{
			final PlayerInstance player = (PlayerInstance) object;
			if (player.isTeleporting()) // TODO: Drop when we stop removing player from the world while teleporting.
			{
				return;
			}
			_allPlayers.remove(object.getObjectId());
			if (Config.FACTION_SYSTEM_ENABLED)
			{
				if (player.getFaction() == Faction.FIRE)
				{
					_allFirePlayers.remove(player.getObjectId());
				}
				else if (player.getFaction() == Faction.WATER)
				{
					_allWaterPlayers.remove(player.getObjectId());
				}
			}
		}
	}
	
	/**
	 * <b><u>Example of use</u>:</b>
	 * <ul>
	 * <li>Client packets : Action, AttackRequest, RequestJoinParty, RequestJoinPledge...</li>
	 * </ul>
	 *
	 * @param objectId
	 *            Identifier of the WorldObject
	 * @return the WorldObject object that belongs to an ID or null if no object found.
	 */
	public WorldObject findObject(int objectId)
	{
		return _allObjects.get(objectId);
	}
	
	public Collection<WorldObject> getVisibleObjects()
	{
		return _allObjects.values();
	}
	
	/**
	 * Get the count of all visible objects in world.
	 *
	 * @return count off all World objects
	 */
	public int getVisibleObjectsCount()
	{
		return _allObjects.size();
	}
	
	public Collection<PlayerInstance> getPlayers()
	{
		return _allPlayers.values();
	}
	
	public Collection<PlayerInstance> getAllFirePlayers()
	{
		return _allFirePlayers.values();
	}
	
	public Collection<PlayerInstance> getAllWaterPlayers()
	{
		return _allWaterPlayers.values();
	}
	
	public Collection<PlayerInstance> getPlayersSellingOrBuying()
	{
		if ((System.currentTimeMillis() - Config.CACHE_TIME_PRIVATE_STORE) > LAST_PRIVATE_STORE_UPDATE)
		{
			_allStoreModeBuySellPlayers.clear();
			_allPlayers.values().stream().filter(PlayerInstance::isInStoreSellOrBuyMode).forEach(player -> _allStoreModeBuySellPlayers.put(player.getObjectId(), player));
		}
		return _allStoreModeBuySellPlayers.values();
	}
	
	public Collection<PlayerInstance> getFakePlayers()
	{
		return _allPlayers.values().stream().filter(plr -> plr.isFakePlayer()).collect(Collectors.toList());
	}
	
	/**
	 * <b>If you have access to player objectId use {@link #getPlayer(int playerObjId)}</b>
	 *
	 * @param name
	 *            Name of the player to get Instance
	 * @return the player instance corresponding to the given name.
	 */
	public PlayerInstance getPlayer(String name)
	{
		return getPlayer(CharNameTable.getInstance().getIdByName(name));
	}
	
	/**
	 * @param objectId
	 *            of the player to get Instance
	 * @return the player instance corresponding to the given object ID.
	 */
	public PlayerInstance getPlayer(int objectId)
	{
		return _allPlayers.get(objectId);
	}
	
	/**
	 * @param ownerId
	 *            ID of the owner
	 * @return the pet instance from the given ownerId.
	 */
	public PetInstance getPet(int ownerId)
	{
		return _petsInstance.get(ownerId);
	}
	
	/**
	 * Add the given pet instance from the given ownerId.
	 *
	 * @param ownerId
	 *            ID of the owner
	 * @param pet
	 *            PetInstance of the pet
	 * @return
	 */
	public PetInstance addPet(int ownerId, PetInstance pet)
	{
		return _petsInstance.put(ownerId, pet);
	}
	
	/**
	 * Remove the given pet instance.
	 *
	 * @param ownerId
	 *            ID of the owner
	 */
	public void removePet(int ownerId)
	{
		_petsInstance.remove(ownerId);
	}
	
	/**
	 * Add a WorldObject in the world. <b><u>Concept</u>:</b> WorldObject (including PlayerInstance) are identified in <b>_visibleObjects</b> of his current WorldRegion and in <b>_knownObjects</b> of other surrounding Creatures<br>
	 * PlayerInstance are identified in <b>_allPlayers</b> of World, in <b>_allPlayers</b> of his current WorldRegion and in <b>_knownPlayer</b> of other surrounding Creatures <b><u> Actions</u>:</b>
	 * <li>Add the WorldObject object in _allPlayers* of World</li>
	 * <li>Add the WorldObject object in _gmList** of GmListTable</li>
	 * <li>Add object in _knownObjects and _knownPlayer* of all surrounding WorldRegion Creatures</li>
	 * <li>If object is a Creature, add all surrounding WorldObject in its _knownObjects and all surrounding PlayerInstance in its _knownPlayer</li><br>
	 * <i>* only if object is a PlayerInstance</i><br>
	 * <i>** only if object is a GM PlayerInstance</i> <font color=#FF0000><b><u>Caution</u>: This method DOESN'T ADD the object in _visibleObjects and _allPlayers* of WorldRegion (need synchronisation)</b></font><br>
	 * <font color=#FF0000><b><u>Caution</u>: This method DOESN'T ADD the object to _allObjects and _allPlayers* of World (need synchronisation)</b></font> <b><u> Example of use</u>:</b>
	 * <li>Drop an Item</li>
	 * <li>Spawn a Creature</li>
	 * <li>Apply Death Penalty of a PlayerInstance</li><br>
	 *
	 * @param object
	 *            L2object to add in the world
	 * @param newRegion
	 *            WorldRegion in wich the object will be add (not used)
	 */
	public void addVisibleObject(WorldObject object, WorldRegion newRegion)
	{
		if (!newRegion.isActive())
		{
			return;
		}
		// List<WorldObject> objects = new ArrayList<>();
		forEachVisibleObject(object, WorldObject.class, wo ->
		{
			describeOneToAnother(object, wo);
		});
		// Collections.sort(objects, (o1, o2) ->
		// {
		// int zDiff1 = Math.abs(object.getZ() - o1.getZ());
		// int zDiff2 = Math.abs(object.getZ() - o2.getZ());
		// if (zDiff1 > zDiff2)
		// {
		// return 1;
		// }
		// if (zDiff1 < zDiff2)
		// {
		// return -1;
		// }
		// return 0;
		// });
		// int i = 0;
		// for (WorldObject wo : objects)
		// {
		// if ((Math.abs(object.getZ()) - wo.getZ()) > 2000)
		// {
		// continue;
		// }
		// if (object.isPlayer())
		// {
		// i++;
		// System.out.println(i);
		// ThreadPool.get().schedule(() -> describeOneToAnother(object, wo), i * 100);
		// }
		// else
		// {
		// describeOneToAnother(object, wo);
		// }
		// }
	}
	
	private void describeOneToAnother(WorldObject object, WorldObject wo)
	{
		boolean isInsideToi = Config.TOI_MAX_Z_ENABLED ? object.isInsideZone(ZoneId.Z_Load) : false;
		if (object.isPlayer() && wo.isVisibleFor((PlayerInstance) object) && (!isInsideToi || (object.calculateDistanceZ(wo) <= Config.TOI_MAX_Z)))
		{
			wo.sendInfo((PlayerInstance) object);
			if (wo.isPlayer() && (wo.getActingPlayer().getTransformationDisplayId() > 0)) // Fix Mounts not loading
			{
				wo.sendInfo((PlayerInstance) object);
			}
			if (wo.isCreature())
			{
				final CreatureAI ai = ((Creature) wo).getAI();
				if (ai != null)
				{
					ai.describeStateToPlayer((PlayerInstance) object);
					if (wo.isMonster() && (ai.getIntention() == CtrlIntention.AI_INTENTION_IDLE))
					{
						ai.setIntention(CtrlIntention.AI_INTENTION_ACTIVE);
					}
				}
			}
		}
		if (wo.isPlayer() && object.isVisibleFor((PlayerInstance) wo) && (!isInsideToi || (object.calculateDistanceZ(wo) <= Config.TOI_MAX_Z)))
		{
			object.sendInfo((PlayerInstance) wo);
			if (object.isPlayer() && (object.getActingPlayer().getTransformationDisplayId() > 0)) // Fix Mounts not loading
			{
				object.sendInfo((PlayerInstance) wo);
			}
			if (object.isCreature())
			{
				final CreatureAI ai = ((Creature) object).getAI();
				if (ai != null)
				{
					ai.describeStateToPlayer((PlayerInstance) wo);
					if (object.isMonster() && (ai.getIntention() == CtrlIntention.AI_INTENTION_IDLE))
					{
						ai.setIntention(CtrlIntention.AI_INTENTION_ACTIVE);
					}
				}
			}
		}
		if (wo.isNpc() && object.isCreature())
		{
			EventDispatcher.getInstance().notifyEventAsync(new OnNpcCreatureSee((Npc) wo, (Creature) object, object.isSummon()), (Npc) wo);
		}
		if (object.isNpc() && wo.isCreature())
		{
			EventDispatcher.getInstance().notifyEventAsync(new OnNpcCreatureSee((Npc) object, (Creature) wo, wo.isSummon()), (Npc) object);
		}
	}
	
	public static void addFactionPlayerToWorld(PlayerInstance player)
	{
		if (player.getFaction() == Faction.FIRE)
		{
			_allFirePlayers.put(player.getObjectId(), player);
		}
		else if (player.getFaction() == Faction.WATER)
		{
			_allWaterPlayers.put(player.getObjectId(), player);
		}
	}
	
	/**
	 * Remove a WorldObject from the world. <b><u>Concept</u>:</b> WorldObject (including PlayerInstance) are identified in <b>_visibleObjects</b> of his current WorldRegion and in <b>_knownObjects</b> of other surrounding Creatures<br>
	 * PlayerInstance are identified in <b>_allPlayers</b> of World, in <b>_allPlayers</b> of his current WorldRegion and in <b>_knownPlayer</b> of other surrounding Creatures <b><u> Actions</u>:</b>
	 * <li>Remove the WorldObject object from _allPlayers* of World</li>
	 * <li>Remove the WorldObject object from _visibleObjects and _allPlayers* of WorldRegion</li>
	 * <li>Remove the WorldObject object from _gmList** of GmListTable</li>
	 * <li>Remove object from _knownObjects and _knownPlayer* of all surrounding WorldRegion Creatures</li>
	 * <li>If object is a Creature, remove all WorldObject from its _knownObjects and all PlayerInstance from its _knownPlayer</li> <font color=#FF0000><b><u>Caution</u>: This method DOESN'T REMOVE the object from _allObjects of World</b></font> <i>* only if object is a PlayerInstance</i><br>
	 * <i>** only if object is a GM PlayerInstance</i> <b><u> Example of use</u>:</b>
	 * <li>Pickup an Item</li>
	 * <li>Decay a Creature</li><br>
	 *
	 * @param object
	 *            L2object to remove from the world
	 * @param oldRegion
	 *            WorldRegion in which the object was before removing
	 */
	public void removeVisibleObject(WorldObject object, WorldRegion oldRegion)
	{
		if ((object == null) || (oldRegion == null))
		{
			return;
		}
		oldRegion.removeVisibleObject(object);
		// Go through all surrounding WorldRegion Creatures
		final WorldRegion[] surroundingRegions = oldRegion.getSurroundingRegions();
		for (int i = 0; i < surroundingRegions.length; i++)
		{
			final List<WorldObject> visibleObjects = surroundingRegions[i].getVisibleObjects();
			for (int j = 0; j < visibleObjects.size(); j++)
			{
				final WorldObject wo = visibleObjects.get(j);
				if ((wo == null) || (wo == object))
				{
					continue;
				}
				if (object.isCreature())
				{
					final Creature objectCreature = (Creature) object;
					final CreatureAI ai = objectCreature.getAI();
					if (ai != null)
					{
						ai.notifyEvent(CtrlEvent.EVT_FORGET_OBJECT, wo);
					}
					if (objectCreature.getTarget() == wo)
					{
						objectCreature.setTarget(null);
					}
					if (object.isPlayer())
					{
						object.sendPacket(new DeleteObject(wo));
					}
				}
				if (wo.isCreature())
				{
					final Creature woCreature = (Creature) wo;
					final CreatureAI ai = woCreature.getAI();
					if (ai != null)
					{
						ai.notifyEvent(CtrlEvent.EVT_FORGET_OBJECT, object);
					}
					if (woCreature.getTarget() == object)
					{
						woCreature.setTarget(null);
					}
					if (wo.isPlayer())
					{
						wo.sendPacket(new DeleteObject(object));
					}
				}
			}
		}
	}
	
	public void switchRegion(WorldObject object, WorldRegion newRegion)
	{
		final WorldRegion oldRegion = object.getWorldRegion();
		if ((oldRegion == null) || (oldRegion == newRegion))
		{
			return;
		}
		boolean isInsideToi = Config.TOI_MAX_Z_ENABLED ? object.isInsideZone(ZoneId.Z_Load) : false;
		final WorldRegion[] newSurroundingRegions = newRegion.getSurroundingRegions();
		for (int i = 0; i < newSurroundingRegions.length; i++)
		{
			final WorldRegion worldRegion = newSurroundingRegions[i];
			if (oldRegion.isSurroundingRegion(worldRegion))
			{
				continue;
			}
			final List<WorldObject> visibleObjects = worldRegion.getVisibleObjects();
			for (int j = 0; j < visibleObjects.size(); j++)
			{
				final WorldObject wo = visibleObjects.get(j);
				if ((wo == null) || (wo == object) || (wo.getInstanceWorld() != object.getInstanceWorld()) || (isInsideToi && (wo.calculateDistanceZ(object) > Config.TOI_MAX_Z)))
				{
					continue;
				}
				if (object.isPlayer() && wo.isVisibleFor((PlayerInstance) object) && (!isInsideToi || (object.calculateDistanceZ(wo) <= Config.TOI_MAX_Z)))
				{
					wo.sendInfo((PlayerInstance) object);
					if (wo.isPlayer() && (wo.getActingPlayer().getTransformationDisplayId() > 0)) // Fix Mounts not loading
					{
						wo.sendInfo((PlayerInstance) object);
					}
					if (wo.isCreature())
					{
						final CreatureAI ai = ((Creature) wo).getAI();
						if (ai != null)
						{
							ai.describeStateToPlayer((PlayerInstance) object);
							if (wo.isMonster() && (ai.getIntention() == CtrlIntention.AI_INTENTION_IDLE))
							{
								ai.setIntention(CtrlIntention.AI_INTENTION_ACTIVE);
							}
						}
					}
				}
				if (wo.isPlayer() && object.isVisibleFor((PlayerInstance) wo) && (!isInsideToi || (object.calculateDistanceZ(wo) <= Config.TOI_MAX_Z)))
				{
					object.sendInfo((PlayerInstance) wo);
					if (object.isPlayer() && (object.getActingPlayer().getTransformationDisplayId() > 0)) // Fix Mounts not loading
					{
						object.sendInfo((PlayerInstance) wo);
					}
					if (object.isCreature())
					{
						final CreatureAI ai = ((Creature) object).getAI();
						if (ai != null)
						{
							ai.describeStateToPlayer((PlayerInstance) wo);
							if (object.isMonster() && (ai.getIntention() == CtrlIntention.AI_INTENTION_IDLE))
							{
								ai.setIntention(CtrlIntention.AI_INTENTION_ACTIVE);
							}
						}
					}
				}
				if (wo.isNpc() && object.isCreature())
				{
					EventDispatcher.getInstance().notifyEventAsync(new OnNpcCreatureSee((Npc) wo, (Creature) object, object.isSummon()), (Npc) wo);
				}
				if (object.isNpc() && wo.isCreature())
				{
					EventDispatcher.getInstance().notifyEventAsync(new OnNpcCreatureSee((Npc) object, (Creature) wo, wo.isSummon()), (Npc) object);
				}
			}
		}
		final WorldRegion[] oldSurroundingRegions = oldRegion.getSurroundingRegions();
		for (int i = 0; i < oldSurroundingRegions.length; i++)
		{
			final WorldRegion worldRegion = oldSurroundingRegions[i];
			if (newRegion.isSurroundingRegion(worldRegion))
			{
				continue;
			}
			final List<WorldObject> visibleObjects = worldRegion.getVisibleObjects();
			for (int j = 0; j < visibleObjects.size(); j++)
			{
				final WorldObject wo = visibleObjects.get(j);
				if ((wo == null) || (wo == object) || (isInsideToi && (wo.calculateDistanceZ(object) > Config.TOI_MAX_Z)))
				{
					continue;
				}
				if (object.isCreature())
				{
					final Creature objectCreature = (Creature) object;
					final CreatureAI ai = objectCreature.getAI();
					if (ai != null)
					{
						ai.notifyEvent(CtrlEvent.EVT_FORGET_OBJECT, wo);
					}
					if (objectCreature.getTarget() == wo)
					{
						objectCreature.setTarget(null);
					}
					if (object.isPlayer())
					{
						object.sendPacket(new DeleteObject(wo));
					}
				}
				if (wo.isCreature())
				{
					final Creature woCreature = (Creature) wo;
					final CreatureAI ai = woCreature.getAI();
					if (ai != null)
					{
						ai.notifyEvent(CtrlEvent.EVT_FORGET_OBJECT, object);
					}
					if (woCreature.getTarget() == object)
					{
						woCreature.setTarget(null);
					}
					if (wo.isPlayer())
					{
						wo.sendPacket(new DeleteObject(object));
					}
				}
			}
		}
	}
	
	public <T extends WorldObject> List<T> getVisibleObjects(WorldObject object, Class<T> clazz)
	{
		final List<T> result = new ArrayList<>();
		forEachVisibleObject(object, clazz, result::add);
		return result;
	}
	
	public <T extends WorldObject> List<T> getVisibleObjects(WorldObject object, Class<T> clazz, Predicate<T> predicate)
	{
		final List<T> result = new ArrayList<>();
		forEachVisibleObject(object, clazz, o ->
		{
			if (predicate.test(o))
			{
				result.add(o);
			}
		});
		return result;
	}
	
	public <T extends WorldObject> void forEachVisibleObject(WorldObject object, Class<T> clazz, Consumer<T> c)
	{
		if (object == null)
		{
			return;
		}
		final WorldRegion worldRegion = getRegion(object);
		if (worldRegion == null)
		{
			return;
		}
		boolean isInsideToi = Config.TOI_MAX_Z_ENABLED ? object.isInsideZone(ZoneId.Z_Load) : false;
		final WorldRegion[] surroundingRegions = worldRegion.getSurroundingRegions();
		for (int i = 0; i < surroundingRegions.length; i++)
		{
			final List<WorldObject> visibleObjects = surroundingRegions[i].getVisibleObjects();
			for (int j = 0; j < visibleObjects.size(); j++)
			{
				final WorldObject wo = visibleObjects.get(j);
				if ((wo == null) || (wo == object) || !clazz.isInstance(wo) || (isInsideToi && (wo.calculateDistanceZ(object) > Config.TOI_MAX_Z)))
				{
					continue;
				}
				if (wo.getInstanceWorld() != object.getInstanceWorld())
				{
					continue;
				}
				final int x = ((object.getX() - World.MAP_MIN_X) >> 15) + World.TILE_X_MIN;
				final int y = ((object.getY() - World.MAP_MIN_Y) >> 15) + World.TILE_Y_MIN;
				int z_limit = (x == 23) && (y == 18) ? 900 : 2000;
				if (Math.abs(object.getZ() - wo.getZ()) > z_limit)
				{
					continue;
				}
				c.accept(clazz.cast(wo));
			}
		}
	}
	
	public <T extends WorldObject> List<T> getVisibleObjectsInRange(WorldObject object, Class<T> clazz, int range)
	{
		final List<T> result = new ArrayList<>();
		forEachVisibleObjectInRange(object, clazz, range, result::add);
		return result;
	}
	
	public <T extends WorldObject> List<T> getVisibleObjectsInRange(WorldObject object, Class<T> clazz, int range, Predicate<T> predicate)
	{
		final List<T> result = new ArrayList<>();
		forEachVisibleObjectInRange(object, clazz, range, o ->
		{
			if (predicate.test(o))
			{
				result.add(o);
			}
		});
		return result;
	}
	
	public <T extends WorldObject> void forEachVisibleObjectInRange(WorldObject object, Class<T> clazz, int range, Consumer<T> c)
	{
		if (object == null)
		{
			return;
		}
		final WorldRegion worldRegion = getRegion(object);
		if (worldRegion == null)
		{
			return;
		}
		final WorldRegion[] surroundingRegions = worldRegion.getSurroundingRegions();
		for (int i = 0; i < surroundingRegions.length; i++)
		{
			final List<WorldObject> visibleObjects = surroundingRegions[i].getVisibleObjects();
			for (int j = 0; j < visibleObjects.size(); j++)
			{
				final WorldObject wo = visibleObjects.get(j);
				if ((wo == null) || (wo == object) || !clazz.isInstance(wo))
				{
					continue;
				}
				if (wo.getInstanceWorld() != object.getInstanceWorld())
				{
					continue;
				}
				final int x = ((object.getX() - World.MAP_MIN_X) >> 15) + World.TILE_X_MIN;
				final int y = ((object.getY() - World.MAP_MIN_Y) >> 15) + World.TILE_Y_MIN;
				int z_limit = (x == 23) && (y == 18) ? 900 : 2000;
				if (Math.abs(object.getZ() - wo.getZ()) > z_limit)
				{
					continue;
				}
				if (wo.calculateDistance3D(object) <= range)
				{
					c.accept(clazz.cast(wo));
				}
			}
		}
	}
	
	/**
	 * Calculate the current WorldRegions of the object according to its position (x,y). <b><u>Example of use</u>:</b>
	 * <li>Set position of a new WorldObject (drop, spawn...)</li>
	 * <li>Update position of a WorldObject after a movement</li><br>
	 *
	 * @param object
	 *            the object
	 * @return
	 */
	public WorldRegion getRegion(WorldObject object)
	{
		try
		{
			return _worldRegions[(object.getX() >> SHIFT_BY) + OFFSET_X][(object.getY() >> SHIFT_BY) + OFFSET_Y];
		}
		catch (ArrayIndexOutOfBoundsException e) // Precaution. Moved at invalid region?
		{
			disposeOutOfBoundsObject(object);
			return null;
		}
	}
	
	public WorldRegion getRegion(int x, int y)
	{
		try
		{
			return _worldRegions[(x >> SHIFT_BY) + OFFSET_X][(y >> SHIFT_BY) + OFFSET_Y];
		}
		catch (ArrayIndexOutOfBoundsException e)
		{
			LOGGER.warning(getClass().getSimpleName() + ": Incorrect world region X: " + ((x >> SHIFT_BY) + OFFSET_X) + " Y: " + ((y >> SHIFT_BY) + OFFSET_Y));
			return null;
		}
	}
	
	/**
	 * Returns the whole 3d array containing the world regions used by ZoneData.java to setup zones inside the world regions
	 *
	 * @return
	 */
	public WorldRegion[][] getWorldRegions()
	{
		return _worldRegions;
	}
	
	/**
	 * Get all NPCs in the game.
	 *
	 * @return a collection of all NPCs
	 */
	public Collection<Npc> getAllNpcs()
	{
		return _allObjects.values().stream().filter(obj -> obj instanceof Npc).map(obj -> (Npc) obj).collect(Collectors.toList());
	}
	
	public synchronized void disposeOutOfBoundsObject(WorldObject object)
	{
		if (object.isPlayer())
		{
			((Creature) object).stopMove(((PlayerInstance) object).getLastServerPosition());
		}
		else if (object.isSummon())
		{
			final Summon summon = (Summon) object;
			summon.unSummon(summon.getOwner());
		}
		else if (_allObjects.remove(object.getObjectId()) != null)
		{
			if (object.isNpc())
			{
				final Npc npc = (Npc) object;
				LOGGER.warning("Deleting npc " + object.getName() + " NPCID[" + npc.getId() + "] from invalid location X:" + object.getX() + " Y:" + object.getY() + " Z:" + object.getZ());
				npc.deleteMe();
				final Spawn spawn = npc.getSpawn();
				if (spawn != null)
				{
					LOGGER.warning("Spawn location X:" + spawn.getX() + " Y:" + spawn.getY() + " Z:" + spawn.getZ() + " Heading:" + spawn.getHeading());
				}
			}
			else if (object.isCreature())
			{
				LOGGER.warning("Deleting object " + object.getName() + " OID[" + object.getObjectId() + "] from invalid location X:" + object.getX() + " Y:" + object.getY() + " Z:" + object.getZ());
				((Creature) object).deleteMe();
			}
			if (object.getWorldRegion() != null)
			{
				object.getWorldRegion().removeVisibleObject(object);
			}
		}
	}
	
	public void incrementParty()
	{
		_partyNumber.incrementAndGet();
	}
	
	public void decrementParty()
	{
		_partyNumber.decrementAndGet();
	}
	
	public void incrementPartyMember()
	{
		_memberInPartyNumber.incrementAndGet();
	}
	
	public void decrementPartyMember()
	{
		_memberInPartyNumber.decrementAndGet();
	}
	
	public int getPartyCount()
	{
		return _partyNumber.get();
	}
	
	public int getPartyMemberCount()
	{
		return _memberInPartyNumber.get();
	}
	
	public synchronized void addPkPlayer(PlayerInstance player)
	{
		_pkPlayers.add(player);
		_lastPkTime.set((int) System.currentTimeMillis() / 1000);
	}
	
	public void removePkPlayer(PlayerInstance player)
	{
		_pkPlayers.remove(player);
		_lastPkTime.set((int) System.currentTimeMillis() / 1000);
	}
	
	public Set<PlayerInstance> getPkPlayers()
	{
		return _pkPlayers;
	}
	
	public int getLastPkTime()
	{
		return _lastPkTime.get();
	}
	
	public List<Npc> getAllByNpcId(int npcId)
	{
		List<Npc> npcs = new LinkedList<>();
		for (WorldObject npc : World.getInstance().getVisibleObjects())
		{
			if (npc.getId() == npcId)
			{
				npcs.add((Npc) npc);
			}
		}
		return npcs;
	}
	
	/**
	 * Get all players around a specific player within the visible range.
	 *
	 * @param pc
	 *            the player to check around
	 * @return a list of PlayerInstance around the given player
	 */
	public List<PlayerInstance> getAroundPlayers(PlayerInstance pc)
	{
		return getVisibleObjects(pc, PlayerInstance.class);
	}
	
	private static class SingletonHolder
	{
		protected static final World INSTANCE = new World();
	}
}
