/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.mentoring;

import club.projectessence.Config;
import club.projectessence.gameserver.data.sql.CharNameTable;
import club.projectessence.gameserver.instancemanager.MentorManager;
import club.projectessence.gameserver.model.Mentee;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerMenteeLeft;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerMenteeRemove;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * <AUTHOR>
 */
public class RequestMentorCancel extends ClientPacket {
	private int _confirmed;
	private String _name;

	@Override
	public void readImpl() {
		_confirmed = readInt();
		_name = readString();
	}

	@Override
	public void runImpl() {
		if (_confirmed != 1) {
			return;
		}

		final PlayerInstance player = client.getPlayer();
		final int objectId = CharNameTable.getInstance().getIdByName(_name);
		if (player != null) {
			if (player.isMentor()) {
				final Mentee mentee = MentorManager.getInstance().getMentee(player.getObjectId(), objectId);
				if (mentee != null) {
					MentorManager.getInstance().cancelAllMentoringBuffs(mentee.getPlayerInstance());

					if (MentorManager.getInstance().isAllMenteesOffline(player.getObjectId(), mentee.getObjectId())) {
						MentorManager.getInstance().cancelAllMentoringBuffs(player);
					}

					player.sendPacket(new SystemMessage(SystemMessageId.S1_S_MENTORING_CONTRACT_IS_CANCELLED_THE_MENTOR_CANNOT_BOND_WITH_ANOTHER_MENTEE_FOR_2_DAYS).addString(_name));
					MentorManager.getInstance().setPenalty(player.getObjectId(), Config.MENTOR_PENALTY_FOR_MENTEE_LEAVE);
					MentorManager.getInstance().deleteMentor(player.getObjectId(), mentee.getObjectId());

					// Notify to scripts
					EventDispatcher.getInstance().notifyEventAsync(new OnPlayerMenteeRemove(player, mentee), player);
				}
			} else if (player.isMentee()) {
				final Mentee mentor = MentorManager.getInstance().getMentor(player.getObjectId());
				if ((mentor != null) && (mentor.getObjectId() == objectId)) {
					MentorManager.getInstance().cancelAllMentoringBuffs(player);

					if (MentorManager.getInstance().isAllMenteesOffline(mentor.getObjectId(), player.getObjectId())) {
						MentorManager.getInstance().cancelAllMentoringBuffs(mentor.getPlayerInstance());
					}

					MentorManager.getInstance().setPenalty(mentor.getObjectId(), Config.MENTOR_PENALTY_FOR_MENTEE_LEAVE);
					MentorManager.getInstance().deleteMentor(mentor.getObjectId(), player.getObjectId());

					// Notify to scripts
					EventDispatcher.getInstance().notifyEventAsync(new OnPlayerMenteeLeft(mentor, player), player);
					mentor.getPlayerInstance().sendPacket(new SystemMessage(SystemMessageId.S1_S_MENTORING_CONTRACT_IS_CANCELLED_THE_MENTOR_CANNOT_BOND_WITH_ANOTHER_MENTEE_FOR_2_DAYS).addString(_name));
				}
			}
		}
	}
}
