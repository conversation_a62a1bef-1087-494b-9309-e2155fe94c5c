/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.data.xml.SkillTreeData;
import club.projectessence.gameserver.enums.AcquireSkillType;
import club.projectessence.gameserver.enums.Race;
import club.projectessence.gameserver.model.SkillLearn;
import club.projectessence.gameserver.model.actor.Npc;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.ClanPrivilege;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.AcquireSkillInfo;
import club.projectessence.gameserver.network.serverpackets.ExAcquireSkillInfo;

import java.util.Map.Entry;

/**
 * Request Acquire Skill Info client packet implementation.
 *
 * <AUTHOR>
 */
public class RequestAcquireSkillInfo extends ClientPacket {
	private int _id;
	private int _level;
	private AcquireSkillType _skillType;

	@Override
	public void readImpl() {
		_id = readInt();
		_level = readInt();
		_skillType = AcquireSkillType.getAcquireSkillType(readInt());
	}

	@Override
	public void runImpl() {
		if ((_id <= 0) || (_level <= 0)) {
			LOGGER.warning(RequestAcquireSkillInfo.class.getSimpleName() + ": Invalid Id: " + _id + " or level: " + _level + "!");
			return;
		}

		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}

		final Npc trainer = player.getLastFolkNPC();
		if ((_skillType != AcquireSkillType.CLASS) && ((trainer == null) || !trainer.isNpc() || (!trainer.canInteract(player) && !player.isGM()))) {
			return;
		}

		final Skill skill = SkillData.getInstance().getSkill(_id, _level);
		if (skill == null) {
			LOGGER.warning("Skill Id: " + _id + " level: " + _level + " is undefined. " + RequestAcquireSkillInfo.class.getName() + " failed.");
			return;
		}

		SkillLearn s = SkillTreeData.getInstance().getSkillLearn(_skillType, _id, _level, player);
		if (s == null) {
			if (player.getReplacedSkills().containsValue(_id)) {
				for (Entry<Integer, Integer> e : player.getReplacedSkills().entrySet()) {
					if (e.getValue() == _id) {
						s = SkillTreeData.getInstance().getSkillLearn(_skillType, e.getKey(), _level, player);
					}
					break;
				}
			} else {
				return;
			}
		}

		switch (_skillType) {
			case TRANSFORM:
			case FISHING:
			case SUBCLASS:
			case COLLECT:
			case TRANSFER:
			case DUALCLASS: {
				client.sendPacket(new AcquireSkillInfo(_skillType, s));
				break;
			}
			case CLASS: {
				client.sendPacket(new ExAcquireSkillInfo(player, s));
				break;
			}
			case PLEDGE: {
				if (!player.isClanLeader()) {
					return;
				}
				client.sendPacket(new AcquireSkillInfo(_skillType, s));
				break;
			}
			case SUBPLEDGE: {
				if (!player.isClanLeader() || !player.hasClanPrivilege(ClanPrivilege.CL_TROOPS_FAME)) {
					return;
				}
				client.sendPacket(new AcquireSkillInfo(_skillType, s));
				break;
			}
			case ALCHEMY: {
				if (player.getRace() != Race.ERTHEIA) {
					return;
				}
				client.sendPacket(new AcquireSkillInfo(_skillType, s));
				break;
			}
			case REVELATION: {
				/*
				 * if ((player.getLevel() < 85) || !player.isInCategory(CategoryType.SIXTH_CLASS_GROUP)) { return; } client.sendPacket(new AcquireSkillInfo(_skillType, s));
				 */
				return;
			}
			case REVELATION_DUALCLASS: {
				/*
				 * if (!player.isSubClassActive() || !player.isDualClassActive()) { return; } client.sendPacket(new AcquireSkillInfo(_skillType, s));
				 */
				return;
			}
		}
	}
}
