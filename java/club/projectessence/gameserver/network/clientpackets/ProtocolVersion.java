/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.network.serverpackets.KeyPacket;

import java.util.logging.Logger;

/**
 * @version $Revision: 1.5.2.8.2.8 $ $Date: 2005/04/02 10:43:04 $
 */
public class ProtocolVersion extends ClientPacket {
	private static final Logger LOGGER_ACCOUNTING = Logger.getLogger("accounting");

	private int _version;
	// TODO[K] - Guard section start
	// private byte[] data;
	// private int dataChecksum;
	// TODO[K] - Guard section end

	@Override
	public void readImpl() {
		_version = readInt();
		// TODO[K] - Guard section start
		// if (StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// try
		// {
		// if (available() >= StrixPlatform.getInstance().getProtocolVersionDataSize())
		// {
		// data = new byte[StrixPlatform.getInstance().getClientDataSize()];
		// readBytes(data);
		// dataChecksum = readInt();
		// }
		// }
		// catch (final Exception e)
		// {
		// Log.error("Client [IP=" + client.getIpAddr() + "] used unprotected client. Disconnect...");
		// client.close(new KeyPacket(null, 0));
		// return;
		// }
		// }
		// TODO[K] - Guard section end
	}

	@Override
	public void runImpl() {
		// this packet is never encrypted
		if (_version == -2) {
			// this is just a ping attempt from the new C2 client
			client.close();
		} else if (!Config.PROTOCOL_LIST.contains(_version)) {
			LOGGER_ACCOUNTING.warning("Wrong protocol version " + _version + ", " + client);
			client.setProtocolOk(false);
			client.close(new KeyPacket(client.enableCrypt(), 0));
		}
		// TODO[K] - Strix section start
		// else if (StrixPlatform.getInstance().isPlatformEnabled())
		// {
		// if (data == null)
		// {
		// Log.info("Client [IP=%s] used unprotected client. Disconnect..." + client.getIpAddr());
		// client.close(new KeyPacket(null, 0));
		// return;
		// }
		// final StrixClientData clientData = ClientProtocolDataManager.getInstance().getDecodedData(data, dataChecksum);
		// if (clientData != null)
		// {
		// if (!ClientGameSessionManager.getInstance().checkServerResponse(clientData))
		// {
		// client.close(new KeyPacket(null, clientData));
		// return;
		// }
		// client.setStrixClientData(clientData);
		// client.setRevision(_version);
		// client.sendPacket(new KeyPacket(client.enableCrypt(), 1));
		// client.setProtocolOk(true);
		// return;
		// }
		// Log.info("Decode client data failed. See Strix-Platform log file. Disconected client " + client.getIpAddr());
		// client.close(new KeyPacket(null, 0));
		// return;
		// }
		// TODO[K] - Strix section end
		else {
			client.sendPacket(new KeyPacket(client.enableCrypt(), 1));
			client.setProtocolVersion(_version);
			client.setProtocolOk(true);
		}
	}
}
