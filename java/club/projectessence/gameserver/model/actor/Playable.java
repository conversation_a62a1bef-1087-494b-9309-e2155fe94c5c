/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.actor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import club.projectessence.Config;
import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.enums.ClanWarState;
import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.enums.InstanceType;
import club.projectessence.gameserver.instancemanager.ZoneManager;
import club.projectessence.gameserver.model.DamageList;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.stat.PlayableStat;
import club.projectessence.gameserver.model.actor.status.PlayableStatus;
import club.projectessence.gameserver.model.actor.templates.CreatureTemplate;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanWar;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.OnCreatureDeath;
import club.projectessence.gameserver.model.events.returns.TerminateReturn;
import club.projectessence.gameserver.model.instancezone.Instance;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.quest.QuestState;
import club.projectessence.gameserver.model.skills.CommonSkill;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.network.serverpackets.EtcStatusUpdate;
import gabriel.eventEngine.interf.GabrielEvents;

/**
 * This class represents all Playable characters in the world.<br>
 * Playable:
 * <ul>
 * <li>PlayerInstance</li>
 * <li>Summon</li>
 * </ul>
 */
public abstract class Playable extends Creature
{
	private Creature		_lockedTarget		= null;
	private PlayerInstance	_transferDmgTo		= null;
	private int				_transferDmgRange	= -1;
	
	/**
	 * Constructor of Playable.<br>
	 * <br>
	 * <b><u>Actions</u>:</b>
	 * <ul>
	 * <li>Call the Creature constructor to create an empty _skills slot and link copy basic Calculator set to this Playable</li>
	 * </ul>
	 *
	 * @param objectId
	 *            the object id
	 * @param template
	 *            The CreatureTemplate to apply to the Playable
	 */
	public Playable(int objectId, CreatureTemplate template)
	{
		super(objectId, template);
		setInstanceType(InstanceType.Playable);
		setInvul(false);
	}
	
	public Playable(CreatureTemplate template)
	{
		super(template);
		setInstanceType(InstanceType.Playable);
		setInvul(false);
	}
	
	@Override
	public PlayableStat getStat()
	{
		return (PlayableStat) super.getStat();
	}
	
	@Override
	public void initCharStat()
	{
		setStat(new PlayableStat(this));
	}
	
	@Override
	public PlayableStatus getStatus()
	{
		return (PlayableStatus) super.getStatus();
	}
	
	@Override
	public void initCharStatus()
	{
		setStatus(new PlayableStatus(this));
	}
	
	@Override
	public boolean doDie(Creature killer)
	{
		final TerminateReturn returnBack = EventDispatcher.getInstance().notifyEvent(new OnCreatureDeath(killer, this), this, TerminateReturn.class);
		if ((returnBack != null) && returnBack.terminate())
		{
			return false;
		}
		// killing is only possible one time
		synchronized (this)
		{
			if (isDead())
			{
				return false;
			}
			// now reset currentHp to zero
			setCurrentHp(0);
			setDead(true);
		}
		abortAttack();
		abortCast();
		// Set target to null and cancel Attack or Cast
		setTarget(null);
		// Stop movement
		stopMove(null);
		// Stop HP/MP/CP Regeneration task
		getStatus().stopHpMpRegeneration();
		// Lấy debuffMap trước khi xóa effect và tối ưu hóa việc lọc debuffers
		List<PlayerInstance> debuffers = new ArrayList<>();
		if (this instanceof Playable)
		{
			long currentTime = System.currentTimeMillis();
			Map<PlayerInstance, Long> debuffMap = ((Playable) this).getDebuffList();
			// System.out.println("Debuff Map for target " + getName() + ": " + debuffMap);
			if (debuffMap != null && !debuffMap.isEmpty())
			{
				// Tối ưu: Chỉ giữ thời gian gần nhất cho mỗi PlayerInstance
				Map<PlayerInstance, Long> latestDebuffMap = debuffMap.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, // Key là PlayerInstance
				Map.Entry::getValue, // Value là thời gian
				(oldValue, newValue) -> newValue > oldValue ? newValue : oldValue // Giữ thời gian lớn nhất (gần nhất)
				));
				// Lọc các debuffer trong vòng 60 giây và tạo danh sách
				debuffers = latestDebuffMap.entrySet().stream().filter(entry -> currentTime - entry.getValue() <= TimeUnit.SECONDS.toMillis(60)).map(Map.Entry::getKey).collect(Collectors.toList());
			}
		}
		// System.out.println("Debuffers before filtering: " + debuffers);
		boolean deleteBuffs = true;
		if (isNoblesseBlessedAffected())
		{
			stopEffects(EffectFlag.NOBLESS_BLESSING);
			deleteBuffs = false;
		}
		if (isResurrectSpecialAffected())
		{
			stopEffects(EffectFlag.RESURRECTION_SPECIAL);
			deleteBuffs = false;
		}
		if (isPlayer())
		{
			final PlayerInstance player = getActingPlayer();
			if (player.hasCharmOfCourage())
			{
				if (player.isInSiege())
				{
					getActingPlayer().reviveRequest(getActingPlayer(), false, 0, 0, 0, 0);
				}
				player.setCharmOfCourage(false);
				player.sendPacket(new EtcStatusUpdate(player));
			}
		}
		if ((killer != null) && killer.isPlayable())
		{
			final PlayerInstance killerPlayer = killer.getActingPlayer();
			if (isAffected(EffectFlag.STIGMA_OF_DEATH) && killerPlayer.isDeathKnight())
			{
				switch (killerPlayer.getAffectedSkillLevel(45336))
				{
					case 0 -> CommonSkill.SOUL_STEAL_1.getSkill().applyEffects(killerPlayer, killerPlayer);
					case 1 -> CommonSkill.SOUL_STEAL_2.getSkill().applyEffects(killerPlayer, killerPlayer);
					case 2 -> CommonSkill.SOUL_STEAL_3.getSkill().applyEffects(killerPlayer, killerPlayer);
					case 3 -> CommonSkill.SOUL_STEAL_4.getSkill().applyEffects(killerPlayer, killerPlayer);
					case 4 -> CommonSkill.SOUL_STEAL_5.getSkill().applyEffects(killerPlayer, killerPlayer);
					case 5 -> CommonSkill.SOUL_STEAL_5.getSkill().applyEffects(killerPlayer, killerPlayer);
				}
			}
		}
		if (deleteBuffs)
		{
			stopAllEffectsExceptThoseThatLastThroughDeath();
		}
		// Send the Server->Client packet StatusUpdate with current HP and MP to all other PlayerInstance to inform
		broadcastStatusUpdate();
		ZoneManager.getInstance().getRegion(this).onDeath(this);
		// Notify Quest of Playable's death
		final PlayerInstance actingPlayer = getActingPlayer();
		if (!actingPlayer.isNotifyQuestOfDeathEmpty())
		{
			for (QuestState qs : actingPlayer.getNotifyQuestOfDeath())
			{
				qs.getQuest().notifyDeath((killer == null ? this : killer), this, qs);
			}
		}
		// Notify instance
		if (isPlayer())
		{
			final Instance instance = getInstanceWorld();
			if (instance != null && instance.getTemplateId() != 0)
			{
				instance.onDeath(getActingPlayer());
			}
		}
		if (killer != null)
		{
			final PlayerInstance killerPlayer = killer.getActingPlayer();
			if ((killerPlayer != null) && isPlayable())
			{
				killerPlayer.onPlayerKill(this, debuffers);
			}
		}
		// Notify Creature AI
		if (isPlayer() && killer.isPlayable())
		{
			getActingPlayer().setLastDeathByPlayer(true);
		}
		getAI().notifyEvent(CtrlEvent.EVT_DEAD);

		// Check for auto-resurrection in autoplay with enhanced safety checks
		// Include offline players in auto-resurrection
		if (isPlayer())
		{
			final PlayerInstance player = getActingPlayer();
			try
			{
				if (player != null && player.isOnline())
				{
					// For online players: check if they've been online for at least 10 seconds
					// For offline players: skip this check as they don't have getClient()
					boolean canProceed = false;
					if (player.isInOfflineMode())
					{
						canProceed = true; // Offline players can always proceed
					}
					else if (player.getClient() != null && System.currentTimeMillis() - player.getOnlineTime() > 10000)
					{
						canProceed = true; // Online players after 10 seconds
					}

					if (canProceed && club.projectessence.gameserver.instancemanager.AutoResurrectionManager.canAutoResurrect(player))
					{
						// Schedule auto-resurrection after a longer delay to ensure stability
						club.projectessence.commons.concurrent.ThreadPool.schedule(() -> {
							try
							{
								if (player.isOnline() && player.isDead())
								{
									club.projectessence.gameserver.instancemanager.AutoResurrectionManager.performAutoResurrection(player);
								}
							}
							catch (Exception e)
							{
								// Ignore exceptions to prevent crashes
							}
						}, 3000); // 3 second delay for extra safety
					}
				}
			}
			catch (Exception e)
			{
				// Ignore exceptions to prevent crashes
			}
		}

		return true;
	}
	
	public boolean checkIfPvP(PlayerInstance target)
	{
		final PlayerInstance player = getActingPlayer();
		if ((player == null) //
		|| (target == null) //
		|| (player == target) //
		|| (target.getReputation() < 0) //
		|| (target.getPvpFlag() > 0) //
		|| target.isOnDarkSide())
		{
			return true;
		}

		// Anti-cheat checks - don't count as PvP if any of these conditions are met
		// GM characters still participate in PvP system

		// HWID check to prevent self-kill farming (same computer)
		// Skip HWID check if either player is GM (for testing purposes)
		if (Config.HARDWARE_INFO_ENABLED && !player.isGM() && !target.isGM() && player.getClient() != null && target.getClient() != null)
		{
			final String playerHWID = player.getClient().getHardwareInfo() != null ? player.getClient().getHardwareInfo().getMacAddress() : "";
			final String targetHWID = target.getClient().getHardwareInfo() != null ? target.getClient().getHardwareInfo().getMacAddress() : "";
			if (!playerHWID.isEmpty() && !targetHWID.isEmpty() && playerHWID.equals(targetHWID))
			{
				return false; // Same HWID - don't count as PvP
			}
		}

		// Don't count party members as PvP
		if (player.isInParty() && player.getParty().containsPlayer(target))
		{
			return false;
		}

		// Don't count event participants as regular PvP
		if (GabrielEvents.isInEvent(target))
		{
			return false;
		}

		// Check clan war
		final Clan playerClan = player.getClan();
		if ((playerClan != null) && !player.isAcademyMember() && !target.isAcademyMember())
		{
			final ClanWar war = playerClan.getWarWith(target.getClanId());
			return (war != null) && (war.getState() == ClanWarState.MUTUAL);
		}

		// Check if players are from different factions - always count as PvP
		if (Config.FACTION_SYSTEM_ENABLED && player.getFaction() != Faction.NONE && target.getFaction() != Faction.NONE)
		{
			return player.getFaction() != target.getFaction();
		}

		return false;
	}
	
	/**
	 * Return True.
	 */
	@Override
	public boolean canBeAttacked()
	{
		return true;
	}
	
	// Support for Noblesse Blessing skill, where buffs are retained after resurrect
	public boolean isNoblesseBlessedAffected()
	{
		return isAffected(EffectFlag.NOBLESS_BLESSING);
	}
	
	/**
	 * @return {@code true} if char can resurrect by himself, {@code false} otherwise
	 */
	public boolean isResurrectSpecialAffected()
	{
		return isAffected(EffectFlag.RESURRECTION_SPECIAL);
	}
	
	/**
	 * @return {@code true} if the Silent Moving mode is active, {@code false} otherwise
	 */
	public boolean isSilentMovingAffected()
	{
		return isAffected(EffectFlag.SILENT_MOVE);
	}
	
	/**
	 * For Newbie Protection Blessing skill, keeps you safe from an attack by a chaotic character >= 10 levels apart from you.
	 *
	 * @return
	 */
	public boolean isProtectionBlessingAffected()
	{
		return isAffected(EffectFlag.PROTECTION_BLESSING);
	}
	
	@Override
	public void updateEffectIcons(boolean partyOnly)
	{
		getEffectList().updateEffectIcons(partyOnly);
	}
	
	public boolean isLockedTarget()
	{
		return _lockedTarget != null;
	}
	
	public Creature getLockedTarget()
	{
		return _lockedTarget;
	}
	
	public void setLockedTarget(Creature creature)
	{
		_lockedTarget = creature;
	}
	
	public void setTransferDamageTo(PlayerInstance val)
	{
		_transferDmgTo = val;
	}
	
	public PlayerInstance getTransferingDamageTo()
	{
		return _transferDmgTo;
	}
	
	public void setTransferDamageRange(int val)
	{
		_transferDmgRange = val;
	}
	
	public int getTransferingDamageRange()
	{
		return _transferDmgRange;
	}
	
	public int getRelation(PlayerInstance target)
	{
		PlayerInstance player = this.getActingPlayer();
		if (player != null)
		{
			return player.getRelation(target);
		}
		return 0;
	}
	
	public abstract void doPickupItem(WorldObject object);
	
	public abstract boolean useMagic(Skill skill, ItemInstance item, boolean forceUse, boolean dontMove, boolean showSystemMessage);
	
	public abstract void storeMe();
	
	public abstract void storeEffect(boolean storeEffects);
	
	public abstract void restoreEffects();
	
	@Override
	public boolean isPlayable()
	{
		return true;
	}
	
	public double getEventExpBonus()
	{
		if (!Config.EXP_EVENT_ENABLED)
		{
			return 1;
		}
		final int level = getLevel();
		double exp = 1;
		if (level > 86)
		{
			exp = 1;
		}
		else
		{
			exp = 4;
		}
		// if (getLevel() > 82)
		// {
		// exp = 1;
		// }
		// else if (getLevel() >= 76)
		// {
		// exp = 2;
		// }
		// else if (getLevel() >= 61)
		// {
		// exp = 2.5;
		// }
		// else
		// {
		// exp = 3;
		// }
		// if (isPet())
		// {
		// return exp * 2;
		// }
		return exp;
	}
	
	public abstract DamageList getDamageList();
	
	public abstract void addDamage(Playable attacker, long damage);
	
	public abstract void clearDamageAndDebuffList();
	
	public abstract Map<PlayerInstance, Long> getDebuffList();
}
