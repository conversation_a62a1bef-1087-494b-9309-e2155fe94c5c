# Test Auto-Resurrection System

## <PERSON><PERSON> tả thay đổi

Đã sửa đổi hệ thống auto-resurrection để tự động chọn hồi sinh bằng L-coin mà không hiển thị dialog lựa chọn khi người chơi AFK.

## Các file đã sửa đổi

### 1. Die.java
- **Vị trí**: `java/club/projectessence/gameserver/network/serverpackets/Die.java`
- **Thay đổi**: 
  - Thêm kiểm tra `AutoResurrectionManager.canAutoResurrect()` trong constructor
  - Khi auto-resurrection có thể sử dụng, set `_flags = 0` để ẩn tất cả lựa chọn hồi sinh
  - Trong `writeImpl()`, gửi dữ liệu tối thiểu (tất cả = 0) để ngăn dialog hiển thị

### 2. AutoResurrectionManager.java
- **Vị trí**: `java/club/projectessence/gameserver/instancemanager/AutoResurrectionManager.java`
- **Thay đổi**:
  - Thêm method `forceResurrectionWithLCoin()` để thực hiện hồi sinh trực tiếp
  - Thêm import `EtcStatusUpdate`
  - Sử dụng `player.doRevive(power)` để hồi sinh với % phục hồi chính xác

## Cách hoạt động

1. **Khi người chơi chết**:
   - `Playable.doDie()` được gọi
   - Kiểm tra `AutoResurrectionManager.canAutoResurrect()`
   - Nếu có thể auto-resurrect, lên lịch thực hiện sau 2 giây

2. **Khi gửi packet Die**:
   - `Die.java` kiểm tra auto-resurrection
   - Nếu có thể sử dụng, set flags = 0 và gửi dữ liệu tối thiểu
   - Client không hiển thị dialog hồi sinh

3. **Khi auto-resurrection thực hiện**:
   - Tính toán chi phí L-coin
   - Trừ L-coin từ inventory
   - Gọi `forceResurrectionWithLCoin()` để hồi sinh trực tiếp
   - Teleport về thành phố
   - Lên lịch quay lại vị trí hunt sau 5 phút

## Test Cases

### Test 1: Auto-resurrection bật và đủ L-coin
1. Bật auto-resurrection trong settings
2. Đảm bảo có đủ L-coin
3. Để character bị giết
4. **Kết quả mong đợi**: Không hiển thị dialog, tự động hồi sinh và về thành phố

### Test 2: Auto-resurrection bật nhưng không đủ L-coin
1. Bật auto-resurrection trong settings
2. Đảm bảo không đủ L-coin
3. Để character bị giết
4. **Kết quả mong đợi**: Hiển thị dialog hồi sinh bình thường

### Test 3: Auto-resurrection tắt
1. Tắt auto-resurrection trong settings
2. Để character bị giết
3. **Kết quả mong đợi**: Hiển thị dialog hồi sinh bình thường

### Test 4: Trong forbidden zone
1. Bật auto-resurrection
2. Vào PvP zone hoặc siege zone
3. Để character bị giết
4. **Kết quả mong đợi**: Hiển thị dialog hồi sinh bình thường

## Lợi ích

1. **Không gián đoạn AFK**: Người chơi AFK không cần can thiệp
2. **Tự động chọn L-coin**: Luôn sử dụng L-coin thay vì adena
3. **Mượt mà hơn**: Không có dialog làm gián đoạn
4. **An toàn**: Vẫn có các kiểm tra an toàn và giới hạn

## Cấu hình liên quan

Các config trong `Config.java` ảnh hưởng đến tính năng:
- `AUTOPLAY_AUTO_RESURRECTION_ENABLED`
- `AUTOPLAY_AUTO_RESURRECTION_COOLDOWN`
- `AUTOPLAY_AUTO_RESURRECTION_MAX_USES_PER_DAY`
- `AUTOPLAY_AUTO_RESURRECTION_RETURN_DELAY`
