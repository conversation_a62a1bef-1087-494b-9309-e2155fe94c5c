/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.olympiad;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.olympiad.Hero;
import club.projectessence.gameserver.model.olympiad.Olympiad;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

import java.util.Map;

/**
 * <AUTHOR> Ken<PERSON>, godson
 */
public class ExHeroList extends ServerPacket {
	private final Map<Integer, StatSet> _heroList;

	public ExHeroList() {
		_heroList = Hero.getInstance().getHeroes();
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_HERO_LIST, buffer);

		buffer.writeInt(_heroList.size());
		for (StatSet hero : _heroList.values()) {
			buffer.writeString(hero.getString(Olympiad.CHAR_NAME));
			buffer.writeInt(hero.getInt(Olympiad.CLASS_ID));
			buffer.writeString(hero.getString(Hero.CLAN_NAME, ""));
			buffer.writeInt(0x00); // hero.getInt(Hero.CLAN_CREST, 0)
			buffer.writeString(hero.getString(Hero.ALLY_NAME, ""));
			buffer.writeInt(0x00); // hero.getInt(Hero.ALLY_CREST, 0)
			buffer.writeInt(hero.getInt(Hero.COUNT));
			buffer.writeInt(0x00);
			buffer.writeByte(0x00); // 272
		}
	}
}
