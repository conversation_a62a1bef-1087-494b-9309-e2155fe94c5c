/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.itemcontainer;

import club.projectessence.Config;
import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.enums.ItemLocation;
import club.projectessence.gameserver.enums.MailType;
import club.projectessence.gameserver.instancemanager.MailManager;
import club.projectessence.gameserver.model.Message;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.dkp.*;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.serverpackets.CreatureSay;
import club.projectessence.gameserver.network.serverpackets.ExShowScreenMessage;
import club.projectessence.gameserver.util.Util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class ClanDkpShopWarehouse extends Warehouse {
	public static final DateTimeFormatter MAIL_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");
	private static final int MAX_HISTORY_SIZE = 1000;

	private final Clan _clan;

	public ClanDkpShopWarehouse(Clan clan) {
		_clan = clan;
	}

	@Override
	public String getName() {
		return "ClanDkpWarehouse";
	}

	@Override
	public int getOwnerId() {
		return _clan.getId();
	}

	@Override
	public PlayerInstance getOwner() {
		return _clan.getLeader().getPlayerInstance();
	}

	@Override
	public ItemLocation getBaseLocation() {
		return ItemLocation.DKPSHOPWH;
	}

	@Override
	public boolean validateCapacity(long slots) {
		return (_items.size() + slots) <= Config.DKP_WAREHOUSE_SLOTS_CLAN;
	}

	@Override
	public ItemInstance addItem(String process, int itemId, long count, PlayerInstance actor, Object reference) {
		return super.addItem(process, itemId, count, actor, reference);
	}

	@Override
	public ItemInstance addItem(String process, ItemInstance item, PlayerInstance actor, Object reference) {
		return super.addItem(process, item, actor, reference);
	}

	@Override
	public ItemInstance destroyItem(String process, ItemInstance item, long count, PlayerInstance actor, Object reference) {
		_clan.getDkp().removePrice(item.getObjectId());
		return super.destroyItem(process, item, count, actor, reference);
	}

	@Override
	public ItemInstance transferItem(String process, int objectId, long count, ItemContainer target, PlayerInstance actor, Object reference) {
		long amount = count;
		synchronized (this) {
			if (getItemByObjectId(objectId).getCount() <= amount) {
				_clan.getDkp().removePrice(objectId);
				if (getItemByObjectId(objectId).getCount() < amount) {
					LOGGER.info(getClass().getSimpleName() + ": tried to buy " + amount + " of " + getItemByObjectId(objectId) + " when count is " + getItemByObjectId(objectId).getCount());
					amount = getItemByObjectId(objectId).getCount();
				}
			}
			return super.transferItem(process, objectId, amount, target, actor, reference);
		}
	}

	public void buyItem(PlayerInstance player, int objectId, int count) {
		synchronized (this) {
			ItemInstance item = getItemByObjectId(objectId);
			if ((item == null) || (item.getCount() < count)) {
				player.sendMessage("Out of stock!");
				player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> DKP", "Out of stock!", 0));
				player.sendPacket(new ExShowScreenMessage("Out of stock!", 2, 1500, 0, true, true));
				return;
			}
			int playerOID = player.getObjectId();
			final ClanDkpData dkp = _clan.getDkp();
			int price = dkp.getPrice(objectId) * count;
			if ((price < 0) || (count < 0)) {
				return;
			}

			DkpPlayerInfo playerDkp = dkp.getPlayerDkp(playerOID);
			if (!playerDkp.decPoints(DkpEvent.DKP_SHOP_PURCHASE.getId(), 0, price)) {
				player.sendMessage("Not enough DKP!");
				player.sendPacket(new CreatureSay(null, ChatType.HERO_VOICE, ">> DKP", "Not enough DKP!", 0));
				player.sendPacket(new ExShowScreenMessage("Not enough DKP!", 2, 1500, 0, true, true));
				return;
			}

			final int itemId = item.getId();
			final int enchantLevel = item.getEnchantLevel();
			final boolean isAugmented = item.isAugmented();

			String itemName = item.getName() + (item.getItem().getAdditionalName() == null ? "" : item.getItem().getAdditionalName());
			final Message mail = new Message(player.getObjectId(), "DKP Purchase", //
					"Date: " + MAIL_DATE_FORMAT.format(LocalDateTime.now()) //
							+ "\nClan: " + _clan.getName()//
							+ "\nItem: " + itemName //
							+ "\nPrice: " + DkpCBH.NUMBER_FORMAT.format(price) + " DKP", MailType.SYSTEM);
			final Mail attachment = mail.createAttachments();
			transferItem("DKP BUY " + _clan.getName(), objectId, count, attachment, player, player);
			MailManager.getInstance().sendMessage(mail);

			_clan.getDkp().getShopHistoryRaw().add(new DkpShopHistoryHolder(player.getObjectId(), itemId, count, price, enchantLevel, isAugmented, System.currentTimeMillis()));
			if (_clan.getDkp().getShopHistoryRaw().size() > MAX_HISTORY_SIZE) {
				_clan.getDkp().getShopHistoryRaw().remove(0);
			}
		}
	}

	public List<ItemInstance> getShopItems() {
		return getShopItems(null);
	}

	public List<ItemInstance> getShopItems(String search) {
		final String[] searchWords = search == null ? null : search.toLowerCase().split(" ");
		final List<ItemInstance> sorted = new ArrayList<>(getItems(item ->
		{
			if (searchWords == null) {
				return true;
			}
			boolean isRussian = Util.containsCyrylic(search);
			String itemName = isRussian ? item.getItem().getNameRu() : item.getItemName();
			String addName = isRussian ? item.getItem().getAdditionalNameRu() : item.getItem().getAdditionalName();
			if (addName != null) {
				itemName += " " + addName;
			}
			itemName = itemName.toLowerCase();
			for (String word : searchWords) {
				if (!itemName.contains(word)) {
					return false;
				}
			}
			return true;
		}));

		sorted.sort(Comparator //
				.comparing(ItemInstance::isWeapon) //
				.thenComparing(ItemInstance::isArmor) //
				.thenComparing(i -> i.getItem().getItemGrade().ordinal()) //
				.thenComparing(i -> i.getItem().getBodyPart()) //
				.thenComparing(ItemInstance::getId) //
				.thenComparing(ItemInstance::getEnchantLevel) //
				.thenComparing(ItemInstance::isAugmented));
		Collections.reverse(sorted);

		return sorted;
	}
}
