/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.variables;

import club.projectessence.commons.database.DatabaseFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public class ItemVariables extends AbstractVariables {
	// Static Constants
	public static final String VISUAL_ID = "visualId";
	public static final String VISUAL_APPEARANCE_STONE_ID = "visualAppearanceStoneId";
	public static final String VISUAL_APPEARANCE_LIFE_TIME = "visualAppearanceLifetime";
	public static final String USAGE_LIMIT_USED = "usageLimitUsed";
	private static final Logger LOGGER = Logger.getLogger(ItemVariables.class.getName());
	// SQL Queries.
	private static final String SELECT_QUERY = "SELECT * FROM item_variables WHERE id = ?";
	private static final String SELECT_COUNT = "SELECT COUNT(*) FROM item_variables WHERE id = ?";
	private static final String DELETE_QUERY = "DELETE FROM item_variables WHERE id = ?";
	private static final String INSERT_QUERY = "INSERT INTO item_variables (id, var, val) VALUES (?, ?, ?)";
	public static String IS_LOCKED = "isLocked";
	public static String ENCHANT_DISABLED = "ENCHANT_DISABLED";
	private final int _objectId;

	public ItemVariables(int objectId) {
		_objectId = objectId;
		restoreMe();
	}

	public static boolean hasVariables(int objectId) {
		// Restore previous variables.
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement st = con.prepareStatement(SELECT_COUNT)) {
			st.setInt(1, objectId);
			try (ResultSet rset = st.executeQuery()) {
				if (rset.next()) {
					return rset.getInt(1) > 0;
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, ItemVariables.class.getSimpleName() + ": Couldn't select variables count for: " + objectId, e);
			return false;
		}
		return true;
	}

	@Override
	public boolean restoreMe() {
		// Restore previous variables.
		try (Connection con = DatabaseFactory.getConnection();
		     PreparedStatement st = con.prepareStatement(SELECT_QUERY)) {
			st.setInt(1, _objectId);
			try (ResultSet rset = st.executeQuery()) {
				while (rset.next()) {
					set(rset.getString("var"), rset.getString("val"), false);
				}
			}
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Couldn't restore variables for: " + _objectId, e);
			return false;
		} finally {
			compareAndSetChanges(true, false);
		}
		return true;
	}

	@Override
	public boolean storeMe() {
		// No changes, nothing to store.
		if (!hasChanges()) {
			return false;
		}

		try (Connection con = DatabaseFactory.getConnection()) {
			// Clear previous entries.
			try (PreparedStatement st = con.prepareStatement(DELETE_QUERY)) {
				st.setInt(1, _objectId);
				st.execute();
			}

			// Insert all variables.
			try (PreparedStatement st = con.prepareStatement(INSERT_QUERY)) {
				st.setInt(1, _objectId);
				for (Entry<String, Object> entry : getSet().entrySet()) {
					st.setString(2, entry.getKey());
					st.setString(3, String.valueOf(entry.getValue()));
					st.addBatch();
				}
				st.executeBatch();
			}
		} catch (SQLException e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Couldn't update variables for: " + _objectId, e);
			return false;
		} finally {
			compareAndSetChanges(true, false);
		}
		return true;
	}

	@Override
	public boolean deleteMe() {
		try (Connection con = DatabaseFactory.getConnection()) {
			// Clear previous entries.
			try (PreparedStatement st = con.prepareStatement(DELETE_QUERY)) {
				st.setInt(1, _objectId);
				st.execute();
			}

			// Clear all entries
			getSet().clear();
		} catch (Exception e) {
			LOGGER.log(Level.WARNING, getClass().getSimpleName() + ": Couldn't delete variables for: " + _objectId, e);
			return false;
		}
		return true;
	}
}
