/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.conditions;

import club.projectessence.gameserver.GameTimeController;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * The Class ConditionGameTime.
 *
 * <AUTHOR>
 */
public class ConditionGameTime extends Condition {
	private final CheckGameTime _check;
	private final boolean _required;
	/**
	 * Instantiates a new condition game time.
	 *
	 * @param check    the check
	 * @param required the required
	 */
	public ConditionGameTime(CheckGameTime check, boolean required) {
		_check = check;
		_required = required;
	}

	/**
	 * Test impl.
	 *
	 * @return true, if successful
	 */
	@Override
	public boolean testImpl(Creature effector, Creature effected, Skill skill, Item item) {
		switch (_check) {
			case NIGHT: {
				return GameTimeController.getInstance().isNight() == _required;
			}
		}
		return !_required;
	}

	/**
	 * The Enum CheckGameTime.
	 */
	public enum CheckGameTime {
		NIGHT
	}
}
