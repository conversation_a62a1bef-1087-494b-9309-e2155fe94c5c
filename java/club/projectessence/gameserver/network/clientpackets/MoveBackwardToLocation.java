package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.ai.CtrlEvent;
import club.projectessence.gameserver.ai.CtrlIntention;
import club.projectessence.gameserver.data.xml.DoorData;
import club.projectessence.gameserver.enums.AdminTeleportType;
import club.projectessence.gameserver.model.Location;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.events.EventDispatcher;
import club.projectessence.gameserver.model.events.impl.creature.player.OnPlayerMoveRequest;
import club.projectessence.gameserver.model.events.returns.TerminateReturn;
import club.projectessence.gameserver.model.skills.AbnormalVisualEffect;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.*;
import club.projectessence.gameserver.network.serverpackets.FlyToLocation.FlyType;
import club.projectessence.gameserver.taskmanager.autoplay.AutoPlayTaskManager;
import club.projectessence.gameserver.util.Broadcast;
import club.projectessence.gameserver.util.Util;

public class MoveBackwardToLocation extends ClientPacket {
	private int _targetX;
	private int _targetY;
	private int _targetZ;
	private int _originX;
	private int _originY;
	private int _originZ;
	private boolean _mouseClickMovement;

	@Override
	public void readImpl() {
		_targetX = readInt();
		_targetY = readInt();
		_targetZ = readInt();
		_originX = readInt();
		_originY = readInt();
		_originZ = readInt();
		_mouseClickMovement = readInt() != 0; // 1 if mouse is used, 0 if wasd / double mouse is used
	}

	@Override
	public void runImpl() {
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		if (player.isSpawnProtected()) {
			player.setSpawnProtection(false);
			player.getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.SEIZURE1);
			player.sendMessage("Spawn protection has ended.");
		}
		if (player.isTeleportProtected()) {
			player.setTeleportProtection(false);
			player.getEffectList().stopAbnormalVisualEffect(AbnormalVisualEffect.SEIZURE1);
			player.sendMessage("Spawn protection has ended.");
		}
		if (Config.ONLY_CHARACTER_CREATE && !player.isGM()) {
			// player.sendMessage("Server has not started yet!");
			player.sendPacket(ActionFailed.STATIC_PACKET);
			player.sendPacket(new ExShowScreenMessage("Server has not started yet!", 2, 2000, 0, true, false));
			// Disconnection.of(client).defaultSequence(true);
			return;
		}
		if ((Config.PLAYER_MOVEMENT_BLOCK_TIME > 0) && !player.isGM() && (player.getNotMoveUntil() > System.currentTimeMillis())) {
			player.sendPacket(SystemMessageId.YOU_CANNOT_MOVE_WHILE_SPEAKING_TO_AN_NPC_ONE_MOMENT_PLEASE);
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		if (!_mouseClickMovement) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			if ((_targetX == _originX) && (_targetY == _originY) && (_targetZ == _originZ)) {
				if (player.getAI() != null) {
					player.getAI().notifyEvent(CtrlEvent.EVT_ARRIVED_BLOCKED, player.getLocation());
				}
				return;
			}
		}
		// Check for possible door logout and move over exploit. Also checked at ValidatePosition.
		if (DoorData.getInstance().checkIfDoorsBetween(player.getX(), player.getY(), player.getZ(), _targetX, _targetY, _targetZ, player.getInstanceWorld(), false)) {
			player.stopMove(player.getLastServerPosition());
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		// Prevent player force moving in or out siege area.

		// Correcting targetZ from floor level to head level (?)
		// Client is giving floor level as targetZ but that floor level doesn't
		// match our current geodata and teleport coords as good as head level!
		// L2J uses floor, not head level as char coordinates. This is some
		// sort of incompatibility fix.
		// Validate position packets sends head level.
		_targetZ += player.getTemplate().getCollisionHeight();
		final TerminateReturn terminate = EventDispatcher.getInstance().notifyEvent(new OnPlayerMoveRequest(player, new Location(_targetX, _targetY, _targetZ)), player, TerminateReturn.class);
		if ((terminate != null) && terminate.terminate()) {
			player.sendPacket(ActionFailed.STATIC_PACKET);
			return;
		}
		final AdminTeleportType teleMode = player.getTeleMode();
		switch (teleMode) {
			case DEMONIC: {
				player.sendPacket(ActionFailed.STATIC_PACKET);
				player.stopMove(player.getLocation());
				player.setXYZ(_targetX, _targetY, _targetZ);
				player.stopMove(player.getLocation());
				player.sendPacket(new ValidateLocation(player));
				player.broadcastPacket(new ValidateLocation(player));
				player.setTeleMode(AdminTeleportType.NORMAL);
				break;
			}
			case CHARGE: {
				player.setXYZ(_targetX, _targetY, _targetZ);
				Broadcast.toSelfAndKnownPlayers(player, new MagicSkillUse(player, 30012, 10, 500, 0));
				Broadcast.toSelfAndKnownPlayers(player, new FlyToLocation(player, _targetX, _targetY, _targetZ, FlyType.CHARGE));
				Broadcast.toSelfAndKnownPlayers(player, new MagicSkillLaunched(player, 30012, 10));
				player.sendPacket(ActionFailed.STATIC_PACKET);
				break;
			}
			default: {
				double dx = _targetX - player.getX();
				double dy = _targetY - player.getY();
				// Can't move if character is confused, or trying to move a huge distance
				if (player.isControlBlocked() || (((dx * dx) + (dy * dy)) > 98010000)) // 9900*9900
				{
					player.sendPacket(ActionFailed.STATIC_PACKET);
					return;
				}
				// Check async range.
				dx = _originX - player.getX();
				dy = _originY - player.getY();
				double dz1 = _originZ - player.getZ();
				float diff = (float) Math.sqrt((dx * dx) + (dy * dy) + (dz1 * dz1));
				int heading = Util.calculateHeadingFrom(_originX, _originY, player.getX(), player.getY());
				if (Math.abs(player.getHeading() - heading) > 16000) {
					diff = diff * -1;
				}
				if (player.getAutoPlaySettings().isStopIfMoving()) {
					AutoPlayTaskManager.getInstance().stopAutoPlay(player);
				}
				player.getAI().setIntention(CtrlIntention.AI_INTENTION_MOVE_TO, new Location(_targetX, _targetY, _targetZ), _mouseClickMovement);
				break;
			}
		}
		player.onActionRequest();
	}
}