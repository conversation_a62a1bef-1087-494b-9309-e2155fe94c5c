/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets.pledge;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.clan.ClanMember;
import club.projectessence.gameserver.model.variables.ClanVariables;
import club.projectessence.gameserver.network.GameClient;
import club.projectessence.gameserver.network.serverpackets.ServerPacket;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR>
 */
public class ExPledgeContributionList extends ServerPacket {
	private final PlayerInstance _player;

	public ExPledgeContributionList(PlayerInstance player) {
		_player = player;
	}

	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer) {
		writeId(ServerPacketId.EX_PLEDGE_CONTRIBUTION_LIST, buffer);

		Clan clan = _player.getClan();
		if (clan == null) {
			buffer.writeInt(0x00);
			return;
		}
		buffer.writeInt(clan.getMembersCount());
		for (ClanMember member : clan.getMembers()) {
			buffer.writeSizedString(member.getName());
			buffer.writeInt((int) clan.getVariables().getDouble(ClanVariables.CLAN_WEEKLY_CONTRIBUTION_VAR + member.getObjectId(), 0));
			buffer.writeInt((int) clan.getVariables().getDouble(ClanVariables.CLAN_TOTAL_CONTRIBUTION_VAR + member.getObjectId(), 0));
		}
	}
}
