/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.targethandlers.affectscope;

import club.projectessence.gameserver.enums.Faction;
import club.projectessence.gameserver.handler.AffectObjectHandler;
import club.projectessence.gameserver.handler.IAffectObjectHandler;
import club.projectessence.gameserver.handler.IAffectScopeHandler;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.Playable;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.Skill;
import club.projectessence.gameserver.model.skills.targets.AffectScope;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.function.Predicate;

/**
 * Clan or Faction affect scope implementation.
 * Affects clan members first, then extends to faction members within range.
 */
public class PledgeOrFaction implements IAffectScopeHandler {
    @Override
    public void forEachAffected(Creature creature, WorldObject target, Skill skill, Consumer<? super WorldObject> action) {
        final IAffectObjectHandler affectObject = AffectObjectHandler.getInstance().getHandler(skill.getAffectObject());
        final int affectRange = skill.getAffectRange();
        final int affectLimit = skill.getAffectLimit();

        if (target.isPlayable()) {
            final Playable playable = (Playable) target;
            final PlayerInstance player = playable.getActingPlayer();
            final int clanId = player.getClanId();
            final Faction faction = player.getFaction();

            if (clanId > 0) {
                // Target checks for clan members
                final AtomicInteger affected = new AtomicInteger(0);
                final Predicate<Playable> clanFilter = c ->
                {
                    if ((affectLimit > 0) && (affected.get() >= affectLimit)) {
                        return false;
                    }

                    final PlayerInstance p = c.getActingPlayer();
                    if ((p == null) || p.isDead()) {
                        return false;
                    }

                    // Must be in same clan
                    if (p.getClanId() != clanId) {
                        return false;
                    }

                    if ((affectObject != null) && !affectObject.checkAffectedObject(creature, p)) {
                        return false;
                    }

                    affected.incrementAndGet();
                    return true;
                };

                // Add clan members first
                if (clanFilter.test(playable)) {
                    action.accept(playable);
                }

                // Check clan members in range
                World.getInstance().forEachVisibleObjectInRange(playable, Playable.class, affectRange, c ->
                {
                    if (clanFilter.test(c)) {
                        action.accept(c);
                    }
                });

                // Then add faction members within range (excluding clan members)
                if (faction != Faction.NONE && affected.get() < affectLimit) {
                    final Predicate<Playable> factionFilter = c ->
                    {
                        if ((affectLimit > 0) && (affected.get() >= affectLimit)) {
                            return false;
                        }

                        final PlayerInstance p = c.getActingPlayer();
                        if ((p == null) || p.isDead()) {
                            return false;
                        }

                        // Must be same faction but NOT in clan (already added)
                        if (p.getFaction() != faction || p.getClanId() == clanId) {
                            return false;
                        }

                        if ((affectObject != null) && !affectObject.checkAffectedObject(creature, p)) {
                            return false;
                        }

                        affected.incrementAndGet();
                        return true;
                    };

                    // Check faction members in range
                    World.getInstance().forEachVisibleObjectInRange(playable, Playable.class, affectRange, c ->
                    {
                        if (factionFilter.test(c)) {
                            action.accept(c);
                        }
                    });
                }
            } else {
                // No clan, just affect faction members
                if (faction != Faction.NONE) {
                    final AtomicInteger affected = new AtomicInteger(0);
                    final Predicate<Playable> factionFilter = c ->
                    {
                        if ((affectLimit > 0) && (affected.get() >= affectLimit)) {
                            return false;
                        }

                        final PlayerInstance p = c.getActingPlayer();
                        if ((p == null) || p.isDead()) {
                            return false;
                        }

                        if (p.getFaction() != faction) {
                            return false;
                        }

                        if ((affectObject != null) && !affectObject.checkAffectedObject(creature, p)) {
                            return false;
                        }

                        affected.incrementAndGet();
                        return true;
                    };

                    // Add self first
                    if (factionFilter.test(playable)) {
                        action.accept(playable);
                    }

                    // Add faction members in range
                    World.getInstance().forEachVisibleObjectInRange(playable, Playable.class, affectRange, c ->
                    {
                        if (factionFilter.test(c)) {
                            action.accept(c);
                        }
                    });
                }
            }
        }
    }

    @Override
    public Enum<AffectScope> getAffectScopeType() {
        return AffectScope.PLEDGE_OR_FACTION;
    }
}
