/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package handlers.admincommandhandlers;

import java.text.DecimalFormat;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.gameserver.cache.HtmCache;
import club.projectessence.gameserver.handler.IAdminCommandHandler;
import club.projectessence.gameserver.model.World;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.network.serverpackets.NpcHtmlMessage;
import club.projectessence.gameserver.util.Util;

/**
 * <AUTHOR>
 */
public class AdminBalthusEventMonitor implements IAdminCommandHandler
{
	protected static final Logger	LOGGER			= Logger.getLogger(AdminBalthusEventMonitor.class.getName());
	private static final String[]	ADMIN_COMMANDS	=
	{
		"admin_balthus",
		"admin_balthusevent"
	};
	
	@Override
	public boolean useAdminCommand(String command, PlayerInstance activeChar)
	{
		String params[] = command.split(" ");
		if (params.length == 1)
		{
			sendHtml(activeChar);
			return true;
		}
		else if (params[1].equals("count_balthus_knight_mark"))
		{
			Config.COUNT_BALTHUS_KNIGHT_MARK = !Config.COUNT_BALTHUS_KNIGHT_MARK;
		}
		else if (params[1].equals("count_life_control_tower"))
		{
			Config.COUNT_LIFE_CONTROL_TOWER = !Config.COUNT_LIFE_CONTROL_TOWER;
		}
		else if (params[1].equals("count_mid_grade_hp_potion"))
		{
			Config.COUNT_MID_GRADE_HP_POTION = !Config.COUNT_MID_GRADE_HP_POTION;
		}
		else if (params[1].equals("count_scroll_boost_attack"))
		{
			Config.COUNT_SCROLL_BOOST_ATTACK = !Config.COUNT_SCROLL_BOOST_ATTACK;
		}
		else if (params[1].equals("count_scroll_boost_defense"))
		{
			Config.COUNT_SCROLL_BOOST_DEFENSE = !Config.COUNT_SCROLL_BOOST_DEFENSE;
		}
		else if (params[1].equals("count_sayha_cookie"))
		{
			Config.COUNT_SAYHA_COOKIE = !Config.COUNT_SAYHA_COOKIE;
		}
		else if (params[1].equals("count_sayha_blessing"))
		{
			Config.COUNT_SAYHA_BLESSING = !Config.COUNT_SAYHA_BLESSING;
		}
		sendHtml(activeChar);
		return true;
	}
	
	private void sendHtml(PlayerInstance player)
	{
		String content = HtmCache.getInstance().getHtm(player, "data/html/admin/balthusEvent/bathusEventMonitor.htm");
		final NpcHtmlMessage html = new NpcHtmlMessage(0, 1);
		if (content != null)
		{
			html.setHtml(content);
		}
		html.replace("%count_balthus_knight_mark%", Config.COUNT_BALTHUS_KNIGHT_MARK ? "On" : "Off");
		html.replace("%balthus_knight_mark_farmed%", World.BALTHUS_KNIGHT_MARK_FARMED);
		html.replace("%count_life_control_tower%", Config.COUNT_LIFE_CONTROL_TOWER ? "On" : "Off");
		html.replace("%life_control_tower_farmed%", World.LIFE_CONTROL_TOWER_FARMED);
		html.replace("%count_mid_grade_hp_potion%", Config.COUNT_MID_GRADE_HP_POTION ? "On" : "Off");
		html.replace("%mid_grade_hp_potion_farmed%", World.MID_GRADE_HP_POTION_FARMED);
		html.replace("%count_scroll_boost_attack%", Config.COUNT_SCROLL_BOOST_ATTACK ? "On" : "Off");
		html.replace("%scroll_boost_attack_farmed%", World.SCROLL_BOOST_ATTACK_FARMED);
		html.replace("%count_scroll_boost_defense%", Config.COUNT_SCROLL_BOOST_DEFENSE ? "On" : "Off");
		html.replace("%scroll_boost_defense_farmed%", World.SCROLL_BOOST_DEFENSE_FARMED);
		html.replace("%count_sayha_cookie%", Config.COUNT_SAYHA_COOKIE ? "On" : "Off");
		html.replace("%sayha_cookie_farmed%", World.SAYHA_COOKIE_FARMED);
		html.replace("%count_sayha_blessing%", Config.COUNT_SAYHA_BLESSING ? "On" : "Off");
		html.replace("%sayha_blessing_farmed%", World.SAYHA_BLESSING_FARMED);

		html.replace("%total_adena_take%", Util.formatAdena(World.ADENA_TAKE));
		
		html.replace("%total_lcoin_take%", World.L_COINS_TAKE);
		html.replace("%total_sibi_coin_take%", World.SIBI_COIN_TAKE);
		player.sendPacket(html);
	}
	
	@Override
	public String[] getAdminCommandList()
	{
		return ADMIN_COMMANDS;
	}
}
