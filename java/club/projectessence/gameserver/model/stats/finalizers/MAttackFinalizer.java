/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.stats.finalizers;

import club.projectessence.Config;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.effects.EffectFlag;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.model.stats.BaseStat;
import club.projectessence.gameserver.model.stats.IStatFunction;
import club.projectessence.gameserver.model.stats.Stat;

import java.util.OptionalDouble;

/**
 * <AUTHOR>
 */
public class MAttackFinalizer implements IStatFunction {
	@Override
	public double calc(Creature creature, OptionalDouble base, Stat stat) {
		throwIfPresent(base);

		double baseValue = calcWeaponBaseValue(creature, stat);

		// Use standard enchant calculation (now balanced in IStatFunction)
		baseValue += calcEnchantedItemBonus(creature, stat);

		if (creature.isPlayer() || creature.isPet()) {
			// Enchanted chest bonus
			baseValue += calcEnchantBodyPart(creature, Item.BodyPart.SLOT_CHEST.getSlot(), Item.BodyPart.SLOT_FULL_ARMOR.getSlot());
		}

		if (Config.CHAMPION_ENABLE && creature.isChampion()) {
			baseValue *= Config.CHAMPION_ATK;
		}
		if (creature.isRaid()) {
			baseValue *= Config.RAID_MATTACK_MULTIPLIER;
		}

		// Calculate modifiers Magic Attack
		final double physicalBonus = (creature.getStat().getMul(Stat.MAGIC_ATTACK_BY_PHYSICAL_ATTACK, 0) - 1) * creature.getPAtk();
		baseValue *= Math.pow(BaseStat.INT.calcBonus(creature) * creature.getLevelMod(), 2.2072);
		double higherManaGainBonus = 1;
		if (creature.isAffected(EffectFlag.HIGHER_MANA_GAIN)) {
			int maxMp = creature.getMaxMp();
			higherManaGainBonus = maxMp >= 20000 ? 400 : maxMp >= 15000 ? 300 : maxMp >= 10000 ? 200 : maxMp >= 6500 ? 150 : maxMp >= 4000 ? 100 : 0;
		}
		// Hard coded Evolution Power
		final double evolutionPowerBonus = creature.isAffected(EffectFlag.EVOLUTION_POWER) ? Math.max(0, (creature.getLevel() - 70) * 20) : 0;
		return validateValue(creature, Stat.defaultValue(creature, stat, baseValue + physicalBonus + higherManaGainBonus + evolutionPowerBonus), 0, creature.isPlayable() ? Config.MAX_MATK : Double.MAX_VALUE);
	}

	@Override
	public double calcEnchantBodyPartBonus(int enchantLevel, boolean isBlessed) {
		if (isBlessed) {
			return (2 * Math.max(enchantLevel - 3, 0)) + (2 * Math.max(enchantLevel - 6, 0));
		}
		return (1.4 * Math.max(enchantLevel - 3, 0)) + (1.4 * Math.max(enchantLevel - 6, 0));
	}
}
