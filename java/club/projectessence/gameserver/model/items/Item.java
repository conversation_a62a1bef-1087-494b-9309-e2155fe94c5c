/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.model.items;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.EnumMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.logging.Logger;

import club.projectessence.Config;
import club.projectessence.gameserver.data.ItemTable;
import club.projectessence.gameserver.enums.AttributeType;
import club.projectessence.gameserver.enums.AutoUseItemType;
import club.projectessence.gameserver.enums.ItemGrade;
import club.projectessence.gameserver.enums.ItemSkillType;
import club.projectessence.gameserver.model.ExtractableProduct;
import club.projectessence.gameserver.model.PlayerCondOverride;
import club.projectessence.gameserver.model.StatSet;
import club.projectessence.gameserver.model.WorldObject;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.ceremonyofchaos.CeremonyOfChaosEvent;
import club.projectessence.gameserver.model.commission.CommissionItemType;
import club.projectessence.gameserver.model.conditions.Condition;
import club.projectessence.gameserver.model.events.ListenersContainer;
import club.projectessence.gameserver.model.holders.ItemSkillHolder;
import club.projectessence.gameserver.model.interfaces.IIdentifiable;
import club.projectessence.gameserver.model.items.enchant.attribute.AttributeHolder;
import club.projectessence.gameserver.model.items.type.ActionType;
import club.projectessence.gameserver.model.items.type.CrystalType;
import club.projectessence.gameserver.model.items.type.EtcItemType;
import club.projectessence.gameserver.model.items.type.ItemType;
import club.projectessence.gameserver.model.items.type.MaterialType;
import club.projectessence.gameserver.model.stats.Stat;
import club.projectessence.gameserver.model.stats.functions.FuncAdd;
import club.projectessence.gameserver.model.stats.functions.FuncSet;
import club.projectessence.gameserver.model.stats.functions.FuncTemplate;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.serverpackets.SystemMessage;

/**
 * This class contains all informations concerning the item (weapon, armor, etc).<br>
 * Mother class of :
 * <ul>
 * <li>Armor</li>
 * <li>EtcItem</li>
 * <li>Weapon</li>
 * </ul>
 */
public abstract class Item extends ListenersContainer implements IIdentifiable
{
	public static final int						TYPE1_WEAPON_RING_EARRING_NECKLACE	= 0;
	public static final int						TYPE1_SHIELD_ARMOR					= 1;
	public static final int						TYPE1_ITEM_QUESTITEM_ADENA			= 4;
	public static final int						TYPE2_WEAPON						= 0;
	public static final int						TYPE2_SHIELD_ARMOR					= 1;
	public static final int						TYPE2_ACCESSORY						= 2;
	public static final int						TYPE2_QUEST							= 3;
	public static final int						TYPE2_MONEY							= 4;
	public static final int						TYPE2_OTHER							= 5;
	public static final int						SLOT_MULTI_ALLWEAPON				= (int) (BodyPart.SLOT_LR_HAND.getSlot() | BodyPart.SLOT_R_HAND.getSlot());
	protected static final Logger				LOGGER								= Logger.getLogger(Item.class.getName());
	// TODO: do proper datapack
	// @formatter:off
	private static int[]						_nonWorthlessEtcItems				=
	{
		8868, 49088, 93314, 93906, 93907, 93908, 93909, 93910
	};
	protected int								_type1;																											// needed for item list (inventory)
	protected int								_type2;																											// different lists for armor, weapon, etc
	protected Map<Stat, FuncTemplate>			_funcTemplates;
	protected List<Condition>					_preConditions;
	private int									_itemId;
	private int									_displayId;
	private String								_name;
	private String								_additionalName;
	private String								_nameRu;
	private String								_additionalNameRu;
	private String								_icon;
	private AutoUseItemType						_shortcutToggleType;
	private int									_weight;
	private boolean								_stackable;
	private MaterialType						_materialType;
	private CrystalType							_crystalType;
	private int									_equipReuseDelay;
	private int									_duration;
	private long								_time;
	private int									_autoDestroyTime;
	private long								_bodyPart;
	private int									_referencePrice;
	private int									_crystalCount;
	private boolean								_sellable;
	private boolean								_dropable;
	private boolean								_destroyable;
	private boolean								_tradeable;
	private boolean								_depositable;
	private boolean								_enchantable;
	private int									_enchantLimit;
	private boolean								_elementable;
	private boolean								_questItem;
	private boolean								_freightable;
	private boolean								_allowSelfResurrection;
	private boolean								_isOlyRestricted;
	private boolean								_isCocRestricted;
	private boolean								_forNpc;
	private boolean								_common;
	private boolean								_heroItem;
	private boolean								_pvpItem;
	private boolean								_immediateEffect;
	private boolean								_exImmediateEffect;
	private int									_defaultEnchantLevel;
	private ActionType							_defaultAction;
	private boolean								_isDisabledForPets;
	private Map<AttributeType, AttributeHolder>	_elementals							= null;
	private List<ItemSkillHolder>				_skills;
	private int									_useSkillDisTime;
	private int									_reuseDelay;
	private int									_sharedReuseGroup;
	private CommissionItemType					_commissionItemType;
	private int									_usageLimit;
	private boolean								_isAppearanceable;
	private boolean								_isBlessed;
	private int									_artifactSlot;
	
	/**
	 * Constructor of the Item that fill class variables.
	 *
	 * @param set
	 *            : StatSet corresponding to a set of couples (key,value) for description of the item
	 */
	protected Item(StatSet set)
	{
		set(set);
	}
	
	public void set(StatSet set)
	{
		_itemId = set.getInt("item_id");
		_displayId = set.getInt("displayId", _itemId);
		_name = set.getString("name");
		_additionalName = set.getString("additionalName", null);
		_nameRu = set.getString("nameRu");
		_additionalNameRu = set.getString("additionalNameRu", null);
		_icon = set.getString("icon", null);
		_shortcutToggleType = AutoUseItemType.getById(set.getInt("shortcutToggleType", 0));
		_weight = set.getInt("weight", 0);
		_materialType = set.getEnum("material", MaterialType.class, MaterialType.STEEL);
		_equipReuseDelay = set.getInt("equip_reuse_delay", 0) * 1000;
		_duration = set.getInt("duration", -1);
		_time = set.getInt("time", -1);
		_autoDestroyTime = set.getInt("auto_destroy_time", -1) * 1000;
		_bodyPart = ItemTable.SLOTS.get(set.getString("bodypart", "none"));
		_referencePrice = set.getInt("price", 0);
		_crystalType = set.getEnum("crystal_type", CrystalType.class, CrystalType.NONE);
		_crystalCount = set.getInt("crystal_count", 0);
		_stackable = set.getBoolean("is_stackable", false);
		_sellable = set.getBoolean("is_sellable", true);
		_dropable = set.getBoolean("is_dropable", true);
		_destroyable = set.getBoolean("is_destroyable", true);
		_tradeable = set.getBoolean("is_tradable", true);
		_questItem = set.getBoolean("is_questitem", false);
		if (Config.CUSTOM_DEPOSITABLE_ENABLED)
		{
			_depositable = _questItem ? Config.CUSTOM_DEPOSITABLE_QUEST_ITEMS : true;
		}
		else
		{
			_depositable = set.getBoolean("is_depositable", true);
		}
		_elementable = set.getBoolean("element_enabled", false);
		_enchantable = set.getBoolean("enchant_enabled", false);
		_enchantLimit = set.getInt("enchant_limit", 0);
		_freightable = set.getBoolean("is_freightable", false);
		_allowSelfResurrection = set.getBoolean("allow_self_resurrection", false);
		_isOlyRestricted = set.getBoolean("is_oly_restricted", false);
		_isCocRestricted = set.getBoolean("is_coc_restricted", false);
		_forNpc = set.getBoolean("for_npc", false);
		_isAppearanceable = set.getBoolean("isAppearanceable", false);
		_isBlessed = set.getBoolean("blessed", false);
		_artifactSlot = set.getInt("artifactSlot", 0);
		_immediateEffect = set.getBoolean("immediate_effect", false);
		_exImmediateEffect = set.getBoolean("ex_immediate_effect", false);
		_defaultAction = set.getEnum("default_action", ActionType.class, ActionType.NONE);
		_useSkillDisTime = set.getInt("useSkillDisTime", 0);
		_defaultEnchantLevel = set.getInt("enchanted", 0);
		_reuseDelay = set.getInt("reuse_delay", 0);
		_sharedReuseGroup = set.getInt("shared_reuse_group", 0);
		_commissionItemType = set.getEnum("commissionItemType", CommissionItemType.class, CommissionItemType.OTHER_ITEM);
		_usageLimit = set.getInt("usage_limit", 0);
		_isDisabledForPets = set.getBoolean("is_disabled_for_pets", false);
		_common = ((_itemId >= 11605) && (_itemId <= 12361));
		_heroItem = ((_itemId >= 6611) && (_itemId <= 6621)) || ((_itemId >= 9388) && (_itemId <= 9390)) || (_itemId == 6842);
		_pvpItem = ((_itemId >= 10667) && (_itemId <= 10835)) || ((_itemId >= 12852) && (_itemId <= 12977)) || ((_itemId >= 14363) && (_itemId <= 14525)) || (_itemId == 14528) || (_itemId == 14529) || (_itemId == 14558) || ((_itemId >= 15913) && (_itemId <= 16024)) || ((_itemId >= 16134) && (_itemId <= 16147)) || (_itemId == 16149) || (_itemId == 16151) || (_itemId == 16153) || (_itemId == 16155) || (_itemId == 16157) || (_itemId == 16159) || ((_itemId >= 16168) && (_itemId <= 16176)) || ((_itemId >= 16179) && (_itemId <= 16220));
	}
	
	/**
	 * Returns the itemType.
	 *
	 * @return Enum
	 */
	public abstract ItemType getItemType();
	
	/**
	 * Verifies if the item is an etc item.
	 *
	 * @return {@code true} if the item is an etc item, {@code false} otherwise.
	 */
	public boolean isEtcItem()
	{
		return false;
	}
	
	/**
	 * Verifies if the item is an armor.
	 *
	 * @return {@code true} if the item is an armor, {@code false} otherwise.
	 */
	public boolean isArmor()
	{
		return false;
	}
	
	/**
	 * Verifies if the item is a weapon.
	 *
	 * @return {@code true} if the item is a weapon, {@code false} otherwise.
	 */
	public boolean isWeapon()
	{
		return false;
	}
	
	/**
	 * Verifies if the item is a magic weapon.
	 *
	 * @return {@code true} if the weapon is magic, {@code false} otherwise.
	 */
	public boolean isMagicWeapon()
	{
		return false;
	}
	
	/**
	 * @return the _equipReuseDelay
	 */
	public int getEquipReuseDelay()
	{
		return _equipReuseDelay;
	}
	
	/**
	 * Returns the duration of the item
	 *
	 * @return int
	 */
	public int getDuration()
	{
		return _duration;
	}
	
	/**
	 * Returns the time of the item
	 *
	 * @return long
	 */
	public long getTime()
	{
		return _time;
	}
	
	/**
	 * @return the auto destroy time of the item in seconds: 0 or less - default
	 */
	public int getAutoDestroyTime()
	{
		return _autoDestroyTime;
	}
	
	/**
	 * Returns the ID of the item
	 *
	 * @return int
	 */
	@Override
	public int getId()
	{
		return _itemId;
	}
	
	/**
	 * Returns the ID of the item
	 *
	 * @return int
	 */
	public int getDisplayId()
	{
		return _displayId;
	}
	
	public boolean isDisabledForPets()
	{
		return _isDisabledForPets;
	}
	
	public abstract int getItemMask();
	
	/**
	 * Return the type of material of the item
	 *
	 * @return MaterialType
	 */
	public MaterialType getMaterialType()
	{
		return _materialType;
	}
	
	/**
	 * Returns the type 2 of the item
	 *
	 * @return int
	 */
	public int getType2()
	{
		return _type2;
	}
	
	/**
	 * Returns the weight of the item
	 *
	 * @return int
	 */
	public int getWeight()
	{
		return _weight;
	}
	
	/**
	 * Returns if the item is crystallizable
	 *
	 * @return boolean
	 */
	public boolean isCrystallizable()
	{
		return (_crystalType != CrystalType.NONE) && (_crystalCount > 0);
	}
	
	/**
	 * @return return General item grade (No S80, S84, R95, R99)
	 */
	public ItemGrade getItemGrade()
	{
		return ItemGrade.valueOf(_crystalType);
	}
	
	/**
	 * Return the type of crystal if item is crystallizable
	 *
	 * @return CrystalType
	 */
	public CrystalType getCrystalType()
	{
		return _crystalType;
	}
	
	/**
	 * Return the ID of crystal if item is crystallizable
	 *
	 * @return int
	 */
	public int getCrystalItemId()
	{
		return _crystalType.getCrystalId();
	}
	
	/**
	 * For grades S80 and S84 return S, R95, and R99 return R
	 *
	 * @return the grade of the item.
	 */
	public CrystalType getCrystalTypePlus()
	{
		switch (_crystalType)
		{
			case S80:
			case S84:
			{
				return CrystalType.S;
			}
			case R95:
			case R99:
			{
				return CrystalType.R;
			}
			default:
			{
				return _crystalType;
			}
		}
	}
	
	/**
	 * @return the quantity of crystals for crystallization.
	 */
	public int getCrystalCount()
	{
		return _crystalCount;
	}
	
	/**
	 * @param enchantLevel
	 * @return the quantity of crystals for crystallization on specific enchant level
	 */
	public int getCrystalCount(int enchantLevel)
	{
		if (enchantLevel > 3)
		{
			switch (_type2)
			{
				case TYPE2_SHIELD_ARMOR:
				case TYPE2_ACCESSORY:
				{
					return _crystalCount + (_crystalType.getCrystalEnchantBonusArmor() * ((3 * enchantLevel) - 6));
				}
				case TYPE2_WEAPON:
				{
					return _crystalCount + (_crystalType.getCrystalEnchantBonusWeapon() * ((2 * enchantLevel) - 3));
				}
				default:
				{
					return _crystalCount;
				}
			}
		}
		else if (enchantLevel > 0)
		{
			switch (_type2)
			{
				case TYPE2_SHIELD_ARMOR:
				case TYPE2_ACCESSORY:
				{
					return _crystalCount + (_crystalType.getCrystalEnchantBonusArmor() * enchantLevel);
				}
				case TYPE2_WEAPON:
				{
					return _crystalCount + (_crystalType.getCrystalEnchantBonusWeapon() * enchantLevel);
				}
				default:
				{
					return _crystalCount;
				}
			}
		}
		else
		{
			return _crystalCount;
		}
	}
	
	/**
	 * @return the name of the item.
	 */
	public String getName()
	{
		return _name;
	}
	
	/**
	 * @return the item's additional name.
	 */
	public String getAdditionalName()
	{
		return _additionalName;
	}
	
	public String getNameRu()
	{
		return _nameRu;
	}
	
	public String getAdditionalNameRu()
	{
		return _additionalNameRu;
	}
	
	public Collection<AttributeHolder> getAttributes()
	{
		return _elementals != null ? _elementals.values() : null;
	}
	
	/**
	 * Sets the base elemental of the item.
	 *
	 * @param holder
	 *            the element to set.
	 */
	public void setAttributes(AttributeHolder holder)
	{
		if (_elementals == null)
		{
			_elementals = new LinkedHashMap<>(3);
			_elementals.put(holder.getType(), holder);
		}
		else
		{
			final AttributeHolder attribute = getAttribute(holder.getType());
			if (attribute != null)
			{
				attribute.setValue(holder.getValue());
			}
			else
			{
				_elementals.put(holder.getType(), holder);
			}
		}
	}
	
	public AttributeHolder getAttribute(AttributeType type)
	{
		return _elementals != null ? _elementals.get(type) : null;
	}
	
	/**
	 * @return the part of the body used with the item.
	 */
	public long getBodyPart()
	{
		return _bodyPart;
	}
	
	/**
	 * @return the type 1 of the item.
	 */
	public int getType1()
	{
		return _type1;
	}
	
	/**
	 * @return {@code true} if the item is stackable, {@code false} otherwise.
	 */
	public boolean isStackable()
	{
		return _stackable;
	}
	
	/**
	 * @return {@code true} if the item can be equipped, {@code false} otherwise.
	 */
	public boolean isEquipable()
	{
		return (_bodyPart != 0) && !(getItemType() instanceof EtcItemType);
	}
	
	/**
	 * @return the price of reference of the item.
	 */
	public int getReferencePrice()
	{
		// Essence
		if ((this instanceof EtcItem) && (Arrays.binarySearch(_nonWorthlessEtcItems, getId()) < 0))
		{
			return 0;
		}
		return _referencePrice;
	}
	// @formatter:on
	
	/**
	 * @return {@code true} if the item can be sold, {@code false} otherwise.
	 */
	public boolean isSellable()
	{
		return _sellable;
	}
	
	/**
	 * @return {@code true} if the item can be dropped, {@code false} otherwise.
	 */
	public boolean isDropable()
	{
		return _dropable;
	}
	
	/**
	 * @return {@code true} if the item can be destroyed, {@code false} otherwise.
	 */
	public boolean isDestroyable()
	{
		return _destroyable;
	}
	
	/**
	 * @return {@code true} if the item can be traded, {@code false} otherwise.
	 */
	public boolean isTradeable()
	{
		return _tradeable;
	}
	
	/**
	 * @return {@code true} if the item can be put into warehouse, {@code false} otherwise.
	 */
	public boolean isDepositable()
	{
		return _depositable;
	}
	
	/**
	 * This method also check the enchant blacklist.
	 *
	 * @return {@code true} if the item can be enchanted, {@code false} otherwise.
	 */
	public boolean isEnchantable()
	{
		return Arrays.binarySearch(Config.ENCHANT_BLACKLIST, _itemId) < 0 ? _enchantable : false;
	}
	
	/**
	 * Returns the enchantment limit of the item
	 *
	 * @return int
	 */
	public int getEnchantLimit()
	{
		return _enchantLimit > 0 ? _enchantLimit : 0;
	}
	
	/**
	 * @return {@code true} if the item can be elemented, {@code false} otherwise.
	 */
	public boolean isElementable()
	{
		return _elementable;
	}
	
	/**
	 * Returns if item is common
	 *
	 * @return boolean
	 */
	public boolean isCommon()
	{
		return _common;
	}
	
	/**
	 * Returns if item is hero-only
	 *
	 * @return
	 */
	public boolean isHeroItem()
	{
		return _heroItem;
	}
	
	/**
	 * Returns if item is pvp
	 *
	 * @return
	 */
	public boolean isPvpItem()
	{
		return _pvpItem;
	}
	
	public boolean isPotion()
	{
		return getItemType() == EtcItemType.POTION;
	}
	
	public boolean isElixir()
	{
		return getItemType() == EtcItemType.ELIXIR;
	}
	
	public boolean isScroll()
	{
		return getItemType() == EtcItemType.SCROLL;
	}
	
	/**
	 * amount of enhancement stones obtained when failing enchant
	 *
	 * @param grade
	 * @param enchantLevel
	 * @return enhancement stones count
	 */
	public int getEnhancementStones(CrystalType grade, int enchantLevel)
	{
		switch (_type2)
		{
			case 1:
			case 2: // armor
			{
				if (enchantLevel >= 6)
				{
					switch (grade)
					{
						case D:
						{
							if (enchantLevel <= 8)
							{
								return (1 + enchantLevel) - 6;
							}
							if (enchantLevel <= 11)
							{
								return (1 + enchantLevel) - 5;
							}
							return 12;
						}
						case C:
						{
							if (enchantLevel <= 8)
							{
								return (2 + enchantLevel) - 6;
							}
							if (enchantLevel <= 11)
							{
								return (2 + enchantLevel) - 4;
							}
							return 15;
						}
						case B:
						{
							if (enchantLevel <= 8)
							{
								return (3 + enchantLevel) - 6;
							}
							if (enchantLevel <= 11)
							{
								return (3 + enchantLevel) - 3;
							}
							return 19;
						}
						case A:
						{
							if (enchantLevel <= 8)
							{
								return (5 + enchantLevel) - 6;
							}
							if (enchantLevel <= 11)
							{
								return 12 + ((enchantLevel - 9) * 2);
							}
							return 26;
						}
						case S:
						{
							if (enchantLevel <= 9)
							{
								return 10 + ((enchantLevel - 6) * 5);
							}
							if (enchantLevel <= 11)
							{
								return (27 + enchantLevel) - 10;
							}
							return 30;
						}
					}
					return 0;
				}
				break;
			}
			case 0: // weapon
			{
				if (enchantLevel >= 7)
				{
					switch (grade)
					{
						case D:
						{
							if (enchantLevel <= 18)
							{
								return (1 + enchantLevel) - 7;
							}
							return 14;
						}
						case C:
						{
							if (enchantLevel <= 9)
							{
								return (2 + enchantLevel) - 7;
							}
							if (enchantLevel <= 12)
							{
								return (2 + enchantLevel) - 5;
							}
							if (enchantLevel <= 15)
							{
								return (2 + enchantLevel) - 3;
							}
							if (enchantLevel <= 18)
							{
								return (2 + enchantLevel) - 1;
							}
							return 25;
						}
						case B:
						{
							if (enchantLevel <= 9)
							{
								return (3 + enchantLevel) - 7;
							}
							if (enchantLevel <= 12)
							{
								return (3 + enchantLevel) - 5;
							}
							if (enchantLevel <= 15)
							{
								return (3 + enchantLevel) - 3;
							}
							if (enchantLevel <= 18)
							{
								return (3 + enchantLevel) - 1;
							}
							return 28;
						}
						case A:
						{
							if (enchantLevel <= 9)
							{
								return (5 + enchantLevel) - 7;
							}
							if (enchantLevel <= 12)
							{
								return (5 + enchantLevel) - 3;
							}
							if (enchantLevel <= 15)
							{
								return 5 + enchantLevel + 1;
							}
							if (enchantLevel <= 18)
							{
								return 5 + enchantLevel + 5;
							}
							return 38;
						}
						case S:
						{
							if (enchantLevel <= 9)
							{
								return 15 + ((enchantLevel - 7) * 3);
							}
							if (enchantLevel <= 11)
							{
								return 25 + ((enchantLevel - 10) * 3);
							}
							if (enchantLevel <= 18)
							{
								return (29 + enchantLevel) - 12;
							}
							return 36;
						}
					}
					return 0;
				}
				break;
			}
		}
		return 0;
	}
	
	/**
	 * Add the FuncTemplate f to the list of functions used with the item
	 *
	 * @param template
	 *            : FuncTemplate to add
	 */
	public void addFunctionTemplate(FuncTemplate template)
	{
		switch (template.getStat())
		{
			case FIRE_RES:
			case FIRE_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.FIRE, (int) template.getValue()));
				break;
			}
			case WATER_RES:
			case WATER_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.WATER, (int) template.getValue()));
				break;
			}
			case WIND_RES:
			case WIND_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.WIND, (int) template.getValue()));
				break;
			}
			case EARTH_RES:
			case EARTH_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.EARTH, (int) template.getValue()));
				break;
			}
			case HOLY_RES:
			case HOLY_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.HOLY, (int) template.getValue()));
				break;
			}
			case DARK_RES:
			case DARK_POWER:
			{
				setAttributes(new AttributeHolder(AttributeType.DARK, (int) template.getValue()));
				break;
			}
		}
		if (_funcTemplates == null)
		{
			_funcTemplates = new EnumMap<>(Stat.class);
		}
		if (_funcTemplates.put(template.getStat(), template) != null)
		{
			LOGGER.warning("Item with id " + _itemId + " has 2 func templates with same stat: " + template.getStat());
		}
	}
	
	public void attachCondition(Condition c)
	{
		if (_preConditions == null)
		{
			_preConditions = new ArrayList<>();
		}
		_preConditions.add(c);
	}
	
	public List<Condition> getConditions()
	{
		return _preConditions;
	}
	
	/**
	 * Method to retrieve skills linked to this item armor and weapon: passive skills etcitem: skills used on item use <-- ???
	 *
	 * @return Skills linked to this item as SkillHolder[]
	 */
	public List<ItemSkillHolder> getAllSkills()
	{
		return _skills;
	}
	
	/**
	 * @param condition
	 * @return {@code List} of {@link ItemSkillHolder} if item has skills and matches the condition, {@code null} otherwise
	 */
	public List<ItemSkillHolder> getSkills(Predicate<ItemSkillHolder> condition)
	{
		if (_skills == null)
		{
			return null;
		}
		final List<ItemSkillHolder> result = new ArrayList<>();
		for (ItemSkillHolder skill : _skills)
		{
			if (condition.test(skill))
			{
				result.add(skill);
			}
		}
		return result;
	}
	
	/**
	 * @param type
	 * @return {@code List} of {@link ItemSkillHolder} if item has skills, {@code null} otherwise
	 */
	public List<ItemSkillHolder> getSkills(ItemSkillType type)
	{
		if (_skills == null)
		{
			return null;
		}
		final List<ItemSkillHolder> result = new ArrayList<>();
		for (ItemSkillHolder skill : _skills)
		{
			if (skill.getType() == type)
			{
				result.add(skill);
			}
		}
		return result;
	}
	
	/**
	 * Executes the action on each item skill with the specified type (If there are skills at all)
	 *
	 * @param type
	 * @param action
	 */
	public void forEachSkill(ItemSkillType type, Consumer<ItemSkillHolder> action)
	{
		if (_skills != null)
		{
			for (ItemSkillHolder skill : _skills)
			{
				if (skill.getType() == type)
				{
					action.accept(skill);
				}
			}
		}
	}
	
	public void addSkill(ItemSkillHolder holder)
	{
		if (_skills == null)
		{
			_skills = new ArrayList<>();
		}
		_skills.add(holder);
	}
	
	public boolean checkCondition(Creature creature, WorldObject object, boolean sendMessage)
	{
		if (creature.canOverrideCond(PlayerCondOverride.ITEM_CONDITIONS) && !Config.GM_ITEM_RESTRICTION)
		{
			return true;
		}
		// Don't allow hero equipment and restricted items during Olympiad
		if ((isOlyRestrictedItem() || _heroItem) && (creature.isPlayer() && creature.getActingPlayer().isInOlympiadMode()))
		{
			if (sendMessage)
			{
				if (isEquipable())
				{
					creature.sendPacket(SystemMessageId.THE_ITEM_CANNOT_BE_EQUIPPED_IN_THE_OLYMPIAD);
				}
				else
				{
					creature.sendPacket(SystemMessageId.THE_ITEM_CANNOT_BE_USED_IN_THE_OLYMPIAD);
				}
			}
			return false;
		}
		if (_isCocRestricted && (creature.isPlayer() && (creature.getActingPlayer().isOnEvent(CeremonyOfChaosEvent.class))))
		{
			creature.sendPacket(SystemMessageId.YOU_CANNOT_USE_THIS_ITEM_IN_THE_TOURNAMENT);
			return false;
		}
		if (!isConditionAttached())
		{
			return true;
		}
		final Creature target = object.isCreature() ? (Creature) object : null;
		for (Condition preCondition : _preConditions)
		{
			if (preCondition == null)
			{
				continue;
			}
			if (!preCondition.test(creature, target, null, null))
			{
				if (creature.isSummon())
				{
					creature.sendPacket(SystemMessageId.THIS_PET_CANNOT_USE_THIS_ITEM);
					return false;
				}
				if (sendMessage)
				{
					final String msg = preCondition.getMessage();
					final int msgId = preCondition.getMessageId();
					if (msg != null)
					{
						creature.sendMessage(msg);
					}
					else if (msgId != 0)
					{
						final SystemMessage sm = new SystemMessage(msgId);
						if (preCondition.isAddName())
						{
							sm.addItemName(_itemId);
						}
						creature.sendPacket(sm);
					}
				}
				return false;
			}
		}
		return true;
	}
	
	public boolean isConditionAttached()
	{
		return (_preConditions != null) && !_preConditions.isEmpty();
	}
	
	public boolean isQuestItem()
	{
		return _questItem;
	}
	
	public boolean isFreightable()
	{
		if (_itemId == 94896)
		{ // Sylphen Ring
			return true;
		}
		if (getTime() > 0)
		{
			return false;
		}
		return (_freightable || isEquipable()) /* && !isAdenWeapon() */;
	}
	
	public boolean isAllowSelfResurrection()
	{
		return _allowSelfResurrection;
	}
	
	public boolean isOlyRestrictedItem()
	{
		return _isOlyRestricted || Config.OLY_RESTRICTED_ITEMS_LIST.contains(_itemId);
	}
	
	/**
	 * @return {@code true} if item cannot be used in Ceremony of Chaos games.
	 */
	public boolean isCocRestrictedItem()
	{
		return _isCocRestricted;
	}
	
	public boolean isForNpc()
	{
		return _forNpc;
	}
	
	public boolean isAppearanceable()
	{
		return _isAppearanceable;
	}
	
	/**
	 * @return {@code true} if the item is blessed, {@code false} otherwise.
	 */
	public boolean isBlessed()
	{
		return _isBlessed;
	}
	
	public int getArtifactSlot()
	{
		return _artifactSlot;
	}
	
	/**
	 * Returns the name of the item followed by the item ID.
	 *
	 * @return the name and the ID of the item
	 */
	@Override
	public String toString()
	{
		return _name + "(" + _itemId + ")";
	}
	
	/**
	 * Verifies if the item has effects immediately.<br>
	 * <i>Used for herbs mostly.</i>
	 *
	 * @return {@code true} if the item applies effects immediately, {@code false} otherwise
	 */
	public boolean hasExImmediateEffect()
	{
		return _exImmediateEffect;
	}
	
	/**
	 * Verifies if the item has effects immediately.
	 *
	 * @return {@code true} if the item applies effects immediately, {@code false} otherwise
	 */
	public boolean hasImmediateEffect()
	{
		return _immediateEffect;
	}
	
	/**
	 * @return the _default_action
	 */
	public ActionType getDefaultAction()
	{
		return _defaultAction;
	}
	
	public int useSkillDisTime()
	{
		return _useSkillDisTime;
	}
	
	/**
	 * Gets the item reuse delay time in seconds.
	 *
	 * @return the reuse delay time
	 */
	public int getReuseDelay()
	{
		return _reuseDelay;
	}
	
	/**
	 * Gets the shared reuse group.<br>
	 * Items with the same reuse group will render reuse delay upon those items when used.
	 *
	 * @return the shared reuse group
	 */
	public int getSharedReuseGroup()
	{
		return _sharedReuseGroup;
	}
	
	public CommissionItemType getCommissionItemType()
	{
		return _commissionItemType;
	}
	
	/**
	 * Usable in HTML windows.
	 *
	 * @return the icon link in client files
	 */
	public String getIcon()
	{
		return _icon;
	}
	
	public AutoUseItemType getShortcutToggleType()
	{
		return _shortcutToggleType;
	}
	
	public int getDefaultEnchantLevel()
	{
		return _defaultEnchantLevel;
	}
	
	public boolean isPetItem()
	{
		return getItemType() == EtcItemType.PET_COLLAR;
	}
	
	/**
	 * @param extractableProduct
	 */
	public void addCapsuledItem(ExtractableProduct extractableProduct)
	{}
	
	public double getStats(Stat stat, double defaultValue)
	{
		if (_funcTemplates != null)
		{
			final FuncTemplate template = _funcTemplates.get(stat);
			if ((template != null) && ((template.getFunctionClass() == FuncAdd.class) || (template.getFunctionClass() == FuncSet.class)))
			{
				return template.getValue();
			}
		}
		return defaultValue;
	}
	
	public int getUsageLimit()
	{
		return _usageLimit;
	}
	
	public boolean isAdenWeapon()
	{
		return ((getId() == 94897) || (getId() == 95691)) || ((getId() >= 93028) && (getId() <= 93037));
	}
	
	public boolean isSpellbook4Stars()
	{
		boolean isBook4Stars = false;
		switch (_itemId)
		{
			case 93387: // Spellbook: Ultimate Death Knight Transformation
			case 94498: // Spellbook: Titan Champion
			case 94508: // Spellbook: Cacophony of War
			case 94668: // Spellbook: Pa'agrio's Touch
			case 94673: // Spellbook: Team Building
			case 94861: // Spellbook: Tranquility
			case 94862: // Spellbook: Exclusion
			case 94863: // Spellbook: Dark Disruption
			case 95346: // Spellbook: Meteor
			case 95347: // Spellbook: Rolling Dice
			case 95354: // Spellbook: Dragon Strike
			case 95861: // Spellbook: Mechanical Hero
			case 96289: // Spellbook: Phoenix Shield
			case 96291: // Spellbook: Hell
			case 96293: // Spellbook: Shelter
			case 96295: // Spellbook: Condemnation
			case 96670: // Spellbook: Critical Assault
			case 96671: // Spellbook: Fury Blade
			case 96672: // Spellbook: Mortal Piercing
			case 96873: // Spellbook: Legendary Archer
			case 97111: // Spellbook: Wild Evolution
			case 97182: // Spellbook: Cat Emperor
			case 97183: // Spellbook: Unicorn Guard
			case 97184: // Spellbook: Lord Raise
			case 97318: // Spellbook: Challenger
			case 97323: // Spellbook: Great Sweep
			case 97453: // Spellbook: Overwhelming Power
			case 97465: // Spellbook: Leopold
			case 97831: // Spellbook: Amadeus
			case 97832: // Spellbook: Flamenco
			case 97837: // Spellbook: Crusader Transformation
			case 97845: // Spellbook: Amadeus - Sealed
			case 97846: // Spellbook: Flamenco - Sealed
			case 97851: // Spellbook: Crusader Transformation - Sealed
			case 97454: // Spellbook: Overwhelming Power - Sealed
			case 97466: // Spellbook: Leopold - Sealed
			case 97328: // Spellbook: Challenger - Sealed
			case 97333: // Spellbook: Great Sweep - Sealed
			case 97119: // Spellbook: Wild Evolution - Sealed
			case 97191: // Spellbook: Cat Emperor - Sealed
			case 97192: // Spellbook: Unicorn Guard - Sealed
			case 97193: // Spellbook: Lord Raise - Sealed
			case 95355: // Spellbook: Dragon Strike - Sealed
			case 95507: // Spellbook: Ultimate Death Knight Transformation - Sealed
			case 95541: // Spellbook: Titan Champion - Sealed
			case 95551: // Spellbook: Cacophony of War - Sealed
			case 95552: // Spellbook: Pa'agrio's Touch - Sealed
			case 95557: // Spellbook: Team Building - Sealed
			case 95562: // Spellbook: Tranquility - Sealed
			case 95563: // Spellbook: Exclusion - Sealed
			case 95564: // Spellbook: Dark Disruption - Sealed
			case 95565: // Spellbook: Meteor - Sealed
			case 95566: // Spellbook: Rolling Dice - Sealed
			case 95869: // Spellbook: Mechanical Hero - Sealed
			case 96290: // Spellbook: Phoenix Shield - Sealed
			case 96292: // Spellbook: Hell - Sealed
			case 96294: // Spellbook: Shelter - Sealed
			case 96296: // Spellbook: Condemnation - Sealed
			case 96682: // Spellbook: Critical Assault - Sealed
			case 96683: // Spellbook: Fury Blade - Sealed
			case 96684: // Spellbook: Mortal Piercing - Sealed
			case 96887: // Spellbook: Legendary Archer - Sealed
				isBook4Stars = true;
				break;
		}
		return isBook4Stars;
	}
	
	public boolean isSpellbook3Stars()
	{
		boolean isBook3Stars = false;
		switch (_itemId)
		{
			// Sealed Spellbooks from Spellbook Coupon - 3 Stars
			case 97847: // Spellbook: Music Mastery - Sealed
			case 97848: // Spellbook: Dance Mastery - Sealed
			case 97849: // Spellbook: Song of Silence Lv. 4 - Sealed
			case 97850: // Spellbook: Dance of Medusa Lv. 4 - Sealed
			case 97852: // Spellbook: Unique Seal of Damage - Sealed
			case 97854: // Spellbook: Essential Energy - Sealed
			case 97856: // Spellbook: Great Attack Lv. 3 - Sealed
			case 97464: // Spellbook: Death Pain - Sealed
			case 97472: // Spellbook: Lightning Tornado - Sealed
			case 97468: // Spellbook: Resonance - Sealed
			case 97460: // Spellbook: Soul Guard Lv. 3 - Sealed
			case 97329: // Spellbook: Blade Wave - Sealed
			case 97330: // Spellbook: Slashing Blades - Sealed
			case 97334: // Spellbook: Spear Cage - Sealed
			case 97335: // Spellbook: Spear Rumble - Sealed
			case 97112: // Spellbook: Master of Combat Lv. 3 - Sealed
			case 97116: // Spellbook: Wild Growl Lv. 2 - Sealed
			case 97117: // Spellbook: Darkness Unleashing - Sealed
			case 97194: // Spellbook: Cubic of Secrets Lv. 2 - Sealed
			case 97195: // Spellbook: Elemental Cubic Lv. 2 - Sealed
			case 97196: // Spellbook: Phantom Cubic Lv. 2 - Sealed
			case 97197: // Spellbook: Ray of Light - Sealed
			case 97232: // Spellbook: Giant's Stomp - Sealed
			case 95351: // Spellbook: Goring Charge - Sealed
			case 95353: // Spellbook: Elemental Changing - Sealed
			case 95366: // Spellbook: Elemental Spirit Lv. 4 - Sealed
			case 95503: // Spellbook: Divine Beam - Sealed
			case 95504: // Spellbook: Burning Field - Sealed
			case 95505: // Spellbook: Frozen Field - Sealed
			case 95506: // Spellbook: Lightning Storm - Sealed
			case 95510: // Spellbook: Hellfire - Sealed
			case 95513: // Spellbook: Elemental Burst - Sealed
			case 95514: // Spellbook: Ethereal Strike - Sealed
			case 95519: // Spellbook: Devour - Sealed
			case 95529: // Spellbook: Song of Earth Lv. 2 - Sealed
			case 95530: // Spellbook: Song of Hunter Lv. 2 - Sealed
			case 95531: // Spellbook: Dance of Warrior Lv. 2 - Sealed
			case 95532: // Spellbook: Dance of Fire Lv. 2 - Sealed
			case 95540: // Spellbook: Life Force Harmony - Sealed
			case 97456: // Spellbook: Soul Mark - Sealed
			case 95542: // Spellbook: Titanic Weapon - Sealed
			case 95543: // Spellbook: Titanic Sweep - Sealed
			case 95544: // Spellbook: Titanic Break - Sealed
			case 95545: // Spellbook: Ogre Spirit Totem Lv. 2 - Sealed
			case 95546: // Spellbook: Rabbit Spirit Totem Lv. 2 - Sealed
			case 95547: // Spellbook: Puma Spirit Totem Lv. 2 - Sealed
			case 95548: // Spellbook: Burning Strike - Sealed
			case 95550: // Spellbook: Armor Break - Sealed
			case 95554: // Spellbook: Debuff Master - Sealed
			case 95555: // Spellbook: Flame Burst - Sealed
			case 95558: // Spellbook: Blood Bond - Sealed
			case 95567: // Spellbook: Song of Wind Lv. 2 - Sealed
			case 95568: // Spellbook: Dance of Fury Lv. 2 - Sealed
			case 95640: // Spellbook: Extra Hit - Sealed
			case 95864: // Spellbook: Fortune Time - Sealed
			case 95870: // Spellbook: Mechanical Masterpiece - Sealed
			case 95871: // Spellbook: Blacksmith's Attack - Sealed
			case 96051: // Spellbook: Sensation - Sealed
			case 96052: // Spellbook: Balanced Body - Sealed
			case 96054: // Spellbook: Treatment - Sealed
			case 96055: // Spellbook: Eva's Nocturne - Sealed
			case 96057: // Spellbook: Duress - Sealed
			case 96058: // Spellbook: Nemesis - Sealed
			case 96284: // Spellbook: Power of Life - Sealed
			case 96286: // Spellbook: Toughness - Sealed
			case 96288: // Spellbook: Ultimate Defense Lv. 3 - Sealed
			case 96606: // Spellbook: Shield of Security - Sealed
			case 96608: // Spellbook: Lunatic Crusher - Sealed
			case 96610: // Spellbook: Eva's Care - Sealed
			case 96612: // Spellbook: Shillien's Curse - Sealed
			case 96677: // Spellbook: Seclusion - Sealed
			case 96678: // Spellbook: Dark Blow - Sealed
			case 96679: // Spellbook: Reversed Pull - Sealed
			case 96680: // Spellbook: Synchro Freedom - Sealed
			case 96681: // Spellbook: Ethereal Blood - Sealed
			case 96888: // Spellbook: True Aim - Sealed
			case 96889: // Spellbook: Deadly Shooter - Sealed
			case 96891: // Spellbook: Chain Arrest - Sealed
			case 96892: // Spellbook: Flame Arrow - Sealed
			case 96893: // Spellbook: Arrow Shower - Sealed
			case 96894: // Spellbook: Piercing Arrow - Sealed
			case 97042: // Spellbook: Faery Shield - Sealed
			case 97043: // Spellbook: Soul Guardian - Sealed
			case 97044: // Spellbook: Spiral - Sealed
				// Unsealed Spellbooks from Special Craft (Spellbook *** Sub)
			case 96607: // Spellbook: Lunatic Crusher
			case 96605: // Spellbook: Shield of Security
			case 96287: // Spellbook: Ultimate Defense Lv. 3
			case 96611: // Spellbook: Shillien's Curse
			case 96609: // Spellbook: Eva's Care
			case 97104: // Spellbook: Master of Combat Lv. 3
			case 97320: // Spellbook: Slashing Blades
			case 97325: // Spellbook: Spear Rumble
			case 97109: // Spellbook: Darkness Unleashing
			case 97108: // Spellbook: Wild Growl Lv. 2
			case 94500: // Spellbook: Titanic Sweep
			case 94501: // Spellbook: Titanic Break
			case 94497: // Spellbook: Life Force Harmony
			case 94505: // Spellbook: Burning Strike
			case 94507: // Spellbook: Armor Break
			case 94503: // Spellbook: Rabbit Spirit Totem
			case 94502: // Spellbook: Ogre Spirit Totem
			case 94504: // Spellbook: Puma Spirit Totem
			case 95862: // Spellbook: Mechanical Masterpiece
			case 96038: // Spellbook: Balanced Body
			case 95863: // Spellbook: Blacksmith's Attack
			case 95856: // Spellbook: Fortune Time
			case 97471: // Spellbook: Lightning Tornado
			case 97467: // Spellbook: Resonance
			case 97455: // Spellbook: Soul Mark
			case 97459: // Spellbook: Soul Guard Lv. 3
			case 93386: // Spellbook: Lightning Storm
			case 93384: // Spellbook: Burning Field
			case 96666: // Spellbook: Dark Blow
			case 96667: // Spellbook: Reversed Pull
			case 96668: // Spellbook: Synchro Freedom
			case 96669: // Spellbook: Ethereal Blood
			case 96879: // Spellbook: Arrow Shower
			case 96877: // Spellbook: Chain Arrest
			case 96878: // Spellbook: Flame Arrow
			case 96880: // Spellbook: Piercing Arrow
			case 93385: // Spellbook: Frozen Field
			case 94137: // Spellbook: Devour
			case 93631: // Spellbook: Hellfire
			case 95356: // Spellbook: Extra Hit
			case 95365: // Spellbook: Elemental Spirit Lv. 4
			case 95352: // Spellbook: Elemental Changing
			case 93869: // Spellbook: Elemental Burst
			case 97039: // Spellbook: Spiral
			case 97188: // Spellbook: Ray of Light
			case 93870: // Spellbook: Ethereal Strike
			case 97840: // Spellbook: Essential Energy
			case 97842: // Spellbook: Great Attack Lv. 3
			case 97834: // Spellbook: Dance Mastery
			case 97833: // Spellbook: Music Mastery
			case 96044: // Spellbook: Nemesis
			case 96041: // Spellbook: Eva's Nocturne
			case 94670: // Spellbook: Debuff Master
			case 94674: // Spellbook: Blood Bond
			case 94671: // Spellbook: Flame Burst
			case 96283: // Spellbook: Power of Life
			case 96285: // Spellbook: Toughness
			case 97319: // Spellbook: Blade Wave
			case 97324: // Spellbook: Spear Cage
			case 97102: // Spellbook: Giant's Stomp
			case 94499: // Spellbook: Titanic Weapon
			case 97463: // Spellbook: Death Pain
			case 96665: // Spellbook: Seclusion
			case 96875: // Spellbook: Deadly Shooter
			case 96874: // Spellbook: True Aim
			case 97838: // Spellbook: Unique Seal of Damage
			case 97835: // Spellbook: Song of Silence Lv. 4
			case 97836: // Spellbook: Dance of Medusa Lv. 4
			case 94326: // Spellbook: Dance of Warrior
			case 94327: // Spellbook: Dance of Fire
			case 95349: // Spellbook: Dance of Fury
			case 94324: // Spellbook: Song of Earth
			case 95348: // Spellbook: Song of Wind
			case 94325: // Spellbook: Song of Hunter
			case 95350: // Spellbook: Goring Charge
			case 97185: // Spellbook: Cubic of Secrets Lv. 2
			case 97186: // Spellbook: Elemental Cubic Lv. 2
			case 97187: // Spellbook: Phantom Cubic Lv. 2
			case 97037: // Spellbook: Faery Shield
			case 97038: // Spellbook: Soul Guardian
			case 96040: // Spellbook: Treatment
			case 96037: // Spellbook: Sensation
			case 96043: // Spellbook: Duress
			case 93103: // Spellbook: Divine Beam
				isBook3Stars = true;
				break;
		}
		return isBook3Stars;
	}
	
	public boolean isSpellbook2Stars()
	{
		boolean isBook2Stars = false;
		switch (_itemId)
		{
			// Sealed Spellbooks from Spellbook Coupon - 2 Stars
			case 97853: // Spellbook: Fatal Crush - Sealed
			case 97855: // Spellbook: Tower Shield Lv. 2 - Sealed
			case 97857: // Spellbook: Perfect Abilities Lv. 3 - Sealed
			case 97803: // Spellbook: Extended Life - Sealed
			case 97331: // Spellbook: Blade Throw - Sealed
			case 97332: // Spellbook: Sword Spirit Lv. 2 - Sealed
			case 97336: // Spellbook: Unleashed Power - Sealed
			case 97337: // Spellbook: Anger Lv. 2 - Sealed
			case 97113: // Spellbook: Final Secret Lv. 2 - Sealed
			case 97115: // Spellbook: Threatening Swing - Sealed
			case 97118: // Spellbook: Wild Assault - Sealed
			case 97198: // Spellbook: Chains of Pain - Sealed
			case 97199: // Spellbook: Servitor Ultimate Defense Lv. 3 - Sealed
			case 97474: // Spellbook: Lightning Mastery - Sealed
			case 97462: // Spellbook: Shock Impact - Sealed
			case 97458: // Spellbook: Powerful Disarm - Sealed
			case 97470: // Spellbook: Fragarach - Sealed
			case 95500: // Spellbook: Light Master Lv. 2 - Sealed
			case 95501: // Spellbook: Shadow Master Lv. 2 - Sealed
			case 95498: // Spellbook: Collect Light Souls Lv. 2 - Sealed
			case 95499: // Spellbook: Collect Shadow Souls Lv. 2 - Sealed
			case 95549: // Spellbook: Burning Assault - Sealed
			case 95556: // Spellbook: Seal of Infection - Sealed
			case 95559: // Spellbook: Cold Flames - Sealed
			case 95561: // Spellbook: Flying Dagger - Sealed
			case 95358: // Spellbook: Time Burst Lv. 2 - Sealed
			case 95360: // Spellbook: Elemental Wind Lv. 2 - Sealed
			case 95655: // Spellbook: Elemental Care Lv. 2 - Sealed
			case 95364: // Spellbook: Elemental Spirit Lv. 3 - Sealed
			case 95865: // Spellbook: Golden Stone - Sealed
			case 95866: // Spellbook: Crushing Leap - Sealed
			case 95867: // Spellbook: Adena Stun - Sealed
			case 95868: // Spellbook: Life Gain - Sealed
			case 96053: // Spellbook: Advanced Cleanse - Sealed
			case 96056: // Spellbook: Eva's Power - Sealed
			case 96059: // Spellbook: Shillien's Power - Sealed
			case 96060: // Spellbook: Divine Recovery - Sealed
			case 96061: // Spellbook: Sacred Power - Sealed
			case 96062: // Spellbook: Improved Resilience - Sealed
			case 96064: // Spellbook: Holy Buster - Sealed
			case 96063: // Spellbook: Unleashing - Sealed
			case 97746: // Spellbook: Master of Aggression - Sealed
			case 96276: // Spellbook: Knight's Power - Sealed
			case 96278: // Spellbook: Knight's Assault - Sealed
			case 96280: // Spellbook: Chain Strike (Shock) - Sealed
			case 96282: // Spellbook: Shield Throwing - Sealed
			case 96674: // Spellbook: Fatal Accuracy - Sealed
			case 96675: // Spellbook: Shadow Assault - Sealed
			case 96676: // Spellbook: Rogue Mastery - Sealed
			case 95524: // Spellbook: Sword Symphony - Sealed
			case 95525: // Spellbook: Dance of Blood - Sealed
			case 96896: // Spellbook: Steady Aim - Sealed
			case 96897: // Spellbook: Alacrity - Sealed
			case 96898: // Spellbook: Eagle Eye - Sealed
			case 96899: // Spellbook: Dexterous Body - Sealed
			case 96890: // Spellbook: Thorn Shot - Sealed
			case 96895: // Spellbook: Devious Shot - Sealed
			case 97045: // Spellbook: Magic Circle - Sealed
			case 97046: // Spellbook: Heavy Sleep - Sealed
				// Unsealed Spellbooks from Special Craft (Spellbook ** Sub)
			case 97839: // Spellbook: Fatal Crush
			case 97841: // Spellbook: Tower Shield Lv. 2
			case 97843: // Spellbook: Perfect Abilities Lv. 3
			case 97802: // Spellbook: Extended Life
			case 97321: // Spellbook: Blade Throw
			case 97322: // Spellbook: Sword Spirit Lv. 2
			case 97326: // Spellbook: Unleashed Power
			case 97327: // Spellbook: Anger Lv. 2
			case 97105: // Spellbook: Final Secret Lv. 2
			case 97107: // Spellbook: Threatening Swing
			case 97110: // Spellbook: Wild Assault
			case 97189: // Spellbook: Chains of Pain
			case 97190: // Spellbook: Servitor Ultimate Defense Lv. 3
			case 97473: // Spellbook: Lightning Mastery
			case 97461: // Spellbook: Shock Impact
			case 97457: // Spellbook: Powerful Disarm
			case 97469: // Spellbook: Fragarach
			case 91944: // Spellbook: Light Master Lv. 2
			case 91945: // Spellbook: Shadow Master Lv. 2
			case 91942: // Spellbook: Collect Light Souls Lv. 2
			case 91943: // Spellbook: Collect Shadow Souls Lv. 2
			case 94506: // Spellbook: Burning Assault
			case 94672: // Spellbook: Seal of Infection
			case 94675: // Spellbook: Cold Flames
			case 94806: // Spellbook: Flying Dagger
			case 95357: // Spellbook: Time Burst Lv. 2
			case 95359: // Spellbook: Elemental Wind Lv. 2
			case 95654: // Spellbook: Elemental Care Lv. 2
			case 95363: // Spellbook: Elemental Spirit Lv. 3
			case 95857: // Spellbook: Golden Stone
			case 95858: // Spellbook: Crushing Leap
			case 95859: // Spellbook: Adena Stun
			case 95860: // Spellbook: Life Gain
			case 96039: // Spellbook: Advanced Cleanse
			case 96042: // Spellbook: Eva's Power
			case 96045: // Spellbook: Shillien's Power
			case 96046: // Spellbook: Divine Recovery
			case 96047: // Spellbook: Sacred Power
			case 96048: // Spellbook: Improved Resilience
			case 96049: // Spellbook: Holy Buster
			case 96050: // Spellbook: Unleashing
			case 97745: // Spellbook: Master of Aggression
			case 96275: // Spellbook: Knight's Power
			case 96277: // Spellbook: Knight's Assault
			case 96279: // Spellbook: Chain Strike (Shock)
			case 96281: // Spellbook: Shield Throwing
			case 96662: // Spellbook: Fatal Accuracy
			case 96663: // Spellbook: Shadow Assault
			case 96664: // Spellbook: Rogue Mastery
			case 94319: // Spellbook: Sword Symphony
			case 94320: // Spellbook: Dance of Blood
			case 96882: // Spellbook: Steady Aim
			case 96883: // Spellbook: Alacrity
			case 96884: // Spellbook: Eagle Eye
			case 96885: // Spellbook: Dexterous Body
			case 96876: // Spellbook: Thorn Shot
			case 96881: // Spellbook: Devious Shot
			case 97040: // Spellbook: Magic Circle
			case 97041: // Spellbook: Heavy Sleep
				isBook2Stars = true;
				break;
		}
		return isBook2Stars;
	}
	
	public boolean isSpellbook1Stars()
	{
		boolean isBook1Stars = false;
		switch (_itemId)
		{
			// Sealed Spellbooks from Spellbook Coupon - 1 Star
			case 97858: // Spellbook: Improved Sleep - Sealed
			case 97114: // Spellbook: Wild Charge - Sealed
			case 95508: // Spellbook: Death Guard - Sealed
			case 95509: // Spellbook: Bone Cage - Sealed
			case 95511: // Spellbook: Drain Magic Energy - Sealed
			case 95528: // Spellbook: Frantic Pace - Sealed
			case 95512: // Spellbook: Amazing Thunder Storm Mastery - Sealed
			case 95515: // Spellbook: Knight's Help - Sealed
			case 95516: // Spellbook: Amazing Spike Thrust Mastery - Sealed
			case 95553: // Spellbook: Pa'agrio's Cure - Sealed
			case 95560: // Spellbook: Chant of Healing - Sealed
			case 96274: // Spellbook: Masterpiece Shield - Sealed
			case 96673: // Spellbook: Fast Run Lv. 3 - Sealed
			case 96900: // Spellbook: Perfect Mastery - Sealed
			case 95520: // Spellbook: Pain of Sagittarius - Sealed
			case 95521: // Spellbook: Esprit - Sealed
			case 95522: // Spellbook: Dark Sense - Sealed
			case 95523: // Spellbook: Buff Thief - Sealed
			case 95526: // Spellbook: Wild Beat - Sealed
			case 95527: // Spellbook: Crazy Waltz - Sealed
				// Unsealed Spellbooks from Special Craft (Spellbook * Sub)
			case 97844: // Spellbook: Improved Sleep
			case 97106: // Spellbook: Wild Charge
			case 93388: // Spellbook: Death Guard
			case 93392: // Spellbook: Bone Cage
			case 93633: // Spellbook: Drain Magic Energy
			case 94323: // Spellbook: Frantic Pace
			case 93867: // Spellbook: Amazing Thunder Storm Mastery
			case 93871: // Spellbook: Knight's Help
			case 94134: // Spellbook: Amazing Spike Thrust Mastery
			case 94669: // Spellbook: Pa'agrio's Cure
			case 94676: // Spellbook: Chant of Healing
			case 96273: // Spellbook: Masterpiece Shield
			case 96661: // Spellbook: Fast Run Lv. 3
			case 96886: // Spellbook: Perfect Mastery
			case 94138: // Spellbook: Pain of Sagittarius
			case 94139: // Spellbook: Esprit
			case 94140: // Spellbook: Dark Sense
			case 94141: // Spellbook: Buff Thief
			case 94321: // Spellbook: Wild Beat
			case 94322: // Spellbook: Crazy Waltz
				isBook1Stars = true;
				break;
		}
		return isBook1Stars;
	}
	
	public boolean isMasterBooks()
	{
		boolean isMasterBooks = false;
		switch (_itemId)
		{
			case 95769: // Master's Book: Berserker Spirit Sealed
			case 95517: // Master's Book: Might - Sealed
			case 95518: // Master's Book: Shield - Sealed
			case 95533: // Master's Book: Empower - Sealed
			case 95534: // Master's Book: Magic Barrier - Sealed
			case 95535: // Master's Book: Focus - Sealed
			case 95536: // Master's Book: Death Whisper - Sealed
			case 95537: // Master's Book: Wind Walk - Sealed
			case 95770: // Master's Book: Clarity - Sealed
			case 95771: // Master's Book: Wild Magic - Sealed
			case 95538: // Master's Book: Haste - Sealed
			case 95539: // Master's Book: Acumen - Sealed
				// Unsealed Spellbooks from Special Craft (Spellbook *** Sub)
			case 94366: // Master's Book: Berserker Spirit
			case 94135: // Master's Book: Might
			case 94136: // Master's Book: Shield
			case 94361: // Master's Book: Empower
			case 94362: // Master's Book: Magic Barrier
			case 94363: // Master's Book: Focus
			case 94364: // Master's Book: Death Whisper
			case 94365: // Master's Book: Wind Walk
			case 94367: // Master's Book: Clarity
			case 94368: // Master's Book: Wild Magic
			case 94369: // Master's Book: Haste
			case 94370: // Master's Book: Acumen
				isMasterBooks = true;
				break;
		}
		return isMasterBooks;
	}
	
	public boolean isDoll1Stars()
	{
		boolean isDoll1Stars = false;
		switch (_itemId)
		{
			case 94153: // Baium Doll Lv. 1
			case 94154: // Queen Ant Doll Lv. 1
			case 94155: // Orfen Doll Lv. 1
			case 94509: // Frintezza Doll Lv. 1
			case 94645: // Zaken Doll Lv. 1
			case 94648: // Core Doll Lv. 1
			case 96601: // Antharas Doll Lv. 1
			case 97479: // Behemoth doll Lv. 1
			case 97484: // Glakias doll Lv. 1
				isDoll1Stars = true;
				break;
		}
		return isDoll1Stars;
	}
	
	public boolean isDoll1Pack()
	{
		return _itemId == 160035;
	}
	
	public boolean isDoll2Stars()
	{
		boolean isDoll2Stars = false;
		switch (_itemId)
		{
			case 91256: // Baium Doll Lv. 2
			case 91257: // Queen Ant Doll Lv. 2
			case 91258: // Orfen Doll Lv. 2
			case 91604: // Frintezza Doll Lv. 2
			case 94646: // Zaken Doll Lv. 2
			case 94649: // Core Doll Lv. 2
			case 96602: // Antharas Doll Lv. 2
			case 97480: // Behemoth doll Lv. 2
			case 97485: // Glakias doll Lv. 2
				isDoll2Stars = true;
				break;
		}
		return isDoll2Stars;
	}
	
	public boolean isDoll2Pack()
	{
		return _itemId == 160036;
	}
	
	public boolean isDoll3Stars()
	{
		boolean isDoll3Stars = false;
		switch (_itemId)
		{
			case 91423: // Baium Doll Lv. 3
			case 91422: // Queen Ant Doll Lv. 3
			case 91424: // Orfen Doll Lv. 3
			case 91605: // Frintezza Doll Lv. 3
			case 94647: // Zaken Doll Lv. 3
			case 94650: // Core Doll Lv. 3
			case 96603: // Antharas Doll Lv. 3
			case 97481: // Behemoth doll Lv. 3
			case 97486: // Glakias doll Lv. 3
				isDoll3Stars = true;
				break;
		}
		return isDoll3Stars;
	}
	
	public boolean isDoll4Stars()
	{
		boolean isDoll4Stars = false;
		switch (_itemId)
		{
			case 96631: // Baium Doll Lv. 4
			case 96632: // Queen Ant Doll Lv. 4
			case 96633: // Orfen Doll Lv. 4
			case 96636: // Frintezza Doll Lv. 4
			case 96634: // Zaken Doll Lv. 4
			case 96635: // Core Doll Lv. 4
			case 96637: // Antharas Doll Lv. 4
			case 97482: // Behemoth doll Lv. 4
			case 97487: // Glakias doll Lv. 4
				isDoll4Stars = true;
				break;
		}
		return isDoll4Stars;
	}
	
	public boolean isDoll5Stars()
	{
		boolean isDoll5Stars = false;
		switch (_itemId)
		{
			case 96639: // Baium Doll Lv. 5
			case 96638: // Queen Ant Doll Lv. 5
			case 96640: // Orfen Doll Lv. 5
			case 96643: // Frintezza Doll Lv. 5
			case 96641: // Zaken Doll Lv. 5
			case 96642: // Core Doll Lv. 5
			case 96644: // Antharas Doll Lv. 5
			case 97483: // Behemoth doll Lv. 5
			case 97488: // Glakias doll Lv. 5
				isDoll5Stars = true;
				break;
		}
		return isDoll5Stars;
	}
	
	public boolean isArmorProtections()
	{
		boolean isArmorProtections = false;
		switch (_itemId)
		{
			case 94840: // Breastplate of Protection
			case 94841: // Gaiters of Protection
			case 94844: // Leather Armor of Protection
			case 94845: // Leather Leggings of Protection
			case 94848: // Tunic of Protection
			case 94849: // Stockings of Protection
			case 95932: // Helmet of Protection
			case 95933: // Gauntlets of Protection
			case 95934: // Boots of Protection
			case 95663: // Sigil of Protection
			case 95665: // Shield of Protection
				isArmorProtections = true;
				break;
		}
		return isArmorProtections;
	}
	
	public boolean isItemsNoDropFromBaium()
	{
		boolean isItemsNoDropFromBaium = false;
		switch (_itemId)
		{
			case 49580: // Baium's Ring
			case 91256: // Baium Doll Lv. 2
			case 49683: // Talisman of Baium
			case 92408: // Baium's Thunder Breaker
				isItemsNoDropFromBaium = true;
				break;
		}
		return isItemsNoDropFromBaium;
	}
	
	public boolean isArmorThan()
	{
		boolean isArmorThan = false;
		switch (_itemId)
		{
			case 93139: // Ice Crystal Breastplate
			case 93140: // Ice Crystal Gaiters
			case 93315: // Leather Armor of Lightning
			case 93316: // Leather Leggings of Lightning
			case 93733: // Flaming Tunic
			case 93734: // Flaming Stockings
				// case 94263: // Stun Gauntlets
				// case 94083: // Helmet of Mana
				// case 94085: // Boots of Evasion
				isArmorThan = true;
				break;
		}
		return isArmorThan;
	}
	
	public boolean isFrostLordItems()
	{
		boolean isFrostLordItems = false;
		switch (_itemId)
		{
			case 96297: // Frost Lord's Weapon Pack (Sealed)
			case 95725: // Frost Lord's Sword
			case 95727: // Frost Lord's Axe
			case 95729: // Frost Lord's Spear
			case 95731: // Frost Lord's Blade Fists
			case 95733: // Frost Lord's Staff
			case 95735: // Frost Lord's Rapier
			case 95737: // Frost Lord's Dual Swords
			case 95726: // Frost Lord's Two-handed Sword
			case 95728: // Frost Lord's Dagger
			case 95730: // Frost Lord's Bow
			case 95732: // Frost Lord's Magic Blunt Weapon
			case 95734: // Frost Lord's Ancient Sword
			case 95736: // Frost Lord's Pistols
				isFrostLordItems = true;
				break;
		}
		return isFrostLordItems;
	}
	
	public boolean isTOPAGradeWeapon()
	{
		boolean isTOPAGradeWeapon = false;
		switch (_itemId)
		{
			case 8678: // Sirra's Blade
			case 8679: // Sword of Ipos
			case 8680: // Barakiel's Axe
			case 8681: // Behemoth' Tuning Fork
			case 8682: // Naga's Storm
			case 8683: // Tiphon's Spear
			case 8684: // Shyeed's Bow
			case 8685: // Sobekk's Hurricane
			case 8686: // Themis' Tongue
			case 8687: // Cabrio's Hand
			case 8688: // Daimon Crystal
			case 8938: // Tallum Blade*Damascus Sword
			case 91887: // Durendal
			case 91902: // Éclair Bijou
			case 94886: // Zephyrus
				isTOPAGradeWeapon = true;
				break;
		}
		return isTOPAGradeWeapon;
	}
	
	public static enum BodyPart
	{
		SLOT_NONE(0x0000),
		SLOT_UNDERWEAR(0x0001),
		SLOT_R_EAR(0x0002),
		SLOT_L_EAR(0x0004),
		SLOT_LR_EAR(0x00006),
		SLOT_NECK(0x0008),
		SLOT_R_FINGER(0x0010),
		SLOT_L_FINGER(0x0020),
		SLOT_LR_FINGER(0x0030),
		SLOT_HEAD(0x0040),
		SLOT_R_HAND(0x0080),
		SLOT_L_HAND(0x0100),
		SLOT_GLOVES(0x0200),
		SLOT_CHEST(0x0400),
		SLOT_LEGS(0x0800),
		SLOT_FEET(0x1000),
		SLOT_BACK(0x2000),
		SLOT_LR_HAND(0x4000),
		SLOT_FULL_ARMOR(0x8000),
		SLOT_HAIR(0x010000),
		SLOT_ALLDRESS(0x020000),
		SLOT_HAIR2(0x040000),
		SLOT_HAIRALL(0x080000),
		SLOT_R_BRACELET(0x100000),
		SLOT_L_BRACELET(0x200000),
		SLOT_DECO(0x400000),
		SLOT_BELT(0x10000000),
		SLOT_BROOCH(0x20000000),
		SLOT_BROOCH_JEWEL(0x40000000),
		SLOT_AGATHION(0x3000000000L),
		SLOT_ARTIFACT_BOOK(0x20000000000L),
		SLOT_ARTIFACT(0x40000000000L),
		SLOT_WOLF(-100),
		SLOT_HATCHLING(-101),
		SLOT_STRIDER(-102),
		SLOT_BABYPET(-103),
		SLOT_GREATWOLF(-104);
		
		private final long _slot;
		
		BodyPart(long slot)
		{
			_slot = slot;
		}
		
		public static BodyPart getBodyPartBySlot(long slot)
		{
			for (BodyPart bodyPart : values())
			{
				if (bodyPart.getSlot() == slot)
				{
					return bodyPart;
				}
			}
			return null;
		}
		
		public long getSlot()
		{
			return _slot;
		}
	}
}
