/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets;

import club.projectessence.Config;
import club.projectessence.gameserver.data.xml.SkillData;
import club.projectessence.gameserver.model.actor.Summon;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.skills.AbnormalType;
import club.projectessence.gameserver.model.skills.Skill;

/**
 * <AUTHOR>
 */
public class RequestDispel extends ClientPacket {
	private int _objectId;
	private int _skillId;
	private int _skillLevel;
	private int _skillSubLevel;

	@Override
	public void readImpl() {
		_objectId = readInt();
		_skillId = readInt();
		_skillLevel = readShort();
		_skillSubLevel = readShort();
	}

	@Override
	public void runImpl() {
		if ((_skillId <= 0) || (_skillLevel <= 0)) {
			return;
		}
		final PlayerInstance player = client.getPlayer();
		if (player == null) {
			return;
		}
		final Skill skill = SkillData.getInstance().getSkill(_skillId, _skillLevel, _skillSubLevel);
		if (skill == null) {
			return;
		}
		if (!skill.canBeDispelled() || skill.isDebuff()) {
			return;
		}
		if (skill.getAbnormalType() == AbnormalType.TRANSFORM) {
			return;
		}
		if (skill.isDance() && !Config.DANCE_CANCEL_BUFF) {
			return;
		}
		if (player.getObjectId() == _objectId) {
			player.stopSkillEffects(true, _skillId);
		} else {
			final Summon pet = player.getPet();
			if ((pet != null) && (pet.getObjectId() == _objectId)) {
				pet.stopSkillEffects(true, _skillId);
			}

			final Summon servitor = player.getServitor(_objectId);
			if (servitor != null) {
				servitor.stopSkillEffects(true, _skillId);
			}
		}
	}
}
