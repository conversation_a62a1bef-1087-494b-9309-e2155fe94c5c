/*
 * This file is part of the L2J Mobius project.
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.handler;

import club.projectessence.gameserver.enums.ChatType;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.items.Item;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.util.Util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Interface for chat handlers
 *
 * <AUTHOR>
 */
public interface IChatHandler {
	static final Pattern ITEM_LINK_PATTERN = Pattern.compile("Type=\\d+ 	ID=(\\d+) 	Color=\\d+ 	Underline=0 	ClassID=\\d+ 	Title=([a-zA-Z0-9ЁёА-я $&+,:;=?@#|'<>.^*()%!-]+)");

	/**
	 * @param activeChar - chatter
	 * @param text       - original message
	 * @return new String[] { textEn, textRu };
	 */
	static String[] getItemLinksTranslation(PlayerInstance activeChar, String text) {
		if (text.indexOf(8) >= 0) {
			String textEn = text;
			String textRu = text;
			Matcher matcher = ITEM_LINK_PATTERN.matcher(text);
			while (matcher.find()) {
				final int objId = Integer.parseInt(matcher.group(1));
				final String title = matcher.group(2);
				ItemInstance it = activeChar.getInventory().getItemByObjectId(objId);
				if (it == null) {
					return new String[]
							{
									text,
									text
							};
				}
				Item item = it.getItem();
				if (Util.containsCyrylic(title)) {
					if (item.getAdditionalName() != null) {
						textEn = textEn.replace(title, (it.isEnchanted() ? ("+" + it.getEnchantLevel() + " ") : "") + item.getName() + " " + item.getAdditionalName());
					} else {
						textEn = textEn.replace(title, (it.isEnchanted() ? ("+" + it.getEnchantLevel() + " ") : "") + item.getName());
					}
				} else {
					if (item.getAdditionalNameRu() != null) {
						textRu = textRu.replace(title, (it.isEnchanted() ? ("+" + it.getEnchantLevel() + " ") : "") + item.getNameRu() + " " + item.getAdditionalNameRu());
					} else {
						textRu = textRu.replace(title, (it.isEnchanted() ? ("+" + it.getEnchantLevel() + " ") : "") + item.getNameRu());
					}
				}
			}
			return new String[]
					{
							textEn,
							textRu
					};
		}
		return new String[]
				{
						text,
						text
				};
	}

	/**
	 * Handles a specific type of chat messages
	 *
	 * @param type
	 * @param player
	 * @param target
	 * @param text
	 * @param sharedPositionId
	 */
	void handleChat(ChatType type, PlayerInstance player, String target, String text, int sharedPositionId);

	void handleRequestInvitePartyPacket(ChatType type, PlayerInstance activeChar, byte requestType);

	/**
	 * Returns a list of all chat types registered to this handler
	 *
	 * @return
	 */
	ChatType[] getChatTypeList();
}