/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.clientpackets.blessing;

import club.projectessence.gameserver.data.xml.BlessItemData;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.actor.request.BlessingRequest;
import club.projectessence.gameserver.model.items.instance.ItemInstance;
import club.projectessence.gameserver.network.SystemMessageId;
import club.projectessence.gameserver.network.clientpackets.ClientPacket;
import club.projectessence.gameserver.network.serverpackets.blessing.ExBlessOptionPutItem;

/**
 * <AUTHOR>
 */
public class ExRequestBlessOptionPutItem extends ClientPacket
{
	private int _itemObjId;
	
	@Override
	public void readImpl()
	{
		_itemObjId = readInt();
	}
	
	@Override
	public void runImpl()
	{
		final PlayerInstance player = client.getPlayer();
		if (player == null)
		{
			return;
		}
		else if (player.isInStoreMode())
		{
			client.sendPacket(SystemMessageId.YOU_CANNOT_DO_THAT_WHILE_IN_A_PRIVATE_STORE_OR_PRIVATE_WORKSHOP);
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		else if (player.isProcessingTransaction() || player.isProcessingRequest())
		{
			client.sendPacket(SystemMessageId.YOU_CANNOT_USE_THIS_SYSTEM_DURING_TRADING_PRIVATE_STORE_AND_WORKSHOP_SETUP);
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		if (player.isAccountLockedDown())
		{
			player.sendMessage("Your account is in lockdown");
			return;
		}
		final BlessingRequest request = player.getRequest(BlessingRequest.class);
		if ((request == null) || request.isProcessing())
		{
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		// Make sure player owns this item.
		request.setItemTwo(_itemObjId);
		final ItemInstance itemOne = request.getItemOne();
		final ItemInstance itemTwo = request.getItemTwo();
		if ((itemOne == null) || (itemTwo == null))
		{
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		if (!BlessItemData.getInstance().canBeBlessed(itemTwo))
		{
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		// Lets prevent using same item twice. Also stackable item check.
		if ((itemOne.getObjectId() == itemTwo.getObjectId()) && (!itemOne.isStackable() || (player.getInventory().getInventoryItemCount(itemOne.getItem().getId(), -1) < 2)))
		{
			player.sendPacket(new ExBlessOptionPutItem(false));
			return;
		}
		player.sendPacket(new ExBlessOptionPutItem(true));
	}
}