/*
 * This file is part of the L2J Mobius project.
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
 * General Public License for more details.
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
package club.projectessence.gameserver.network.serverpackets;

import club.projectessence.commons.network.ServerPacketId;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData.FeeHolder;
import club.projectessence.gameserver.data.xml.ResurrectionFeesData.ResurrectionFeeItem;
import club.projectessence.gameserver.instancemanager.CastleManager;
import club.projectessence.gameserver.instancemanager.FortManager;
import club.projectessence.gameserver.instancemanager.PremiumManager;
import club.projectessence.gameserver.model.SiegeClan;
import club.projectessence.gameserver.model.actor.Creature;
import club.projectessence.gameserver.model.actor.instance.PlayerInstance;
import club.projectessence.gameserver.model.clan.Clan;
import club.projectessence.gameserver.model.siege.Castle;
import club.projectessence.gameserver.model.siege.Fort;
import club.projectessence.gameserver.model.stats.Stat;

import club.projectessence.gameserver.model.variables.PlayerVariables;
import club.projectessence.gameserver.network.GameClient;
import gabriel.eventEngine.interf.GabrielEvents;
import io.github.joealisson.mmocore.WritableBuffer;

/**
 * <AUTHOR> Benetis
 */
public class Die extends ServerPacket
{
	private final Creature	_creature;
	@SuppressWarnings("unused")
	private final boolean	_isSweepable;
	private final boolean	_isDeathByPlayer;
	private int				_flags;
	
	public Die(Creature creature)
	{
		_creature = creature;
		_isSweepable = creature.isAttackable() && creature.isSweepActive();
		_isDeathByPlayer = creature.isPlayer() && creature.getActingPlayer().isLastDeathByPlayer();
		if (creature.isPlayer())
		{
			final Clan clan = creature.getActingPlayer().getClan();
			boolean isInCastleDefense = false;
			boolean isInFortDefense = false;
			SiegeClan siegeClan = null;
			final Castle castle = CastleManager.getInstance().getCastle(creature);
			final Fort fort = FortManager.getInstance().getFort(creature);
			if ((castle != null) && castle.getSiege().isInProgress())
			{
				siegeClan = castle.getSiege().getAttackerClan(clan);
				isInCastleDefense = (siegeClan == null) && castle.getSiege().checkIsDefender(clan);
			}
			else if ((fort != null) && fort.getSiege().isInProgress())
			{
				siegeClan = fort.getSiege().getAttackerClan(clan);
				isInFortDefense = (siegeClan == null) && fort.getSiege().checkIsDefender(clan);
			}

			// Check if auto-resurrection is enabled and can be used
			// Include offline players in auto-resurrection check
			boolean hasAutoResurrection = false;
			try
			{
				final PlayerInstance player = creature.getActingPlayer();
				if (player != null && player.isOnline() && player.getClient() != null)
				{
					// Additional safety: only check if player has been online for at least 5 seconds
					if (System.currentTimeMillis() - player.getOnlineTime() > 5000)
					{
						hasAutoResurrection = club.projectessence.gameserver.instancemanager.AutoResurrectionManager.canAutoResurrect(player);
					}
				}
			}
			catch (Exception e)
			{
				// Ignore exceptions to prevent crashes
				hasAutoResurrection = false;
			}

			if (!GabrielEvents.isInEvent(creature.getActingPlayer()))
			{
				// If auto-resurrection is available, set flags to 0 to hide resurrection options
				// This prevents the dialog from showing and allows auto-resurrection to work seamlessly
				if (hasAutoResurrection)
				{
					_flags = 0; // Hide all resurrection options when auto-resurrection is active
				}
				else
				{
					_flags = _creature.isPlayer() ? 1 : 0; // 0 - To village | 1 - To village + Resurrection with L-Coin
					// ClanHall check.
					if ((clan != null) && (clan.getHideoutId() > 0))
					{
						_flags += 2;
					}
					// Castle check.
					if (((clan != null) && (clan.getCastleId() > 0)) || isInCastleDefense)
					{
						_flags += 4;
					}
					// Fortress check.
					if (((clan != null) && (clan.getFortId() > 0)) || isInFortDefense)
					{
						_flags += 8;
					}
					// Outpost check.
					if (((siegeClan != null) && !isInCastleDefense && !isInFortDefense && !siegeClan.getFlag().isEmpty()))
					{
						_flags += 16;
					}
					// Feather check.
					if (creature.getAccessLevel().allowFixedRes() || creature.getInventory().haveItemForSelfResurrection())
					{
						_flags += 32;
					}
				}
			}
		}
	}
	
	@Override
	public void writeImpl(GameClient client, WritableBuffer buffer)
	{
		writeId(ServerPacketId.DIE, buffer);
		buffer.writeInt(_creature.getObjectId());
		buffer.writeByte(_flags);
		buffer.writeInt(0x02); // ?
		buffer.writeByte(0x00); // ?
		buffer.writeByte(0x00); // ?
		buffer.writeByte(0x00); // ?
		buffer.writeInt(_isDeathByPlayer ? 2 : _isSweepable ? 1 : 0);
		buffer.writeInt(0x00); // ?
		buffer.writeInt(_creature.isPlayer() ? 0x02 : 0x00); // ? (off 0 sometimes)
		buffer.writeByte(0x00); // ?
		if (_creature.isPlayer())
		{
			PlayerInstance player = _creature.getActingPlayer();

			// Check if auto-resurrection is enabled and can be used
			// Include offline players in auto-resurrection check
			boolean hasAutoResurrection = false;
			try
			{
				if (player != null && player.isOnline() && player.getClient() != null)
				{
					// Additional safety: only check if player has been online for at least 5 seconds
					if (System.currentTimeMillis() - player.getOnlineTime() > 5000)
					{
						hasAutoResurrection = club.projectessence.gameserver.instancemanager.AutoResurrectionManager.canAutoResurrect(player);
					}
				}
			}
			catch (Exception e)
			{
				// Ignore exceptions to prevent crashes
				hasAutoResurrection = false;
			}

			if (hasAutoResurrection)
			{
				// When auto-resurrection is active, send minimal data to prevent dialog display
				buffer.writeInt(0); // remaining free resurrections
				buffer.writeInt(0); // adena cost
				buffer.writeInt(0); // adena recovery
				buffer.writeInt(0); // lcoin cost
				buffer.writeInt(0); // lcoin recovery
			}
			else
			{
				// Normal resurrection dialog data
				buffer.writeInt(_creature.getActingPlayer().getVariables().getInt(PlayerVariables.RESURRECTION_COUNT_FREE, 2)); // remaining free resurections
				FeeHolder adenaFee = ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeeItem.ADENA);
				FeeHolder lcoinFee = ResurrectionFeesData.getInstance().getResurrectionFee(player, ResurrectionFeeItem.LCOIN);
				double premiumMod = PremiumManager.getInstance().hasAnyPremiumBenefits(player) ? PremiumManager.getInstance().getEffectivePremiumRate(player, "RESURRECTION_COST") : 1.0;
				buffer.writeInt((int) (adenaFee != null ? adenaFee.getCost() * Math.max(0, player.getStat().getValue(Stat.RESURRECTION_FEE_MOD, 1)) * premiumMod : 0x00));
				buffer.writeInt(adenaFee != null ? adenaFee.getRecovery() : 100);
				buffer.writeInt((int) (lcoinFee != null ? lcoinFee.getCost() * Math.max(0, player.getStat().getValue(Stat.RESURRECTION_FEE_MOD, 1)) * premiumMod : 0x00));
				buffer.writeInt(lcoinFee != null ? lcoinFee.getRecovery() : 100);
			}
		}
		else
		{
			buffer.writeInt(0x00);
			buffer.writeInt(0x00);
		}
	}
}
